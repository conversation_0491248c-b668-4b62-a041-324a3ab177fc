<svg width="107" height="106" viewBox="0 0 107 106" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_647_10115)">
<rect x="16.5" y="16" width="74" height="74" rx="37" fill="#0A3D91"/>
<g filter="url(#filter1_i_647_10115)">
<circle cx="54" cy="53" r="24" fill="#3262B1"/>
</g>
<g clip-path="url(#clip0_647_10115)">
<path d="M49 65H51V63H49V65ZM53 65H55V63H53V65ZM55 43H53V53H55V43ZM58.56 45.44L57.11 46.89C58.84 47.94 60 49.83 60 52C60 55.31 57.31 58 54 58C50.69 58 48 55.31 48 52C48 49.83 49.16 47.94 50.88 46.88L49.44 45.44C47.36 46.88 46 49.28 46 52C46 56.42 49.58 60 54 60C58.42 60 62 56.42 62 52C62 49.28 60.64 46.88 58.56 45.44ZM57 65H59V63H57V65Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_647_10115" x="0.5" y="0" width="106" height="106" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_647_10115"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_647_10115" result="shape"/>
</filter>
<filter id="filter1_i_647_10115" x="30" y="29" width="48" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_647_10115"/>
</filter>
<clipPath id="clip0_647_10115">
<rect width="24" height="24" fill="white" transform="translate(42 41)"/>
</clipPath>
</defs>
</svg>
