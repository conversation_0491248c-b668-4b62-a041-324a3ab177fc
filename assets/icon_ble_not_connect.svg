<svg width="106" height="106" viewBox="0 0 106 106" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_647_10010)">
<rect x="16" y="16" width="74" height="74" rx="37" fill="#FF5630"/>
<g filter="url(#filter1_i_647_10010)">
<circle cx="53.5" cy="53" r="24" fill="#FFAC82"/>
</g>
<g clip-path="url(#clip0_647_10010)">
<path d="M48.5 65H50.5V63H48.5V65ZM52.5 65H54.5V63H52.5V65ZM54.5 43H52.5V53H54.5V43ZM58.06 45.44L56.61 46.89C58.34 47.94 59.5 49.83 59.5 52C59.5 55.31 56.81 58 53.5 58C50.19 58 47.5 55.31 47.5 52C47.5 49.83 48.66 47.94 50.38 46.88L48.94 45.44C46.86 46.88 45.5 49.28 45.5 52C45.5 56.42 49.08 60 53.5 60C57.92 60 61.5 56.42 61.5 52C61.5 49.28 60.14 46.88 58.06 45.44ZM56.5 65H58.5V63H56.5V65Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_647_10010" x="0" y="0" width="106" height="106" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_647_10010"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_647_10010" result="shape"/>
</filter>
<filter id="filter1_i_647_10010" x="29.5" y="29" width="48" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_647_10010"/>
</filter>
<clipPath id="clip0_647_10010">
<rect width="24" height="24" fill="white" transform="translate(41.5 41)"/>
</clipPath>
</defs>
</svg>
