<svg width="88" height="96" viewBox="0 0 88 96" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd_647_8721)">
<rect x="20" y="8" width="56" height="56" rx="28" fill="#FFB92A"/>
<rect x="20.5" y="8.5" width="55" height="55" rx="27.5" stroke="#FBA900"/>
<g clip-path="url(#clip0_647_8721)">
<path d="M57.3337 37.3332H49.3337V45.3332H46.667V37.3332H38.667V34.6665H46.667V26.6665H49.3337V34.6665H57.3337V37.3332Z" fill="#212B36"/>
</g>
</g>
<defs>
<filter id="filter0_dd_647_8721" x="0" y="0" width="96" height="96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect1_dropShadow_647_8721"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.568627 0 0 0 0 0.619608 0 0 0 0 0.670588 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_647_8721"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.568627 0 0 0 0 0.619608 0 0 0 0 0.670588 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_647_8721" result="effect2_dropShadow_647_8721"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_647_8721" result="shape"/>
</filter>
<clipPath id="clip0_647_8721">
<rect width="32" height="32" fill="white" transform="translate(32 20)"/>
</clipPath>
</defs>
</svg>
