# Badge Sync Feature - iOS Only

## Tổng quan
Tính năng đồng bộ Badge (số thông báo chưa đọc) được thiết kế để hoạt động chỉ trên iOS platform, tự động cập nhật số badge trên app icon dựa trên API `fetchUnseenCount`.

## Quy tắc hoạt động

### 1. App đang đóng/terminated
- Khi nhận được FCM notification và app có thể chạy nền → gọi API `fetchUnseenCount` và update Badge
- Nếu không thể chạy nền → đợi đến khi app mở lại → gọi API ngay lúc start để đồng bộ Badge

### 2. App đang mở (foreground)
- Khi nhận được FCM notification → **KHÔNG** update Badge ngoài icon
- Badge chỉ được update khi app ở background/terminated

### 3. Platform restriction
- Tính năng chỉ hoạt động trên iOS
- Trên Android và các platform khác sẽ bị bỏ qua

## Cấu trúc code

### BadgeService (`lib/core/service/badge_service.dart`)
- `updateBadge(int unreadCount)`: Cập nhật badge với số thông báo chưa đọc
- `resetBadge()`: Reset badge về 0
- `isIOSPlatform`: Kiểm tra platform có phải iOS không

### PushNotificationService Updates
- `syncBadgeOnAppStart()`: Đồng bộ badge khi app khởi động
- `_updateUnseenCount()`: Cập nhật unseen count và sync badge theo quy tắc
- Logic phân biệt foreground/background để quyết định có update badge hay không

### NotificationProvider Updates
- Tự động sync badge mỗi khi `unseenCount` thay đổi
- Chỉ sync khi có sự thay đổi thực sự trong số lượng

## Cách sử dụng

### Automatic Sync
Tính năng hoạt động tự động, không cần can thiệp thủ công:

1. **App start**: Badge được đồng bộ tự động khi app khởi động
2. **Notification received**: Badge được cập nhật khi nhận notification (chỉ khi app không ở foreground)
3. **Mark as read**: Badge được cập nhật khi đánh dấu notification đã đọc

### Manual Control (nếu cần)
```dart
// Update badge manually
await BadgeService.updateBadge(5);

// Reset badge
await BadgeService.resetBadge();

// Check if iOS platform
if (BadgeService.isIOSPlatform) {
  // iOS specific code
}
```

## Testing
Chạy test để kiểm tra tính năng:
```bash
flutter test test/badge_service_test.dart
```

## Lưu ý quan trọng
1. Tính năng chỉ hoạt động trên iOS
2. Badge không được update khi app đang ở foreground
3. API `fetchUnseenCount` được gọi tự động, không cần can thiệp thủ công
4. Badge được đồng bộ ngay khi app khởi động từ trạng thái terminated
