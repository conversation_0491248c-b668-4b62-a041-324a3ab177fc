# iOS Background Worker Implementation

## Overview

This implementation adds iOS background processing capabilities to handle API calls for push notification badge synchronization when the app is running in the background. The solution uses the `workmanager` package to enable background task execution on iOS.

## Key Components

### 1. BackgroundWorkerService (`lib/core/service/background_worker_service.dart`)

A dedicated service that handles background processing:

- **Purpose**: Manages background tasks for badge synchronization
- **Key Features**:
  - Standalone HTTP client (no Flutter context dependencies)
  - Direct API calls to `/notifications/un-seen` endpoint
  - Badge updates using AwesomeNotifications
  - iOS-specific background task registration

### 2. Updated PushNotificationService

Enhanced to use the background worker:

- **Firebase Background Handler**: Now uses `BackgroundWorkerService.registerBadgeSyncTask()` for iOS
- **Initialization**: Includes background worker service initialization
- **Platform-specific Logic**: iOS uses background tasks, Android uses regular sync

### 3. iOS Configuration Updates

#### AppDelegate.swift
- Added workmanager import
- Configured background app refresh
- Added background fetch handler

#### Info.plist
- Added `background-processing` to UIBackgroundModes
- Maintains existing background modes (fetch, remote-notification, etc.)

### 4. Main App Integration

#### main.dart
- Initializes background worker service in `mainRoot()`
- Registers background tasks on app lifecycle changes
- Triggers background sync when app goes to background/inactive

## How It Works

### Background Task Flow

1. **App Goes to Background**: When app state changes to `paused` or `inactive`
2. **Task Registration**: `BackgroundWorkerService.registerBadgeSyncTask()` is called
3. **Background Execution**: iOS executes the task in a separate isolate
4. **API Call**: Standalone HTTP client fetches unseen notification count
5. **Badge Update**: AwesomeNotifications updates the app badge
6. **Completion**: Task completes and iOS manages the background execution

### Key Advantages

- **No Flutter Context Required**: Background tasks run independently
- **Reliable API Calls**: Uses standalone Dio client with proper authentication
- **iOS-Optimized**: Leverages iOS background processing capabilities
- **Automatic Execution**: iOS manages when and how background tasks run
- **Battery Efficient**: Uses iOS's built-in background task management

## API Integration

### Endpoint Used
- **URL**: `GET /notifications/un-seen`
- **Authentication**: Bearer token from AppPreferences
- **Response**: `{ "data": <unseen_count> }`

### HTTP Client Configuration
```dart
final dio = Dio(BaseOptions(
  baseUrl: F.baseURL, // Uses flavor-specific base URL
  connectTimeout: const Duration(seconds: 30),
  receiveTimeout: const Duration(seconds: 30),
  sendTimeout: const Duration(seconds: 30),
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $token',
  },
));
```

## Usage Examples

### Manual Background Task Registration
```dart
// Register a background task manually
await BackgroundWorkerService.registerBadgeSyncTask();
```

### Cancel All Background Tasks
```dart
// Cancel all registered background tasks
await BackgroundWorkerService.cancelAllTasks();
```

### Direct Background Sync (for testing)
```dart
// Perform background sync directly
await BackgroundWorkerService.syncBadgeInBackground();
```

## Dependencies Added

- **workmanager**: `^0.7.0` - Provides background task execution capabilities

## iOS Background Modes

The app now supports these background modes:
- `fetch` - Background app refresh
- `remote-notification` - Push notifications
- `bluetooth-peripheral` - Bluetooth peripheral mode
- `bluetooth-central` - Bluetooth central mode
- `background-processing` - **NEW** - Background task processing

## Testing

### Verification Steps

1. **Build Test**: Ensure the app builds successfully for iOS
2. **Background Transition**: Test app going to background triggers task registration
3. **Badge Updates**: Verify badges update when notifications are received in background
4. **API Calls**: Confirm background tasks make successful API calls
5. **Battery Impact**: Monitor battery usage during background processing

### Debug Logging

The implementation includes comprehensive logging:
- Task registration events
- Background execution status
- API call results
- Badge update confirmations
- Error handling and reporting

## Limitations and Considerations

### iOS Background Execution Limits
- iOS controls when background tasks actually execute
- Tasks may be delayed or throttled based on device usage patterns
- Background execution time is limited (typically 30 seconds)

### Network Requirements
- Background tasks require active network connection
- Failed API calls are logged but don't retry automatically
- Authentication token must be valid during background execution

### Battery Optimization
- iOS may limit background execution for battery optimization
- Users can disable background app refresh in iOS settings
- Background tasks are subject to iOS's intelligent scheduling

## Future Enhancements

1. **Retry Logic**: Add automatic retry for failed background API calls
2. **Batch Processing**: Handle multiple notification updates in single background task
3. **Analytics**: Track background task execution success rates
4. **Fallback Mechanisms**: Alternative sync methods when background tasks are unavailable

## Troubleshooting

### Common Issues

1. **Background Tasks Not Executing**
   - Check iOS background app refresh settings
   - Verify app has sufficient background execution budget
   - Ensure device is not in low power mode

2. **API Calls Failing**
   - Verify authentication token is valid
   - Check network connectivity during background execution
   - Review API endpoint availability

3. **Badge Not Updating**
   - Confirm AwesomeNotifications permissions
   - Check iOS notification settings
   - Verify badge update logic in background context

### Debug Commands

```bash
# Check background task registration
flutter logs --verbose

# Monitor background execution
# Use Xcode console to view background task logs

# Test API connectivity
# Use network debugging tools during background execution
```
