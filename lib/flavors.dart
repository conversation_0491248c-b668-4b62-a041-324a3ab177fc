import 'package:firebase_core/firebase_core.dart';
import 'firebase_options_dev.dart' as dev;
import 'firebase_options_prod.dart' as prod;

enum Flavor {
  dev,
  prod,
}

class F {
  static Flavor? appFlavor;

  static String get name => appFlavor?.name ?? '';

  static String get title {
    switch (appFlavor) {
      case Flavor.dev:
        return 'Kitemite Dev';
      case Flavor.prod:
        return 'Kitemite';
      default:
        return 'title';
    }
  }
  static String get baseURL {
    switch (appFlavor) {
      case Flavor.dev:
        return "https://kitemite-api.mystg-env.com/api";
      case Flavor.prod:
        return "https://api.ktmt.shop/api";
      default:
        return "https://kitemite-api.mystg-env.com/api";
    }
  }
    static FirebaseOptions get firebaseOptions {
    switch (appFlavor) {
      case Flavor.dev:
        return dev.DefaultFirebaseOptions.currentPlatform;
      case Flavor.prod:
        return prod.DefaultFirebaseOptions.currentPlatform;
      default:
        return dev.DefaultFirebaseOptions.currentPlatform;
    }
  }
}
