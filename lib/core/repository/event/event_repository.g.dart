// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getEventRepositoryHash() =>
    r'7bcd07d87701b32f1d91c08c75b1fc9bba52d86e';

/// See also [getEventRepository].
@ProviderFor(getEventRepository)
final getEventRepositoryProvider =
    AutoDisposeProvider<EventRepository>.internal(
  getEventRepository,
  name: r'getEventRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getEventRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetEventRepositoryRef = AutoDisposeProviderRef<EventRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
