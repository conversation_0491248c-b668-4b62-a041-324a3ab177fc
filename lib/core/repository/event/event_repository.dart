import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/core/failure/failure_mapper.dart';
import 'package:kitemite_app/core/service/event_service.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/request/event/create_comment_request.dart';
import 'package:kitemite_app/model/request/event/create_event_request.dart';
import 'package:kitemite_app/model/response/event/event_comment_model.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'event_repository.g.dart';

@riverpod
EventRepository getEventRepository(Ref ref) {
  final apiService = ref.read(apiEventServiceProvider);
  return EventRepositoryImpl(apiService: apiService);
}

abstract class EventRepository {
  Future<BaseListResponse<EventDetailModel>> getListEvents(
    int page,
    int perPage,
    String? search,
    String? tags,
  );
  Future<void> createEvent({required CreateEventRequest request});
  Future<BaseResponse<EventDetailModel>> getEventDetail({required int id});
  Future<BaseListResponse<EventCommentModel>> getEventComments({
    required int eventId,
    required int page,
    required int perPage,
  });
  Future<void> createComment({
    required CreateCommentRequest request,
  });
  Future<void> updateEvent({
    required int id,
    required CreateEventRequest request,
  });
}

class EventRepositoryImpl implements EventRepository {
  final EventService _apiService;

  EventRepositoryImpl({required EventService apiService})
      : _apiService = apiService;

  @override
  Future<BaseListResponse<EventDetailModel>> getListEvents(
    int page,
    int perPage,
    String? search,
    String? tags,
  ) async {
    try {
      final response =
          await _apiService.getListEvents(page, perPage, search, tags);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<void> createEvent({required CreateEventRequest request}) async {
    try {
      final response = await _apiService.createEvent(request);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<BaseResponse<EventDetailModel>> getEventDetail(
      {required int id}) async {
    try {
      final response = await _apiService.getEventDetail(id);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<BaseListResponse<EventCommentModel>> getEventComments({
    required int eventId,
    required int page,
    required int perPage,
  }) async {
    try {
      final response = await _apiService.getEventComments(
        eventId,
        page,
        perPage,
      );
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<void> createComment({
    required CreateCommentRequest request,
  }) async {
    try {
      final response = await _apiService.createComment(request);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<void> updateEvent(
      {required int id, required CreateEventRequest request}) async {
    try {
      final response = await _apiService.updateEvent(id, request);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }
}
