// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getNotificationRepositoryHash() =>
    r'7319f004c097864e5c3aa372f3e40d5d6b7d9fa9';

/// See also [getNotificationRepository].
@ProviderFor(getNotificationRepository)
final getNotificationRepositoryProvider =
    AutoDisposeProvider<NotificationRepository>.internal(
  getNotificationRepository,
  name: r'getNotificationRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getNotificationRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetNotificationRepositoryRef
    = AutoDisposeProviderRef<NotificationRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
