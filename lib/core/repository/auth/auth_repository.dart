import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/core/failure/failure_mapper.dart';
import 'package:kitemite_app/core/service/auth_service.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/request/auth/user_request.dart';
import 'package:kitemite_app/model/response/auth/image_url_model.dart';
import 'package:kitemite_app/model/response/auth/link_card_model.dart';
import 'package:kitemite_app/model/response/auth/payment_info_model.dart';
import 'package:kitemite_app/model/response/auth/token_model.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';
import 'package:kitemite_app/model/response/location/province_model.dart';
import 'package:kitemite_app/model/response/version_app/version_app_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_repository.g.dart';

@riverpod
AuthRepository getAuthRepository(Ref ref) {
  final apiService = ref.read(apiAuthServiceProvider);
  return AuthRepositoryImpl(apiService: apiService);
}

abstract class AuthRepository {
  Future<dynamic> login({required String email});
  Future<void> resentOTP({required String email});
  Future<BaseResponse<TokenModel>> verifyOTP(
      {required String email, required String otp});
  Future<BaseResponse<UserInfoModel>> getMe();
  Future<void> updateUser({required int userID, required UserRequest request});
  Future<VersionAppModel> checkVersionApp();
  Future<void> logout({required String deviceID});
  Future<void> destroyUser({required int id});
  Future<void> storeDevice(
      {required int userID,
      required String platform,
      required String deviceToken,
      required String deviceId});
  Future<ImageUrlModel> upLoadFile({required String base64});
  Future<ImageUrlModel> upListFileImage({required List<String> base64});
  Future<BaseResponse<LinkCardModel>> generateLinkAddCard();
  Future<BaseListResponse<PaymentInfoModel>> getPaymentInformation(
      {required int userId});
  Future<ProvinceListResponse> getProvinces();
  Future<void> changeDefaultCard({required int userId, required int cardId});
  Future<void> deleteCard({required int userId, required int cardId});

}

class AuthRepositoryImpl implements AuthRepository {
  final AuthService _apiService;
  AuthRepositoryImpl({required AuthService apiService})
      : _apiService = apiService;

  @override
  Future<BaseResponse<UserInfoModel>> getMe() async {
    try {
      final response = await _apiService.getInfo();
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<UserInfoModel>(data: null);
    }
  }

  @override
  Future<void> login({required String email}) async {
    try {
      final request = {"email": email};
      final response = await _apiService.login(request);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<dynamic> resentOTP({required String email}) async {
    try {
      final request = {"email": email};
      final response = await _apiService.resentOTP(request);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<void> updateUser(
      {required int userID, required UserRequest request}) async {
    try {
      final response = await _apiService.updateUser(userID, request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
    }
  }

  @override
  Future<BaseResponse<TokenModel>> verifyOTP(
      {required String email, required String otp}) async {
    try {
      final request = {"email": email, "otp": otp};
      final response = await _apiService.verifyOTP(request);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<VersionAppModel> checkVersionApp() async {
    try {
      final response = await _apiService.checkVersionApp();
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<void> logout({required String deviceID}) async {
    try {
      final request = {"device_id": deviceID};
      final response = await _apiService.logout(request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
    }
  }

  @override
  Future<void> destroyUser({required int id}) async {
    try {
      final response = await _apiService.destroyUser(id);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
    }
  }

  @override
  Future<void> storeDevice(
      {required int userID,
      required String platform,
      required String deviceToken,
      required String deviceId}) async {
    try {
      final request = {
        "platform": platform,
        "device_token": deviceToken,
        "device_id": deviceId
      };
      final response = await _apiService.storeDevice(userID, request);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<ImageUrlModel> upLoadFile({required String base64}) async {
    try {
      final request = {
        "files": [base64]
      };
      final response = await _apiService.upLoadFile(request);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<BaseResponse<LinkCardModel>> generateLinkAddCard() async {
    try {
      final response = await _apiService.generateLinkAddCard();
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<BaseListResponse<PaymentInfoModel>> getPaymentInformation(
      {required int userId}) async {
    try {
      final response = await _apiService.getPaymentInformation(userId);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<ProvinceListResponse> getProvinces() async {
    try {
      final response = await _apiService.getProvinces();
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }

  @override
  Future<void> changeDefaultCard(
      {required int userId, required int cardId}) async {
    try {
      await _apiService.changeDefaultCard(userId, cardId);
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
    }
  }

  @override
  Future<ImageUrlModel> upListFileImage({required List<String> base64}) async {
    try {
      final request = {"files": base64};
      final response = await _apiService.upLoadFile(request);
      return response;
    } on Exception catch (e) {
      throw FailureMapper.getFailures(e);
    }
  }
  
  @override
  Future<void> deleteCard({required int userId, required int cardId}) async {
    try {
      await _apiService.deleteCard(userId, cardId);
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
    }
  }
}
