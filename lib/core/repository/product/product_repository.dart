import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/core/failure/failure_mapper.dart';
import 'package:kitemite_app/core/service/product_service.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/request/order/create_order_request.dart';
import 'package:kitemite_app/model/request/product/register_product_request.dart';
import 'package:kitemite_app/model/response/order/create_order_response.dart';
import 'package:kitemite_app/model/response/order/draft_order_response.dart';
import 'package:kitemite_app/model/response/product/check_cart_model.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'product_repository.g.dart';

@riverpod
ProductRepository getProductRepository(Ref ref) {
  final apiService = ref.read(apiProductServiceProvider);
  return ProductRepositoryImpl(apiService: apiService);
}

abstract class ProductRepository {
  Future<BaseListResponse<ProductModel>> getProducts(
    int page,
    int perPage,
    String? search,
    double? latitude,
    double? longitude,
  );
  Future<BaseResponse<ProductModel>> getProductDetail(int id);

  Future<void> reportProduct(int id, String reason);

  Future<void> registerProduct(RegisterProductRequest request);

  Future<void> updateProduct(int id, RegisterProductRequest request);

  Future<BaseResponse<DraftOrderResponse>> createDraftOrder(
      List<int> productIds);

  Future<BaseResponse<CreateOrderData>> createOrder(
      int userId, CreateOrderRequest request);

  Future<void> deleteProduct(int id);
  Future<BaseListResponse<CheckCartModel>> checkCart(
    int productIds,
  );

  Future<BaseResponse<CreateOrderData>> getOrderDetail(
      int userId, int orderId);
}

class ProductRepositoryImpl implements ProductRepository {
  final ProductService _apiService;

  ProductRepositoryImpl({required ProductService apiService})
      : _apiService = apiService;

  @override
  Future<BaseListResponse<ProductModel>> getProducts(
    int page,
    int perPage,
    String? search,
    double? latitude,
    double? longitude,
  ) async {
    try {
      final response = await _apiService.getProducts(
          page, perPage, search, latitude, longitude);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseListResponse<ProductModel>(
        data: [],
        total: 0,
        currentPage: 1,
        perPage: 10,
      );
    }
  }

  @override
  Future<BaseResponse<ProductModel>> getProductDetail(int id) async {
    try {
      final response = await _apiService.getProductDetail(id);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<ProductModel>(data: null);
    }
  }

  @override
  Future<void> reportProduct(int id, String reason) async {
    try {
      final request = {
        'message': reason,
      };
      await _apiService.reportProduct(id, request);
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<void> registerProduct(RegisterProductRequest request) async {
    try {
      final response = await _apiService.registerProduct(request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<void> updateProduct(int id, RegisterProductRequest request) async {
    try {
      final response = await _apiService.updateProduct(id, request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<BaseResponse<DraftOrderResponse>> createDraftOrder(
      List<int> productIds) async {
    try {
      final request = {
        'product_ids': productIds.join(','),
      };
      final response = await _apiService.createDraftOrder(request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<DraftOrderResponse>(data: null);
    }
  }

  @override
  Future<BaseResponse<CreateOrderData>> createOrder(
      int userId, CreateOrderRequest request) async {
    try {
      final response = await _apiService.createOrder(userId, request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<CreateOrderData>(data: null);
    }
  }

  @override
  Future<void> deleteProduct(int id) async {
    try {
      await _apiService.deleteProduct(id);
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }
  
  @override
  Future<BaseListResponse<CheckCartModel>> checkCart(int productIds) async {
    try {
      final response = await _apiService.checkCart(productIds);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
    }
    return BaseListResponse<CheckCartModel>(
      data: [],
      total: 0,
      currentPage: 1,
      perPage: 10,
    );
  }
  
  @override
  Future<BaseResponse<CreateOrderData>> getOrderDetail(int userId, int orderId) {
    try {
      final response = _apiService.getOrderDetail(userId, orderId);
      return response;
    } on Exception catch (e) {
        throw FailureMapper.getFailures(e);
    }
  }
}
