// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getProductRepositoryHash() =>
    r'd330ba334829e03ceb7218f251abcb42688508ce';

/// See also [getProductRepository].
@ProviderFor(getProductRepository)
final getProductRepositoryProvider =
    AutoDisposeProvider<ProductRepository>.internal(
  getProductRepository,
  name: r'getProductRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getProductRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetProductRepositoryRef = AutoDisposeProviderRef<ProductRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
