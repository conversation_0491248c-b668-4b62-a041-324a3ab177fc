import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/core/failure/failure_mapper.dart';
import 'package:kitemite_app/core/service/store_service.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/request/store/register_seller_request.dart';
import 'package:kitemite_app/model/request/store/template_request.dart';
import 'package:kitemite_app/model/request/store/update_template_request.dart';
import 'package:kitemite_app/model/response/store/nearby_store_model.dart';
import 'package:kitemite_app/model/response/store/sales_history_model.dart';
import 'package:kitemite_app/model/response/store/store_detail_model.dart';
import 'package:kitemite_app/model/response/store/store_model.dart';
import 'package:kitemite_app/model/response/store/template_model.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'store_repository.g.dart';

@riverpod
StoreRepository getStoreRepository(Ref ref) {
  final apiService = ref.read(apiStoreServiceProvider);
  return StoreRepositoryImpl(apiService: apiService);
}

abstract class StoreRepository {
  Future<BaseResponse<StoreModel>> getDetailStore(int userId, int storeId);

  Future<void> updateStore(
    int userId,
    int storeId,
    RegisterSellerRequest request,
  );

  Future<void> userRegisterSeller(
    int userId,
    RegisterSellerRequest request,
  );

  Future<BaseListResponse<NearbyStoreModel>> getNearbyStores(
    double? latitude,
    double? longitude,
    String? name,
    String? open,
    String? close,
    String? province,
    String? role,
  );

  Future<BaseListResponse<WarehouseModel>> getListStoreSeller();

  Future<BaseResponse<StoreDetailModel>> getStoreDetail(int id, String role);
  Future<BaseResponse<WarehouseModel>> getStoreDetailWarehouse(int id);
  Future<BaseListResponse<WarehouseModel>> getListProductWarehouse();
  Future<BaseListResponse<TemplateModel>> getListTemplateWarehouse();
  Future<void> deleteTemplateWarehouse(int id);
  Future<BaseResponse<TemplateModel>> getDetailTemplateWarehouse(int id);
  Future<void> updateTemplateWarehouse(int id, UpdateTemplateRequest request);
  Future<void> createTemplateWarehouse(TemplateRequest request);
  Future<void> bulkUpdate(WarehousesBulkUpdateRequest request);

  Future<BaseResponse<StoreDetailModel>> getStoreDetailGuest(int id);

  Future<BaseListResponse<NearbyStoreModel>> getNearbyStoresGuest(
    double? latitude,
    double? longitude,
  );

  Future<BaseListResponse<SalesHistoryModel>> getSellerOrdersHistories(
    int page,
   
  );
}

class StoreRepositoryImpl implements StoreRepository {
  final StoreService _apiService;

  StoreRepositoryImpl({required StoreService apiService})
      : _apiService = apiService;

  @override
  Future<BaseResponse<StoreModel>> getDetailStore(
      int userId, int storeId) async {
    try {
      final response = await _apiService.getDetailStore(userId, storeId);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<StoreModel>(data: null);
    }
  }

  @override
  Future<void> updateStore(
      int userId, int storeId, RegisterSellerRequest request) async {
    try {
      final response = await _apiService.updateStore(userId, storeId, request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<void> userRegisterSeller(
      int userId, RegisterSellerRequest request) async {
    try {
      final response = await _apiService.userRegisterSeller(userId, request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<BaseListResponse<NearbyStoreModel>> getNearbyStores(
    double? latitude,
    double? longitude,
    String? name,
    String? open,
    String? close,
    String? province,
    String? role,
  ) async {
    try {
      final response = await _apiService.getNearbyStores(
          latitude, longitude, name, open, close, province, role);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseListResponse<NearbyStoreModel>(
        data: [],
        total: 0,
        currentPage: 1,
        perPage: 10,
      );
    }
  }

  @override
  Future<BaseListResponse<WarehouseModel>> getListStoreSeller() async {
    try {
      final response = await _apiService.getListStoreSeller();
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseListResponse<WarehouseModel>(
        data: [],
        total: 0,
        currentPage: 1,
        perPage: 10,
      );
    }
  }

  @override
  Future<BaseResponse<StoreDetailModel>> getStoreDetail(
      int id, String role) async {
    try {
      final response = await _apiService.getStoreDetail(id, role);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<StoreDetailModel>(data: null);
    }
  }

  @override
  Future<BaseResponse<WarehouseModel>> getStoreDetailWarehouse(int id) async {
    try {
      final response = await _apiService.getStoreDetailWarehouse(id);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<WarehouseModel>(data: null);
    }
  }

  @override
  Future<BaseListResponse<WarehouseModel>> getListProductWarehouse() async {
    try {
      final response = await _apiService.getListProductWarehouse();
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseListResponse<WarehouseModel>(
        data: [],
        total: 0,
        currentPage: 1,
        perPage: 10,
      );
    }
  }

  @override
  Future<BaseListResponse<TemplateModel>> getListTemplateWarehouse() async {
    try {
      final response = await _apiService.getListTemplateWarehouse();
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseListResponse<TemplateModel>(
        data: [],
        total: 0,
        currentPage: 1,
        perPage: 10,
      );
    }
  }

  @override
  Future<void> deleteTemplateWarehouse(int id) async {
    try {
      final response = await _apiService.deleteTemplateWarehouse(id);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<BaseResponse<TemplateModel>> getDetailTemplateWarehouse(int id) async {
    try {
      final response = await _apiService.getDetailTemplateWarehouse(id);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<TemplateModel>(data: null);
    }
  }

  @override
  Future<void> updateTemplateWarehouse(
      int id, UpdateTemplateRequest request) async {
    try {
      final response = await _apiService.updateTemplateWarehouse(id, request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<void> createTemplateWarehouse(TemplateRequest request) async {
    try {
      final response = await _apiService.createTemplateWarehouse(request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<void> bulkUpdate(WarehousesBulkUpdateRequest request) async {
    try {
      final response = await _apiService.bulkUpdate(request);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<BaseListResponse<NearbyStoreModel>> getNearbyStoresGuest(
      double? latitude, double? longitude) async {
    try {
      final response = await _apiService.getNearbyStoresGuest(
        latitude,
        longitude,
      );
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseListResponse<NearbyStoreModel>(
        data: [],
        total: 0,
        currentPage: 1,
        perPage: 10,
      );
    }
  }

  @override
  Future<BaseResponse<StoreDetailModel>> getStoreDetailGuest(int id) async {
    try {
      final response = await _apiService.getStoreDetailGuest(id);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<StoreDetailModel>(data: null);
    }
  }

  @override
  Future<BaseListResponse<SalesHistoryModel>> getSellerOrdersHistories(
      int page,
  ) async {
    try {
      final response =
          await _apiService.getSellerOrdersHistories( page );
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseListResponse<SalesHistoryModel>(
        data: [],
        total: 0,
        currentPage: 1,
        perPage: 10,
      );
    }
  }
}
