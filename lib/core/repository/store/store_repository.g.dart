// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'store_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getStoreRepositoryHash() =>
    r'cf60d81027c101d57f57e5263dfbad53a0ff9483';

/// See also [getStoreRepository].
@ProviderFor(getStoreRepository)
final getStoreRepositoryProvider =
    AutoDisposeProvider<StoreRepository>.internal(
  getStoreRepository,
  name: r'getStoreRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getStoreRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetStoreRepositoryRef = AutoDisposeProviderRef<StoreRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
