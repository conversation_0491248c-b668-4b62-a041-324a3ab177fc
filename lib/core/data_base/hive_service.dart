import 'package:hive_ce/hive.dart';
import 'package:kitemite_app/core/data_base/hive_box_names.dart';

class HiveService {
  static Future<Box> openBox(String boxName) async {
    try {
      return await Hive.openBox(boxName);
    } catch (e) {
      print('Error opening Hive box $boxName: $e');
      // Try to delete and recreate the box
      await Hive.deleteBoxFromDisk(boxName);
      return await Hive.openBox(boxName);
    }
  }

  static Future<void> put(String boxName, String key, dynamic value) async {
    final box = await openBox(boxName);
    try {
      await box.put(key, value);
    } finally {
      await box.close();
    }
  }

  static Future<dynamic> get(String boxName, String key) async {
    final box = await openBox(boxName);
    try {
      return box.get(key);
    } finally {
      await box.close();
    }
  }

  static Future<void> delete(String boxName, String key) async {
    final box = await openBox(boxName);
    try {
      await box.delete(key);
    } finally {
      await box.close();
    }
  }

  static Future<void> clearBox(String boxName) async {
    final box = await openBox(boxName);
    try {
      await box.clear();
    } finally {
      await box.close();
    }
  }

  static Future<bool> containsKey(String boxName, String key) async {
    final box = await openBox(boxName);
    try {
      return box.containsKey(key);
    } finally {
      await box.close();
    }
  }

  static Future<List<dynamic>> getAll(String boxName) async {
    final box = await openBox(boxName);
    try {
      return box.values.toList();
    } finally {
      await box.close();
    }
  }

  static Future<void> addToCart(Map<String, dynamic> cartItem) async {
    final box = await openBox(HiveBoxNames.products);
    try {
      final cartItems = _getCartItemsFromBox(box);
      cartItems.add(cartItem);
      await box.put('cart_items', cartItems);
    } finally {
      await box.close();
    }
  }

  static Future<void> updateCart(List<dynamic> cartItems) async {
    final box = await openBox(HiveBoxNames.products);
    try {
      await box.put('cart_items', cartItems);
    } finally {
      await box.close();
    }
  }

  static Future<List<dynamic>> getCartItems() async {
    final box = await openBox(HiveBoxNames.products);
    try {
      return _getCartItemsFromBox(box);
    } finally {
      await box.close();
    }
  }

  static List<dynamic> _getCartItemsFromBox(Box box) {
    final rawItems = box.get('cart_items');
    if (rawItems == null) return [];

    // Convert to List<dynamic>
    final List<dynamic> items = rawItems is List ? rawItems : [];

    // Convert each item to Map<String, dynamic> if needed
    return items.map((item) {
      if (item is Map) {
        // Convert Map<dynamic, dynamic> to Map<String, dynamic>
        return Map<String, dynamic>.from(item);
      }
      return item;
    }).toList();
  }
}
