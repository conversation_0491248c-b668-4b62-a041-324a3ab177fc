import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:kitemite_app/core/data_base/hive_box_names.dart';
import 'package:kitemite_app/model/response/product/product_hive_model.dart';

class HiveConfig {
  static bool _isInitialized = false;

  static Future<void> init() async {
    if (_isInitialized) return;

    try {
      // Initialize Hive with Flutter
      await Hive.initFlutter();

      // Register adapters
      Hive.registerAdapter(ShelfHiveModelAdapter());
      Hive.registerAdapter(CabinetHiveModelAdapter());
      Hive.registerAdapter(ProductImageHiveModelAdapter());
      Hive.registerAdapter(ProductHiveModelAdapter());

      // Open boxes
      await Hive.openBox(HiveBoxNames.settings);
      await Hive.openBox(HiveBoxNames.user);
      await Hive.openBox(HiveBoxNames.cache);
      await Hive.openBox(HiveBoxNames.products);

      _isInitialized = true;
    } catch (e) {
      print('Error initializing Hive: $e');
      // Clear Hive and reinitialize
      await Hive.deleteFromDisk();
      await Hive.initFlutter();
      // Register adapters again
      Hive.registerAdapter(ShelfHiveModelAdapter());
      Hive.registerAdapter(CabinetHiveModelAdapter());
      Hive.registerAdapter(ProductImageHiveModelAdapter());
      Hive.registerAdapter(ProductHiveModelAdapter());
      // Open boxes again
      await Hive.openBox(HiveBoxNames.settings);
      await Hive.openBox(HiveBoxNames.user);
      await Hive.openBox(HiveBoxNames.cache);
      await Hive.openBox(HiveBoxNames.products);

      _isInitialized = true;
    }
  }

  static Future<void> close() async {
    if (!_isInitialized) return;

    try {
      await Hive.close();
      _isInitialized = false;
    } catch (e) {
      print('Error closing Hive: $e');
    }
  }
}
