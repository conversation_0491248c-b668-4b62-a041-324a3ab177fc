import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

extension GoRouterX on GoRouter {
  // Navigate back to a specific route
  popUntilPath(BuildContext context, String ancestorPath) {
    while (context.mounted &&
        routerDelegate.currentConfiguration.matches.last.matchedLocation !=
            ancestorPath) {
      if (!context.canPop()) {
        return;
      }
      context.pop();
    }
  }

  RouteMatchList getTopRoute() {
    final lastMatch = routerDelegate.currentConfiguration.last;
    return lastMatch is ImperativeRouteMatch
        ? lastMatch.matches
        : routerDelegate.currentConfiguration;
  }

  Uri getTopRouteUri() {
    final matchList = getTopRoute();
    return matchList.uri;
  }
}
