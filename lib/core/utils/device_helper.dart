import 'dart:io';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:uuid/uuid.dart';

class DeviceHelper {
  static final _storage = FlutterSecureStorage();
  static const _deviceIdKey = 'device_id';


  /// Lấy hoặc tạo `device_id`
  static Future<String> getOrCreateDeviceId() async {
    String? deviceId = await _storage.read(key: _deviceIdKey);

    if (deviceId == null) {
      deviceId = await _getDeviceInfoId(); 

      // Nếu ID không ổn định, tạo UUID
      if (deviceId == null || deviceId.isEmpty) {
        deviceId = const Uuid().v4();
      }

      // Lưu vào Secure Storage
      await _storage.write(key: _deviceIdKey, value: deviceId);
    }

    return deviceId;
  }

  
  static Future<String?> _getDeviceInfoId() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id; 
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor; 
    }

    return null;
  }
}
