import 'package:dio/dio.dart';

extension DioErrorExtension on DioException {
  int? get httpStatusCode => response?.statusCode;

  int? get httpErrorCode {
    try {
      final errorResponse = response?.data as Map<String, dynamic>?;
      if (errorResponse != null && errorResponse.containsKey("code")) {
        return errorResponse["code"] as int;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  String get httpErrorMessage {
    try {
      final errorResponse = response?.data as Map<String, dynamic>?;
      if (errorResponse != null && errorResponse.containsKey("message")) {
        return errorResponse["message"] as String;
      } else {
        return _errorMessage;
      }
    } catch (e) {
      return _errorMessage;
    }
  }

  String get _errorMessage => switch (type) {
        DioExceptionType.cancel => "",
        DioExceptionType.connectionTimeout ||
        DioExceptionType.connectionError =>
          "t.common.unableConnectInternet",
        _ => toString(),
      };
}
