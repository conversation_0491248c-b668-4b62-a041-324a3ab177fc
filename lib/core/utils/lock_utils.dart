import 'package:intl/intl.dart';

class LockUtils {}

List<int> getKeyLongs() {
    // New encryption secret key (not directly used in this function)
    String newEncSecretKey = "00112233445566778899aabbccddeeff";

    // Define key longs as a list of integers
    List<int> keyLongs = [
      int.parse(
        "33221100",
        radix: 16,
      ), // Equivalent to Long.decode("0x33221100")
      int.parse("77665544", radix: 16),
      int.parse("bbaa9988", radix: 16),
      int.parse("ffeeddcc", radix: 16),
    ];

    return keyLongs;
  }

 List<int> convertFromString(String a) {
    if (a.length % 8 == 0) {
      List<int> ret = List.filled(a.length ~/ 2, 0);
      for (int i = 0; i < a.length ~/ 2; i++) {
        ret[i] = int.parse(a.substring(i * 2, (i + 1) * 2), radix: 16) & 0xFFFF;
      }
      return ret;
    }
    throw Exception("Input string invalid, please check");
  }

String getHexTime() {
  // Format for year, month, day, hour, minute, second
  final DateFormat formatter = DateFormat('yyMMddHHmmss');
  // Get current date and time
  final DateTime now = DateTime.now();
  // Get formatted date-time string
  final String dateTime = formatter.format(now);
  // Parse individual components
  final int yy = int.parse(dateTime.substring(0, 2));
  final int month = int.parse(dateTime.substring(2, 4));
  final int dd = int.parse(dateTime.substring(4, 6));
  final int hh = int.parse(dateTime.substring(6, 8));
  final int mn = int.parse(dateTime.substring(8, 10));
  final int ss = int.parse(dateTime.substring(10, 12));

  // Convert each component to hexadecimal and format with leading zero if needed
  String formatHex(int value) =>
      value.toRadixString(16).padLeft(2, '0').toUpperCase();

  // Build the hex time string
  final StringBuffer sb = StringBuffer();
  sb.write(formatHex(yy));
  sb.write(formatHex(month));
  sb.write(formatHex(dd));
  sb.write(formatHex(hh));
  sb.write(formatHex(mn));
  sb.write(formatHex(ss));

  return sb.toString();
}

List<String> splitStringEveryTwoChars(String input) {
  int length = input.length;
  int numberOfParts = (length + 1) ~/ 2; // Calculate number of parts
  List<String> parts = List.filled(numberOfParts, '');

  for (int i = 0; i < length; i += 2) {
    if (i + 1 < length) {
      parts[i ~/ 2] = input.substring(i, i + 2); // Take two characters
    } else {
      parts[i ~/ 2] = input.substring(i); // Handle the remaining character
    }
  }

  return parts;
}

String getHexAddition(String hexStr) {
  // Split the hex string into two-character chunks

  // Split the input hex string
  List<String> hexStrings = splitStringEveryTwoChars(hexStr);

  // Convert each hex string to an integer and calculate the sum
  int sum = hexStrings.fold(0, (acc, hex) => acc + int.parse(hex, radix: 16));

  // Convert the sum back to a hexadecimal string and get the last two characters
  String hexResult = sum.toRadixString(16).toUpperCase();
  return hexResult.substring(hexResult.length - 2);
}

List<int> hexStringToBytes(String src) {
  int length = src.length ~/ 2;
  List<int> bytes = List.filled(length, 0);

  for (int i = 0; i < length; i++) {
    bytes[i] = int.parse(src.substring(i * 2, i * 2 + 2), radix: 16);
  }

  return bytes;
}

List<String> strCutToList(String originalString, int fixedLength) {
  List<String> subStrArr = [];

  for (int i = 0; i < originalString.length; i += fixedLength) {
    // Calculate the end index for substring
    int endIndex = (i + fixedLength > originalString.length)
        ? originalString.length
        : i + fixedLength;

    // Extract substring
    String subString = originalString.substring(i, endIndex);

    // Pad right with '0' if necessary
    String fixSubString = subString.padRight(fixedLength, '0');

    // Add to the list
    subStrArr.add(fixSubString);
  }

  return subStrArr;
}


class TEA {
  static const int UIFILTER =
      0xFFFFFFFF; // Equivalent to Long.decode("0xffffffff")
  static const int UBFILTER = 0xFF; // Equivalent to Short.decode("0xff")
  static const int DELTA = 0x9E3779B9; // TEA delta constant

  /// Encrypts a 64-bit plaintext (`List<int>`) using a 128-bit key (`List<int>`).
  static List<int> encipher(List<int> v, List<int> k) {
    List<int> vL = shortToLong(v);
    List<int> kL = shortToLong(k);
    List<int> wL = List.filled(vL.length, 0);

    int y = vL[0];
    int z = vL[1];
    int a = kL[0];
    int b = kL[1];
    int c = kL[2];
    int d = kL[3];
    int n = 8; // Encrypt 16 times
    int sum = 0;

    while (n-- > 0) {
      sum = (sum + DELTA) & UIFILTER;
      y = (y + (((z << 4) + a) ^ (z + sum) ^ ((z >> 5) + b))) & UIFILTER;
      z = (z + (((y << 4) + c) ^ (y + sum) ^ ((y >> 5) + d))) & UIFILTER;
    }

    wL[0] = y;
    wL[1] = z;
    return longToShort(wL);
  }

  /// Decrypts a 64-bit ciphertext (`List<int>`) using a 128-bit key (`List<int>`).
  static List<int> decipher(List<int> v, List<int> k) {
    List<int> vL = shortToLong(v);
    List<int> kL = shortToLong(k);
    List<int> wL = List.filled(vL.length, 0);

    int y = vL[0];
    int z = vL[1];
    int a = kL[0];
    int b = kL[1];
    int c = kL[2];
    int d = kL[3];
    int n = 8; // Decrypt 16 times
    int sum = (DELTA * n) & UIFILTER;

    while (n-- > 0) {
      z = (z - (((y << 4) + c) ^ (y + sum) ^ ((y >> 5) + d))) & UIFILTER;
      y = (y - (((z << 4) + a) ^ (z + sum) ^ ((z >> 5) + b))) & UIFILTER;
      sum = (sum - DELTA) & UIFILTER;
    }

    wL[0] = y;
    wL[1] = z;
    return longToShort(wL);
  }

  /// Converts a `List<int>` (4 x 16-bit shorts) to a `List<int>` (2 x 32-bit longs).
  static List<int> shortToLong(List<int> source) {
    int sourlen = source.length;
    int turn = sourlen ~/ 4;
    int remainder = sourlen % 4;
    int tarlen = turn + (remainder == 0 ? 0 : 1);
    List<int> target = List.filled(tarlen, 0);

    for (int turnIter = 0; turnIter < tarlen; turnIter++) {
      for (int iter = 0; iter < 4; iter++) {
        target[turnIter] = (target[turnIter] << 8) & UIFILTER;
        if (turnIter != turn - 1 ||
            (turnIter == turn - 1 && (iter < remainder || remainder == 0))) {
          target[turnIter] += source[turnIter * 4 + (3 - iter)] & UBFILTER;
        }
      }
    }
    return target;
  }

  /// Converts a `List<int>` (2 x 32-bit longs) to a `List<int>` (4 x 16-bit shorts).
  static List<int> longToShort(List<int> source) {
    int sourlen = source.length;
    List<int> target = List.filled(sourlen * 4, 0);

    for (int iter = 0; iter < target.length; iter++) {
      int move = 8 * (iter % 4); // Little-endian order
      target[iter] = ((source[iter ~/ 4] >> move) & UBFILTER);
    }
    return target;
  }
}
