class Validator {
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) return "メールを入力"; // Nhập email
    final emailRegex =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) return "無効なメール"; // Email không hợp lệ
    return null;
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) return "パスワードを入力"; // Nhập mật khẩu
    if (value.length < 6) return "6文字以上"; // Ít nhất 6 ký tự
    return null;
  }

  static String? validateRequiredField(String? value, String fieldName) {
    if (value == null || value.isEmpty)
      return "$fieldName を入力"; // Nhập {fieldName}
    return null;
  }

  static String? validateNickname(String? value) {
    if (value == null || value.trim().isEmpty) {
      return "ニックネームを入力してください。";
    }
    if (value.length > 20) {
      return "ニックネームは20文字以内で入力してください。";
    }
    return null;
  }

  static String? validateBirthYear(String? value) {
    if (value == null || value.trim().isEmpty) {
      return "生まれた年を入力してください。";
    }
    if (!RegExp(r'^\d{4}$').hasMatch(value)) {
      return "正しい4桁の年を入力してください。";
    }
    int year = int.tryParse(value) ?? 0;
    if (year < 1900 || year > DateTime.now().year) {
      return "有効な年を入力してください。";
    }
    return null;
  }

  static String? validatePrefecture(String? value) {
    if (value == null || value.trim().isEmpty) {
      return "都道府県を入力してください。";
    }
    return null;
  }

  static String? validateGender(String? value) {
    if (value == null || value.trim().isEmpty) {
      return "性別を入力してください。";
    }
    return null;
  }

  static String? validateOccupation(String? value) {
    if (value == null || value.trim().isEmpty) {
      return "ご職業を入力してください。";
    }
    return null;
  }

  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return "電話番号を入力してください。";
    }
    final phoneRegex = RegExp(r'^\d{10,11}$');
    if (!phoneRegex.hasMatch(value)) {
      return "有効な電話番号を入力してください (10〜11桁の数字)";
    }
    return null;
  }
}
