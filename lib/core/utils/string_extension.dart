extension StringPrice on String {
  String priceString() {
    final number = double.tryParse(this) ?? 0.0;
    return number.toInt().priceString;
  }
}

extension MoneyNumber on int {
  String get priceString {
    final numberString = abs().toString();
    final numberDigits = List.from(numberString.split(''));
    int index = numberDigits.length - 3;
    while (index > 0) {
      numberDigits.insert(index, ',');
      index -= 3;
    }
    return this >= 0 ? numberDigits.join() : "-${numberDigits.join()}";
  }
}
