import 'package:package_info_plus/package_info_plus.dart';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';

class AppVersionService {
  static final AppVersionService _instance = AppVersionService._internal();

  factory AppVersionService() => _instance;

  AppVersionService._internal();

  PackageInfo? _packageInfo;


  Future<void> init() async {
    _packageInfo = await PackageInfo.fromPlatform();
  }


  String get currentVersion => _packageInfo?.version ?? "Unknown";


  String get buildNumber => _packageInfo?.buildNumber ?? "Unknown";


  String get appName => _packageInfo?.appName ?? "Unknown";


  String get packageName => _packageInfo?.packageName ?? "Unknown";
}


Future<String?> getDeviceId() async {
  final deviceInfo = DeviceInfoPlugin();

  if (Platform.isAndroid) {
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    return androidInfo.id; 
  } else if (Platform.isIOS) {
    IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    return iosInfo.identifierForVendor;
  }

  return null;
}
