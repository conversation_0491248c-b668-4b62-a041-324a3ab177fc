import 'package:intl/intl.dart';

class DateTimeFormatter {
  static final DateFormat _dateFormat = DateFormat('yyyy/MM/dd');
  static final DateFormat _timeFormat = DateFormat('HH:mm');
  static final DateFormat _dateTimeFormat = DateFormat('yyyy/MM/dd HH:mm');
  static final DateFormat _fullDateTimeFormat =
      DateFormat('yyyy/MM/dd HH:mm:ss');
  static final DateFormat _monthDayFormat = DateFormat('MM/dd');
  static final DateFormat _yearMonthFormat = DateFormat('yyyy/MM');
  static final DateFormat _dayOfWeekFormat = DateFormat('E', 'ja');
  static final DateFormat _monthDayTimeFormat = DateFormat('MM/dd HH:mm');

  /// Format date to yyyy/MM/dd
  static String formatDate(DateTime date) {
    return _dateFormat.format(date);
  }

  /// Format time to HH:mm
  static String formatTime(DateTime time) {
    return _timeFormat.format(time);
  }

  /// Format date time to yyyy/MM/dd HH:mm
  static String formatDateTime(DateTime dateTime) {
    return _dateTimeFormat.format(dateTime);
  }

  /// Format date time to yyyy/MM/dd HH:mm:ss
  static String formatFullDateTime(DateTime dateTime) {
    return _fullDateTimeFormat.format(dateTime);
  }

  /// Format date to MM/dd
  static String formatMonthDay(DateTime date) {
    return _monthDayFormat.format(date);
  }

  /// Format date to yyyy/MM
  static String formatYearMonth(DateTime date) {
    return _yearMonthFormat.format(date);
  }

  /// Format date to day of week in Japanese (月, 火, 水, 木, 金, 土, 日)
  static String formatDayOfWeek(DateTime date) {
    return _dayOfWeekFormat.format(date);
  }

  /// Format date time to MM/dd HH:mm
  static String formatMonthDayTime(DateTime dateTime) {
    return _monthDayTimeFormat.format(dateTime);
  }

  /// Format date range to yyyy/MM/dd - yyyy/MM/dd
  static String formatDateRange(DateTime startDate, DateTime endDate) {
    return '${formatDate(startDate)} - ${formatDate(endDate)}';
  }

  /// Format date time range to MM/dd HH:mm - MM/dd HH:mm
  static String formatDateTimeRange(
      DateTime startDateTime, DateTime endDateTime) {
    return '${formatMonthDayTime(startDateTime)} - ${formatMonthDayTime(endDateTime)}';
  }

  /// Format relative time (e.g. "2 hours ago", "3 days ago")
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}年前';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}ヶ月前';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}日前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}時間前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分前';
    } else {
      return '今';
    }
  }

  /// Parse date string to DateTime
  static DateTime? parseDate(String dateString) {
    try {
      return _dateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Parse date time string to DateTime
  static DateTime? parseDateTime(String dateTimeString) {
    try {
      return _dateTimeFormat.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day;
  }

  /// Get start of day
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Get end of day
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Format date from yyyy-MM-dd to yyyy/MM/dd
  static String formatDateFromApi(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return _dateFormat.format(date);
    } catch (e) {
      return dateString;
    }
  }
}


 // Format ngày
// final date = DateTime.now();
// final formattedDate = DateTimeFormatter.formatDate(date); // 2023/03/29

 // Format thời gian tương đối
// final twoHoursAgo = DateTime.now().subtract(const Duration(hours: 2));
// final relativeTime = DateTimeFormatter.formatRelativeTime(twoHoursAgo); // 2時間前

 // Format khoảng thời gian
// final startDate = DateTime.now();
// final endDate = startDate.add(const Duration(days: 3));
// final dateRange = DateTimeFormatter.formatDateRange(startDate, endDate); // 2023/03/29 - 2023/04/01

 // Kiểm tra ngày
// final isToday = DateTimeFormatter.isToday(date); // true