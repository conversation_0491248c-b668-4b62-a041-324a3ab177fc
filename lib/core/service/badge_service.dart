import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';

class BadgeService {
  static Future<void> updateBadge(int unreadCount) async {
    // Chỉ thực hiện trên iOS
    // if (!Platform.isIOS) {
    //   print('=== BADGE UPDATE SKIPPED: Not iOS platform ===');
    //   return;
    // }
    // await AwesomeNotifications().setGlobalBadgeCounter(10);
    try {
      if (unreadCount > 0) {
        await AwesomeNotifications().setGlobalBadgeCounter(unreadCount);
      } else {
        await AwesomeNotifications().resetGlobalBadge();
      }
    } catch (e) {}
  }

  static Future<void> resetBadge() async {
    if (!Platform.isIOS) {
      return;
    }
    try {
      await AwesomeNotifications().resetGlobalBadge();
    } catch (e) {}
  }

  /// Kiểm tra xem có phải iOS platform không
  static bool get isIOSPlatform => Platform.isIOS;
}
