import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/core/networking/http_provicer.dart';
import 'package:kitemite_app/model/request/event/create_comment_request.dart';
import 'package:kitemite_app/model/request/event/create_event_request.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/response/event/event_comment_model.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';
import 'package:retrofit/retrofit.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'event_service.g.dart';

@riverpod
EventService apiEventService(Ref ref) {
  final dio = ref.read(dioProvider);
  return EventService(dio);
}

@RestApi()
abstract class EventService {
  factory EventService(Dio dio, {String baseUrl}) = _EventService;

  @GET('/events')
  Future<BaseListResponse<EventDetailModel>> getListEvents(
    @Query('page') int page,
    @Query('per_page') int perPage,
    @Query('name') String? search,
    @Query('tags') String? tags,
  );

  @POST('/events')
  Future<void> createEvent(
      @Body() CreateEventRequest request);

  @PUT('/events/{id}')
  Future<void> updateEvent(
    @Path() int id,
    @Body() CreateEventRequest request,
  );

  @GET('/events/{id}')
  Future<BaseResponse<EventDetailModel>> getEventDetail(@Path() int id);

  @GET('/comments')
  Future<BaseListResponse<EventCommentModel>> getEventComments(
    @Query('event_id') int eventId,
    @Query('page') int page,
    @Query('per_page') int perPage,
  );

  @POST('/comments')
  Future<void> createComment(
    @Body() CreateCommentRequest request,
  );
}
