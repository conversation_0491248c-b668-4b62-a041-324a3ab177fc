import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/request/store/register_seller_request.dart';
import 'package:kitemite_app/model/request/store/template_request.dart';
import 'package:kitemite_app/model/request/store/update_template_request.dart';
import 'package:kitemite_app/model/response/store/nearby_store_model.dart';
import 'package:kitemite_app/model/response/store/sales_history_model.dart';
import 'package:kitemite_app/model/response/store/store_detail_model.dart';
import 'package:kitemite_app/model/response/store/store_model.dart';
import 'package:kitemite_app/model/response/store/template_model.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';
import 'package:retrofit/retrofit.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../networking/http_provicer.dart';

part 'store_service.g.dart';

@riverpod
StoreService apiStoreService(Ref ref) {
  final dio = ref.read(dioProvider);
  return StoreService(dio);
}

@RestApi()
abstract class StoreService {
  factory StoreService(Dio dio, {String baseUrl}) = _StoreService;

  @GET("/users/{userId}/stores/{storeId}")
  Future<BaseResponse<StoreModel>> getDetailStore(
      @Path() int userId, @Path() int storeId);

  @PUT("/users/{userId}/stores/{storeId}")
  Future<void> updateStore(
    @Path() int userId,
    @Path() int storeId,
    @Body() RegisterSellerRequest request,
  );

  @POST("/users/{userId}/stores")
  Future<void> userRegisterSeller(
    @Path() int userId,
    @Body() RegisterSellerRequest request,
  );

  @GET("/places")
  Future<BaseListResponse<NearbyStoreModel>> getNearbyStores(
    @Query("latitude") double? latitude,
    @Query("longitude") double? longitude,
    @Query("name") String? name,
    @Query("open") String? open,
    @Query("close") String? close,
    @Query("province") String? province,
    @Query("role") String? role,
  );
  @GET("/buyer-places")
  Future<BaseListResponse<NearbyStoreModel>> getNearbyStoresGuest(
    @Query("latitude") double? latitude,
    @Query("longitude") double? longitude,
  );
  @GET("/warehouses")
  Future<BaseListResponse<WarehouseModel>> getListStoreSeller();

  @GET("/places/{id}")
  Future<BaseResponse<StoreDetailModel>> getStoreDetail(
    @Path() int id,
    @Query("role") String role,
  );
  @GET("/buyer-places/{id}")
  Future<BaseResponse<StoreDetailModel>> getStoreDetailGuest(
    @Path() int id,
  );
  @GET("/warehouses/{id}")
  Future<BaseResponse<WarehouseModel>> getStoreDetailWarehouse(
    @Path() int id,
  );

  @GET("/warehouses/products")
  Future<BaseListResponse<WarehouseModel>> getListProductWarehouse();

  @GET("/product-base")
  Future<BaseListResponse<TemplateModel>> getListTemplateWarehouse();

  @POST("/product-base")
  Future<void> createTemplateWarehouse(@Body() TemplateRequest request);

  @GET("/product-base/{id}")
  Future<BaseResponse<TemplateModel>> getDetailTemplateWarehouse(
      @Path() int id);

  @DELETE("/product-base/{id}")
  Future<void> deleteTemplateWarehouse(@Path() int id);

  @PUT("/product-base/{id}")
  Future<void> updateTemplateWarehouse(
    @Path() int id,
    @Body() UpdateTemplateRequest request,
  );

  @POST("/warehouses/bulk-update")
  Future<void> bulkUpdate(
    @Body() WarehousesBulkUpdateRequest request,
  );

  @GET("/seller-orders-histories")
  Future<BaseListResponse<SalesHistoryModel>> getSellerOrdersHistories(
    @Query("page") int page,
  );
}
