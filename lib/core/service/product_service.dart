import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/request/order/create_order_request.dart';
import 'package:kitemite_app/model/request/product/register_product_request.dart';
import 'package:kitemite_app/model/response/order/create_order_response.dart';
import 'package:kitemite_app/model/response/order/draft_order_response.dart';
import 'package:kitemite_app/model/response/product/check_cart_model.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';
import 'package:retrofit/retrofit.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../networking/http_provicer.dart';

part 'product_service.g.dart';

@riverpod
ProductService apiProductService(Ref ref) {
  final dio = ref.read(dioProvider);
  return ProductService(dio);
}

@RestApi()
abstract class ProductService {
  factory ProductService(Dio dio, {String baseUrl}) = _ProductService;

  @GET("/products")
  Future<BaseListResponse<ProductModel>> getProducts(
    @Query("page") int page,
    @Query("per_page") int perPage,
    @Query("name") String? search,
    @Query("latitude") double? latitude,
    @Query("longitude") double? longitude,
  );

  @GET("/products/{id}")
  Future<BaseResponse<ProductModel>> getProductDetail(@Path("id") int id);

  @DELETE("/products/{id}")
  Future<void> deleteProduct(@Path("id") int id);

  @POST("/products/{id}/report")
  Future<void> reportProduct(
      @Path("id") int id, @Body() Map<String, dynamic> request);

  @POST("/products")
  Future<void> registerProduct(@Body() RegisterProductRequest request);

  @PUT("/products/{id}")
  Future<void> updateProduct(
      @Path("id") int id, @Body() RegisterProductRequest request);

  @POST("/create-draft-order")
  Future<BaseResponse<DraftOrderResponse>> createDraftOrder(
    @Body() Map<String, dynamic> request,
  );

  @POST("/users/{userId}/orders")
  Future<BaseResponse<CreateOrderData>> createOrder(
    @Path("userId") int userId,
    @Body() CreateOrderRequest request,
  );
 @GET("/check-cart")
  Future<BaseListResponse<CheckCartModel>> checkCart(
    @Query("product_ids") int productIds,
  );

  @GET("/users/{userId}/orders/{orderId}")
  Future<BaseResponse<CreateOrderData>> getOrderDetail(
    @Path("userId") int userId,
    @Path("orderId") int orderId,
  );
}
