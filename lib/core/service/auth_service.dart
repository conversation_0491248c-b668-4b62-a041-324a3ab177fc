import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/core/networking/http_provicer.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/request/auth/user_request.dart';
import 'package:kitemite_app/model/response/auth/image_url_model.dart';
import 'package:kitemite_app/model/response/auth/link_card_model.dart';
import 'package:kitemite_app/model/response/auth/payment_info_model.dart';
import 'package:kitemite_app/model/response/auth/token_model.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';
import 'package:kitemite_app/model/response/location/province_model.dart';
import 'package:kitemite_app/model/response/version_app/version_app_model.dart';
import 'package:retrofit/retrofit.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_service.g.dart';

@riverpod
AuthService apiAuthService(Ref ref) {
  final dio = ref.read(dioProvider);
  return AuthService(dio);
}

@RestApi()
abstract class AuthService {
  factory AuthService(Dio dio, {String baseUrl}) = _AuthService;

  @POST("/login")
  Future<void> login(
    @Body() Map<String, dynamic> request,
  );

  @PUT("/resend-otp")
  Future<void> resentOTP(
    @Body() Map<String, dynamic> request,
  );

  @POST("/verify")
  Future<BaseResponse<TokenModel>> verifyOTP(
    @Body() Map<String, dynamic> request,
  );

  @GET("/me")
  Future<BaseResponse<UserInfoModel>> getInfo();

  @GET("/app-version")
  Future<VersionAppModel> checkVersionApp();

  @POST("/logout")
  Future<void> logout(
    @Body() Map<String, dynamic> request,
  );

  @DELETE("/users/{id}")
  Future<void> destroyUser(@Path() int id);

  @POST("/users/{userId}/devices")
  Future<void> storeDevice(
    @Path() int userId,
    @Body() Map<String, dynamic> request,
  );

  @PUT("/users/{userId}")
  Future<void> updateUser(
    @Path() int userId,
    @Body() UserRequest request,
  );

  @POST("/uploads")
  Future<ImageUrlModel> upLoadFile(
    @Body() Map<String, dynamic> request,
  );

  @GET("/generate-link-add-card")
  Future<BaseResponse<LinkCardModel>> generateLinkAddCard();

  @GET("/users/{userId}/payment-information")
  Future<BaseListResponse<PaymentInfoModel>> getPaymentInformation(
    @Path() int userId,
  );

  @GET("/provinces")
  Future<ProvinceListResponse> getProvinces();

  @GET("/users/{userId}/payment-information/{paymentId}/edit")
  Future<void> changeDefaultCard(
    @Path() int userId,
    @Path() int paymentId,
  );

  @DELETE("/users/{userId}/payment-information/{paymentId}")
   Future<void> deleteCard(
    @Path() int userId,
    @Path() int paymentId,
  );
}
