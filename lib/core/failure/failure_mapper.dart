import 'package:dio/dio.dart';
import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/failure/local_failure.dart';
import 'package:kitemite_app/core/failure/network_failure.dart';

class FailureMapper {
  static AppFailure getFailures(Exception error) {
    if (error is DioException) {
      return NetworkFailure.getDioException(error);
    } else {
      return LocalFailure.fromException(error);
    }
  }
}
