import 'package:dio/dio.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/service/push_notification_service.dart';
import 'package:kitemite_app/core/utils/app_preferences.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class ApiInterceptor extends Interceptor {
  late final Ref _ref;
  ApiInterceptor(this._ref) : super();
  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    String? token = AppPreferences.getString(AppConstants.tokenKey);

    if (token != null && token.isNotEmpty) {
      options.headers["Authorization"] = "Bearer $token";
    }
    return handler.next(options);
  }

  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) async {
    if (response.statusCode != null &&
        (response.statusCode! >= 400 && response.statusCode! <= 599)) {
      return handler.reject(
        DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'].toString(),
        ),
        true,
      );
    }

    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      return handler.next(response);
    }
  }

  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    // Handle no internet connection errors
    if (err.type == DioExceptionType.connectionError ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.connectionTimeout) {
      return handler.reject(
        DioException(
          type: DioExceptionType.connectionError,
          requestOptions: err.requestOptions,
          response: err.response,
          message: 'インタネットに接続していません。',
        ),
      );
    }

    // Handle 401 Unauthorized: Redirect to login screen
    if (err.response?.statusCode == 401) {
      await AppPreferences.remove(AppConstants.tokenKey);
 
      await AppPreferences.remove(AppConstants.accountModel);
      navigatorKey.currentContext?.go(RouterPaths.login);
    }

    return handler.reject(
      DioException(
        requestOptions: err.requestOptions,
        response: err.response,
        message: err.error.toString(),
      ),
    );
  }
}
