import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';

class CommonButton extends StatelessWidget {
  final String? text;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Widget? customTitle;
  final bool isDynamicWidth;

  const CommonButton({
    super.key,
    this.text,
    required this.onPressed,
    this.backgroundColor,
    this.customTitle,
    this.isDynamicWidth = false,
  });

  factory CommonButton.dynamicWidth({
    required VoidCallback onPressed,
    required Widget customTitle,
    Color? backgroundColor,
  }) {
    return CommonButton(
      onPressed: onPressed,
      customTitle: customTitle,
      backgroundColor: backgroundColor,
      isDynamicWidth: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final buttonContent = customTitle ??
        Text(
          text ?? "",
          style: AppTextStyles.bold(15.sp, color: AppColors.textPrimary),
        );

    return isDynamicWidth
        ? IntrinsicWidth(
            child: _buildButton(buttonContent),
          )
        : SizedBox(
            width: double.infinity,
            child: _buildButton(buttonContent),
          );
  }

  Widget _buildButton(Widget child) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.primary,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      onPressed: onPressed,
      child: child,
    );
  }
}
