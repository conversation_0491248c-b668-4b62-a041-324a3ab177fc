import 'package:flutter/material.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:lottie/lottie.dart';

class Loading extends StatelessWidget {
  const Loading({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints.expand(),
      color: AppColors.mono100.withA<PERSON><PERSON>(175),
      child: Center(
        child: Lottie.asset(
          'assets/animations/loading.json',
          width: 120,
          height: 120,
        ),
      ),
    );
  }
}
