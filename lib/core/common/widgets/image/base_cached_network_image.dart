import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class BaseCachedNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;

  const BaseCachedNetworkImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => placeholder ??
            Center(
              child: CircularProgressIndicator(),
            ),
        errorWidget: (context, url, error) => errorWidget ??
            Center(
              child: Icon(Icons.broken_image, color: Colors.red),
            ),
      ),
    );
  }
}
// BaseCachedNetworkImage(
// imageUrl: 'https://example.com/image.jpg',
// width: 100,
// height: 100,
// borderRadius: BorderRadius.circular(10),
// )
