import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:permission_handler/permission_handler.dart';

class CommonImagePicker {
  final ImagePicker _imagePicker = ImagePicker();
  final int maxSizeInBytes; // Image size limit (bytes)
  final List<String> allowedFormats; // Allowed image formats

  CommonImagePicker({
    this.maxSizeInBytes = 50 * 1024 * 1024, // Default 10MB
    this.allowedFormats = const ['jpg', 'jpeg', 'png', 'gif'],
  });

  /// Check and request camera permission
  Future<bool> _checkCameraPermission(BuildContext context) async {
    final status = await Permission.camera.request();
    if (!status.isGranted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text("Camera permission is required to take photos")),
      );
      return false;
    }
    return true;
  }

  /// Check and request storage permission
  Future<bool> _checkStoragePermission(BuildContext context) async {
    // Request photos permission for accessing gallery
    final photosStatus = await Permission.photos.request();
    if (!photosStatus.isGranted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text("Photos permission is required to access gallery")),
      );
      return false;
    }

    return true;
  }

  /// Pick an image from Camera or Gallery
  Future<XFile?> pickImage(BuildContext context,
      {ImageSource source = ImageSource.camera}) async {
    // Check permission based on source
    // if (source == ImageSource.camera) {
    //   if (!await _checkCameraPermission(context)) return null;
    // } else {
    //   if (!await _checkStoragePermission(context)) return null;
    // }

    final XFile? pickedFile = await _imagePicker.pickImage(source: source);

    if (pickedFile == null) {
      return null;
    }

    // Check image size
    File file = File(pickedFile.path);
    int fileSize = await file.length();
    if (fileSize > maxSizeInBytes) {
      context.showErrorSnackBar("写真は50MB以下でアップロードしてください。");
      return null;
    }

    // Check image format
    String extension = pickedFile.path.split('.').last.toLowerCase();
    if (!allowedFormats.contains(extension)) {
      context.showErrorSnackBar("JPEG、JPG、PNGファイルのみアップロードが可能です");
      return null;
    }

    return pickedFile;
  }

  /// Pick multiple images from Gallery
  Future<List<XFile>> pickMultiImage(BuildContext context) async {
    // if (!await _checkStoragePermission(context)) return [];

    final List<XFile> pickedFiles = await _imagePicker.pickMultiImage();

    if (pickedFiles.isEmpty) {
      return [];
    }

    List<XFile> validFiles = [];

    for (var pickedFile in pickedFiles) {
      // Check image size
      File file = File(pickedFile.path);
      int fileSize = await file.length();
      if (fileSize > maxSizeInBytes) {
        context.showErrorSnackBar("写真は50MB以下でアップロードしてください。");
        continue;
      }

      // Check image format
      String extension = pickedFile.path.split('.').last.toLowerCase();
      if (!allowedFormats.contains(extension)) {
        context.showErrorSnackBar("JPEG、JPG、PNGファイルのみアップロードが可能です。");
        continue;
      }

      validFiles.add(pickedFile);
    }

    return validFiles;
  }

  /// Convert image to Base64
  Future<String> convertToBase64(File file) async {
    List<int> imageBytes = await file.readAsBytes();
    String base64String = base64Encode(imageBytes);

    // Lấy phần mở rộng của tệp (jpg, jpeg, png)
    String extension = file.path.split('.').last.toLowerCase();
    String mimeType = "image/$extension";

    return "data:$mimeType;base64,$base64String";
  }
}
