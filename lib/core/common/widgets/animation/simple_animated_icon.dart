import 'package:flutter/material.dart';

class SimpleAnimatedIcon extends StatefulWidget {
  final Widget childWidget;
  const SimpleAnimatedIcon({super.key, required this.childWidget});
  @override
  _SimpleAnimatedIconState createState() => _SimpleAnimatedIconState();
}

class _SimpleAnimatedIconState extends State<SimpleAnimatedIcon> {
  bool _toggled = true;

  @override
  void initState() {
    super.initState();
    _startAnimationLoop();
  }

  void _startAnimationLoop() async {
    while (mounted) {
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() => _toggled = !_toggled);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSlide(
      duration: const Duration(milliseconds: 500),
      offset: _toggled ? const Offset(0, 0) : const Offset(0, -0.1),
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 500),
        opacity: _toggled ? 1.0 : 0.3,
        child: widget.childWidget,
      ),
    );
  }
}
