import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';

class DottedCirclePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double radius = size.width / 2;
    double dashWidth = 5;
    double dashSpace = 5;
    final paint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final rect =
        Rect.fromCircle(center: Offset(radius, radius), radius: radius);
    final circumference = 2 * 3.1415926535 * radius;

    double dashCount = circumference / (dashWidth + dashSpace);
    double gapAngle = (dashSpace / circumference) * 360;
    double dashAngle = (dashWidth / circumference) * 360;

    for (int i = 0; i < dashCount; i++) {
      double startAngle = (dashAngle + gapAngle) * i;
      canvas.drawArc(
          rect, radians(startAngle), radians(dashAngle), false, paint);
    }
  }

  double radians(double degrees) => degrees * 3.1415926535 / 180;

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class DottedRoundedRectPainter extends CustomPainter {
  final double dashWidth;
  final double dashSpace;
  final double borderRadius;
  final Color color;
  final double strokeWidth;

  DottedRoundedRectPainter({
    this.dashWidth = 5,
    this.dashSpace = 5,
    this.borderRadius = 12,
    this.color = AppColors.mono20,
    this.strokeWidth = 2,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    const double verticalPadding = 6;
    const double horizontalPadding = 2;

    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        horizontalPadding,
        -verticalPadding,
        size.width - 2 * horizontalPadding,
        size.height + 2 * verticalPadding,
      ),
      Radius.circular(borderRadius),
    );

    final path = Path()..addRRect(rect);

    for (final metric in path.computeMetrics()) {
      double distance = 0;
      while (distance < metric.length) {
        final segment = metric.extractPath(distance, distance + dashWidth);
        canvas.drawPath(segment, paint);
        distance += dashWidth + dashSpace;
      }
    }
  }

  List<PathMetric> pathMetrics(RRect rect) {
    final path = Path()..addRRect(rect);
    return path.computeMetrics().toList();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
