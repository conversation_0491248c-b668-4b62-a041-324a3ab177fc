import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';

class InputTextField extends StatelessWidget {
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;
  final ValueChanged<String>? onChanged;
  final String? label;
  final String? errorText;
  final bool obscureText;
  final Widget? prefixIcon;
  final String? hintText;
  final TextAlign? textAlign;
  final Widget? suffixIcon;
  final int? maxLine;
  final int? maxLength;
  final TextEditingController? textController;
  final bool enabled;
  final String? Function(String?)? validator;
  final Function(String?)? onSaved;
  final Function(String)? onFieldSubmitted;
  final InputDecoration? decoration;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? ontap;
  final Color? fillColor;
  final TextInputType? keyboardType;
  final TextCapitalization? textCapitalization;
  final TextStyle? hintStyle;
  final Color? enabledBorderColor;
  final Color? disabledBorderColor;
  final bool showClearButton;
  final int? minLines;
  final TextInputAction? textInputAction;
  final TextStyle? labelStyle;

  const InputTextField(
      {super.key,
      this.onChanged,
      this.focusNode,
      this.obscureText = false,
      this.prefixIcon,
      this.errorText,
      this.textController,
      this.suffixIcon,
      this.hintText,
      this.maxLine = 1,
      this.label,
      this.enabled = true,
      this.validator,
      this.inputFormatters,
      this.onSaved,
      this.ontap,
      this.textAlign,
      this.decoration,
      this.padding,
      this.maxLength,
      this.fillColor,
      this.keyboardType,
      this.hintStyle,
      this.textCapitalization,
      this.enabledBorderColor,
      this.disabledBorderColor,
      this.minLines,
      this.showClearButton = false,
      this.textInputAction,
      this.onFieldSubmitted,
      this.labelStyle});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(
          textSelectionTheme: const TextSelectionThemeData(
              selectionHandleColor: Colors.transparent)),
      child: TextFormField(
        minLines: minLines,
        textInputAction: textInputAction,
        cursorColor: AppColors.textNormal,
        keyboardType: keyboardType,
        focusNode: focusNode,
        textCapitalization: textCapitalization ?? TextCapitalization.none,
        inputFormatters: inputFormatters,
        onTap: ontap,
        controller: textController,
        onChanged: onChanged,
        onFieldSubmitted: onFieldSubmitted,
        style: AppTextStyles.medium(14.sp,
            color: AppColors.textNormal, decoration: TextDecoration.none),
        maxLines: maxLine,
        enabled: enabled,
        textAlign: textAlign ?? TextAlign.start,
        validator: validator,
        maxLength: maxLength,
        onSaved: onSaved,
        // obscureText: obscureText,
        decoration: InputDecoration(
            contentPadding: const EdgeInsets.fromLTRB(12.0, 16.0, 12.0, 16.0),
            hintText: hintText,
            hintMaxLines: 1,
            isDense: true,
            alignLabelWithHint: true,
            filled: true,
            fillColor: fillColor ?? AppColors.mono0,
            errorStyle: AppTextStyles.regular(12.sp, color: AppColors.error),
            hintStyle: hintStyle ??
                AppTextStyles.regular(14.sp, color: AppColors.textLight),
            prefixIcon: prefixIcon,
            label: label == null
                ? null
                : Text(
                    label ?? '',
                    style: labelStyle ??
                        AppTextStyles.regular(14.sp,
                            color: AppColors.textLight),
                  ),
            suffixIcon: showClearButton && enabled == true
                ? SuffixIconWidget(
                    controller: textController,
                    suffixIcon: suffixIcon,
                    onTap: () {
                      onChanged?.call("");
                    },
                  )
                : suffixIcon,
            errorText: errorText,
            errorMaxLines: 2,
            focusedErrorBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: AppColors.error, width: 1),
                borderRadius: BorderRadius.all(Radius.circular(8.r))),
            errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: AppColors.error, width: 1),
                borderRadius: BorderRadius.all(Radius.circular(8.r))),
            focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                    color: AppColors.mono40.withOpacity(0.5), width: 1),
                borderRadius: BorderRadius.all(Radius.circular(8.r))),
            enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                    color:
                        enabledBorderColor ?? AppColors.mono40.withOpacity(0.5),
                    width: 1),
                borderRadius: BorderRadius.all(Radius.circular(8.r))),
            disabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                    color: disabledBorderColor ??
                        AppColors.mono40.withOpacity(0.5),
                    width: 1),
                borderRadius: BorderRadius.all(Radius.circular(8.r)))),
      ),
    );
  }
}

class SuffixIconWidget extends StatefulWidget {
  final TextEditingController? controller;
  final Widget? suffixIcon;
  final VoidCallback onTap;

  const SuffixIconWidget(
      {super.key, this.controller, this.suffixIcon, required this.onTap});

  @override
  State<SuffixIconWidget> createState() => _SuffixIconWidgetState();
}

class _SuffixIconWidgetState extends State<SuffixIconWidget> {
  bool isShowClose = false;

  @override
  void initState() {
    super.initState();
    if (widget.controller != null) {
      checkShowClearButton();
      widget.controller?.addListener(checkShowClearButton);
    }
  }

  checkShowClearButton() {
    final text = widget.controller?.text ?? "";
    if (isShowClose) {
      if (text.isEmpty) {
        setState(() {
          isShowClose = false;
        });
      }
    } else {
      if (text.isNotEmpty) {
        setState(() {
          isShowClose = true;
        });
      }
    }
  }

  @override
  void dispose() {
    widget.controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (isShowClose)
          GestureDetector(
            onTap: () {
              widget.controller?.clear();
              widget.onTap.call();
            },
            child: SizedBox(
              height: 48,
              width: 48,
              child: Center(
                child: Icon(
                  Icons.close,
                  color: AppColors.textLight,
                  size: 20.sp,
                ),
              ),
            ),
          ),
        widget.suffixIcon == null ? const SizedBox() : widget.suffixIcon!,
      ],
    );
  }
}
