import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/service/push_notification_service.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';

class FirebaseTokenDialog extends StatefulWidget {
  const FirebaseTokenDialog({super.key});

  @override
  State<FirebaseTokenDialog> createState() => _FirebaseTokenDialogState();
}

class _FirebaseTokenDialogState extends State<FirebaseTokenDialog> {
  String? _firebaseToken;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _getFirebaseToken();
  }

  Future<void> _getFirebaseToken() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final token = await PushNotificationService.getFirebaseToken();

      setState(() {
        _firebaseToken = token;
        _isLoading = false;
        if (token == null) {
          _errorMessage = "Firebase device tokenを取得できませんでした";
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = "エラーが発生しました: ${e.toString()}";
      });
    }
  }

  Future<void> _copyToClipboard() async {
    if (_firebaseToken != null) {
      await Clipboard.setData(ClipboardData(text: _firebaseToken!));
      if (mounted) {
        context.showSuccessSnackBar("Tokenがクリップボードにコピーされました");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              "Firebase Device Token",
              style: AppTextStyles.bold(18.sp, color: AppColors.textPrimary),
            ),
            SizedBox(height: 16.h),

            // Content
            if (_isLoading)
              Center(
                child: Column(
                  children: [
                    const CircularProgressIndicator(
                      color: AppColors.primary,
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      "Tokenを取得中...",
                      style: AppTextStyles.regular(14.sp,
                          color: AppColors.textLightSecondary),
                    ),
                  ],
                ),
              )
            else if (_errorMessage != null)
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 20.sp,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          "エラー",
                          style: AppTextStyles.bold(14.sp, color: Colors.red),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      _errorMessage!,
                      style: AppTextStyles.regular(13.sp,
                          color: Colors.red.shade700),
                    ),
                  ],
                ),
              )
            else if (_firebaseToken != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Device Token:",
                    style: AppTextStyles.medium(14.sp,
                        color: AppColors.textLightSecondary),
                  ),
                  SizedBox(height: 8.h),
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: AppColors.grey200,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: AppColors.border),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SelectableText(
                          _firebaseToken!,
                          style: AppTextStyles.regular(12.sp,
                              color: AppColors.textPrimary),
                        ),
                        SizedBox(height: 12.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            InkWell(
                              onTap: _copyToClipboard,
                              borderRadius: BorderRadius.circular(4.r),
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.copy,
                                      size: 16.sp,
                                      color: AppColors.primary,
                                    ),
                                    SizedBox(width: 4.w),
                                    Text(
                                      "コピー",
                                      style: AppTextStyles.medium(
                                        12.sp,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

            SizedBox(height: 20.h),

            // Buttons
            Row(
              children: [
                if (_errorMessage != null) ...[
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _getFirebaseToken,
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: AppColors.primary),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                      ),
                      child: Text(
                        "再試行",
                        style: AppTextStyles.medium(14.sp,
                            color: AppColors.primary),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                ],
                Expanded(
                  child: CommonButton(
                    text: "閉じる",
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Helper function to show the dialog
Future<void> showFirebaseTokenDialog(BuildContext context) {
  return showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      return const FirebaseTokenDialog();
    },
  );
}
