import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';

class ErrorCommonWidget extends StatelessWidget {
  const ErrorCommonWidget(
      {super.key, required this.error, required this.onPressed});
  final String error;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            error,
            style: AppTextStyles.regular(14.sp, color: AppColors.rambutan100),
          ),
          const Sized<PERSON><PERSON>(height: 16),
          Common<PERSON>utton(
            text: '再実行',
            onPressed: onPressed,
          ),
        ],
      ),
    ));
  }
}
