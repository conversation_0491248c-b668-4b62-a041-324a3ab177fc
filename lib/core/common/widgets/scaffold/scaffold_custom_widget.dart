import 'package:flutter/material.dart';

class BaseScaffold extends StatelessWidget {
  const BaseScaffold(
      {super.key,
      this.body,
      this.appBar,
      this.background,
      this.resizeToAvoidBottomInset,
      this.bottom,
      this.backgroundColor});

  final Widget? body;
  final Widget? bottom;
  final Widget? background;
  final PreferredSizeWidget? appBar;
  final bool? resizeToAvoidBottomInset;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: appBar,
        backgroundColor: backgroundColor ?? Colors.white,
        extendBody: true,
        extendBodyBehindAppBar: true,
        resizeToAvoidBottomInset: resizeToAvoidBottomInset,
        bottomNavigationBar: bottom,
        body: background ??
            SizedBox(
              width: double.maxFinite,
              height: double.maxFinite,
              child: Padding(
                padding: EdgeInsets.only(
                    top: (appBar?.preferredSize.height ?? 0) +
                        MediaQuery.of(context).padding.top),
                child: body,
              ),
            ));
  }
}
