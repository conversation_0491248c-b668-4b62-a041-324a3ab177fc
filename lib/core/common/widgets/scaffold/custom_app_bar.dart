import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppbar({
    super.key,
    this.title,
    this.actions,
    this.flexibleSpace,
    this.automaticallyImplyLeading,
    this.widgetTitle,
    this.leading,
    this.leadingWidth,
    this.styleTitle,
    this.bottom,
    this.bgColor,
    this.heightAppbar,
    this.centerTitle,
    this.maxLines,
    this.isTopLeadingIcon = false,
  });
  final String? title;
  final List<Widget>? actions;
  final Widget? flexibleSpace;
  final bool? automaticallyImplyLeading;
  final Widget? widgetTitle;
  final Widget? leading;
  final double? leadingWidth;
  final TextStyle? styleTitle;
  final PreferredSizeWidget? bottom;
  final Color? bgColor;
  final double? heightAppbar;
  final bool? centerTitle;
  final int? maxLines;
  final bool? isTopLeadingIcon;

  CustomAppbar.basic({
    super.key,
    this.title,
    this.actions,
    this.flexibleSpace,
    this.automaticallyImplyLeading,
    this.widgetTitle,
    this.leadingWidth,
    this.styleTitle,
    VoidCallback? onTap,
    this.bottom,
    bool isLeading = true,
    this.bgColor,
    this.heightAppbar,
    this.centerTitle,
    this.maxLines,
    this.isTopLeadingIcon = false,
  }) : leading = isLeading
            ? _previousButton(onTap, isTopLeadingIcon)
            : const SizedBox();

  @override
  Widget build(BuildContext context) {
    return AppBar(
      surfaceTintColor: Colors.transparent,
      title: widgetTitle ??
          Text(title ?? "",
              style: styleTitle ?? AppTextStyles.bold(20.sp),
              textAlign: TextAlign.center,
              maxLines: maxLines ?? 1,
              overflow: TextOverflow.ellipsis),
      centerTitle: centerTitle ?? true,
      backgroundColor: bgColor ?? Colors.transparent,
      toolbarHeight: heightAppbar,
      elevation: 0.0,
      titleSpacing: 0,
      actions: actions,
      flexibleSpace: flexibleSpace,
      leading: leading,
      leadingWidth: leadingWidth ?? 40.w,
      bottom: bottom,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
      automaticallyImplyLeading: automaticallyImplyLeading ?? true,
    );
  }

  @override
  Size get preferredSize => AppBar().preferredSize;
}

Widget _previousButton(VoidCallback? onTap, bool? isTopLeadingIcon) {
  if (isTopLeadingIcon == true) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTap,
          child: const Icon(
            Icons.arrow_back_ios_new,
            color: Colors.black,
            size: 24,
          ),
        ),
      ],
    );
  }
  return SizedBox(
    child: GestureDetector(
      onTap: onTap,
      child: const Icon(
        Icons.arrow_back_ios_new,
        color: Colors.black,
        size: 24,
      ),
    ),
  );
}
