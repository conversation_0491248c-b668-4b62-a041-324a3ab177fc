import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:textfield_tags/textfield_tags.dart';

class StringTags extends StatefulWidget {
  final List<String> initialTags;
  final String? label;
  final String? hintText;
  final String? helperText;
  final List<String> textSeparators;
  final Function(List<String>)? onTagsChanged;
  final String? Function(String?)? validator;
  final TextEditingController? textController;
  final bool enabled;
  final Color? fillColor;
  final Color? tagColor;
  final Color? tagTextColor;
  final Color? borderColor;
  final bool showSubmitButton;

  const StringTags({
    super.key,
    this.initialTags = const [],
    this.label,
    this.hintText,
    this.helperText,
    this.textSeparators = const [' ', ','],
    this.onTagsChanged,
    this.validator,
    this.textController,
    this.enabled = true,
    this.fillColor,
    this.tagColor,
    this.tagTextColor,
    this.borderColor,
    this.showSubmitButton = true,
  });

  @override
  State<StringTags> createState() => _StringTagsState();
}

class _StringTagsState extends State<StringTags> {
  late double _distanceToField;
  late TextfieldTagsController<String> _stringTagController;
  final FocusNode _focusNode = FocusNode();
  String _pendingInput = '';
  bool _showHelperMessage = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _distanceToField = MediaQuery.of(context).size.width;
  }

  @override
  void initState() {
    super.initState();
    _stringTagController = TextfieldTagsController<String>();

    // Set up focus listener to handle loss of focus
    _focusNode.addListener(_onFocusChange);

    // Check if we need to add a listener to the WillPopScope or route observer
    // to handle when users navigate away
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final navigator = Navigator.of(context);
      navigator.widget.observers.add(_NavigatorObserver(
        onWillPop: _handlePendingInput,
      ));
    });
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    _stringTagController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    if (!_focusNode.hasFocus) {
      _handlePendingInput();
    }
  }

  bool _handlePendingInput() {
    if (_pendingInput.trim().isNotEmpty) {
      _addTag(_pendingInput);
      return true;
    }
    return false;
  }

  void _addTag(String value) {
    final trimmedValue = value.trim();
    if (trimmedValue.isNotEmpty) {
      // Check for validation errors
      final validationError = _validateTag(trimmedValue);
      if (validationError == null) {
        _stringTagController.onTagSubmitted(trimmedValue);
        _pendingInput = '';
        _onTagsChanged();
      } else {
        // Could show a temporary validation error here
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(validationError),
              duration: const Duration(seconds: 2)),
        );
      }
    }
  }

  String? _validateTag(String tag) {
    // Check if tag only contains whitespace
    if (tag.trim().isEmpty) {
      return 'タグを入力してください。';
    }
    if (tag.length > 8) {
      return '8文字以内で入力してください。';
    }
    if (widget.validator != null) {
      final result = widget.validator!(tag);
      if (result != null) return result;
    }
    if (_stringTagController.getTags?.contains(tag) ?? false) {
      return 'タグが既に存在します';
    }
    return null;
  }

  void _onTagsChanged() {
    if (widget.onTagsChanged != null) {
      widget.onTagsChanged!(_stringTagController.getTags ?? []);
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFieldTags<String>(
      textfieldTagsController: _stringTagController,
      initialTags: widget.initialTags,
      textSeparators: widget.textSeparators,
      letterCase: LetterCase.normal,
      validator: _validateTag,
      inputFieldBuilder: (context, inputFieldValues) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: inputFieldValues.textEditingController,
              focusNode: _focusNode,
              enabled: widget.enabled,
              decoration: InputDecoration(
                isDense: true,
                contentPadding:
                    const EdgeInsets.fromLTRB(12.0, 16.0, 12.0, 16.0),
                hintText: inputFieldValues.tags.isNotEmpty
                    ? ''
                    : widget.hintText ?? 'タグを入力...',
                hintStyle:
                    AppTextStyles.regular(12.sp, color: AppColors.textLight),
                helperText: widget.helperText,
                helperStyle:
                    AppTextStyles.regular(12.sp, color: AppColors.textLight),
                errorText: inputFieldValues.error,
                errorMaxLines: 2,
                errorStyle:
                    AppTextStyles.regular(12.sp, color: AppColors.error),
                filled: true,
                fillColor: widget.fillColor ?? AppColors.mono0,
                label: widget.label == null
                    ? null
                    : Text(
                        widget.label!,
                        style: AppTextStyles.regular(14.sp,
                            color: AppColors.textLight),
                      ),
                prefixIconConstraints:
                    BoxConstraints(maxWidth: _distanceToField * 0.74),
                prefixIcon: inputFieldValues.tags.isNotEmpty
                    ? SingleChildScrollView(
                        controller: inputFieldValues.tagScrollController,
                        scrollDirection: Axis.horizontal,
                        child: Padding(
                          padding: EdgeInsets.only(left: 12.w),
                          child: Row(
                            children: inputFieldValues.tags.map((String tag) {
                              return Container(
                                decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(20.r)),
                                  color: widget.tagColor ?? AppColors.primary,
                                ),
                                margin: EdgeInsets.only(right: 4.w),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10.w, vertical: 4.h),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      tag,
                                      style: AppTextStyles.medium(12.sp,
                                          color: widget.tagTextColor ??
                                              Colors.white),
                                    ),
                                    SizedBox(width: 4.w),
                                    InkWell(
                                      child: Icon(
                                        Icons.close,
                                        size: 14.sp,
                                        color: widget.tagTextColor
                                                ?.withOpacity(0.8) ??
                                            Colors.white.withOpacity(0.8),
                                      ),
                                      onTap: () {
                                        inputFieldValues.onTagRemoved(tag);
                                        _onTagsChanged();
                                      },
                                    )
                                  ],
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      )
                    : null,
                suffixIcon: widget.showSubmitButton &&
                        inputFieldValues.textEditingController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.check_circle_outline,
                          color: AppColors.primary,
                          size: 22.sp,
                        ),
                        onPressed: () {
                          final value =
                              inputFieldValues.textEditingController.text;
                          _addTag(value);
                          inputFieldValues.textEditingController.clear();
                          setState(() {
                            _showHelperMessage = false;
                          });
                        },
                      )
                    : null,
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color:
                        widget.borderColor ?? AppColors.mono40.withOpacity(0.5),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.all(Radius.circular(8.r)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color:
                        widget.borderColor ?? AppColors.mono40.withOpacity(0.5),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.all(Radius.circular(8.r)),
                ),
                disabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color:
                        widget.borderColor ?? AppColors.mono40.withOpacity(0.5),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.all(Radius.circular(8.r)),
                ),
                errorBorder: OutlineInputBorder(
                  borderSide:
                      const BorderSide(color: AppColors.error, width: 1),
                  borderRadius: BorderRadius.all(Radius.circular(8.r)),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide:
                      const BorderSide(color: AppColors.error, width: 1),
                  borderRadius: BorderRadius.all(Radius.circular(8.r)),
                ),
              ),
              onChanged: (value) {
                // Prevent whitespace input
                if (value.endsWith(' ')) {
                  inputFieldValues.textEditingController.text = value.trim();
                  inputFieldValues.textEditingController.selection =
                      TextSelection.fromPosition(
                    TextPosition(
                        offset:
                            inputFieldValues.textEditingController.text.length),
                  );
                  return;
                }

                // Prevent emoji and special characters
                final alphanumericWithJapanese = RegExp(
                    r'^[a-zA-Z0-9\u3000-\u303F\u3040-\u309F\u30A0-\u30FF\uFF00-\uFFEF\u4E00-\u9FAF\s]*$');
                if (value.isNotEmpty &&
                    !alphanumericWithJapanese.hasMatch(value)) {
                  // Keep only allowed characters
                  final sanitizedValue = value.replaceAll(
                      RegExp(
                          r'[^\w\s\u3000-\u303F\u3040-\u309F\u30A0-\u30FF\uFF00-\uFFEF\u4E00-\u9FAF]'),
                      '');
                  inputFieldValues.textEditingController.text = sanitizedValue;
                  inputFieldValues.textEditingController.selection =
                      TextSelection.fromPosition(
                    TextPosition(offset: sanitizedValue.length),
                  );
                  return;
                }

                _pendingInput = value;
                setState(() {
                  _showHelperMessage = value.trim().isNotEmpty;
                });
                inputFieldValues.onTagChanged(value);
                _onTagsChanged();
              },
              onFieldSubmitted: (value) {
                // Trim whitespace before submitting
                final trimmedValue = value.trim();
                if (trimmedValue.isNotEmpty) {
                  inputFieldValues.onTagSubmitted(trimmedValue);
                  _onTagsChanged();
                  _pendingInput = '';
                  setState(() {
                    _showHelperMessage = false;
                  });
                }
              },
            ),
            if (_showHelperMessage &&
                inputFieldValues.textEditingController.text.trim().isNotEmpty)
              Padding(
                padding: EdgeInsets.only(top: 4.h, left: 12.w),
                child: Row(
                  children: [
                    Icon(Icons.info_outline,
                        size: 14.sp, color: AppColors.textLight),
                    SizedBox(width: 4.w),
                    Text(
                      'Enterキーまたはチェックマークをタップして追加',
                      style: AppTextStyles.regular(12.sp,
                          color: AppColors.textLight),
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }
}

// Custom navigator observer to handle when users navigate away
class _NavigatorObserver extends NavigatorObserver {
  final Function() onWillPop;

  _NavigatorObserver({required this.onWillPop});

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    onWillPop();
    super.didPop(route, previousRoute);
  }
}

// StringTags(
//   label: "タグ",
//   hintText: "タグを入力...",
//   helperText: "スペースまたはカンマで区切って入力",
//   initialTags: [], // Nếu có tags ban đầu
//   onTagsChanged: (tags) {
//     // Xử lý khi tags thay đổi
//     ref.read(createEventNotifierProvider.notifier).updateRequest(
//       state.request?.copyWith(category: tags.join(',')) ??
//       CreateEventRequest(category: tags.join(','))
//     );
//   },
//   validator: (value) {
//     if (value?.length ?? 0 > 20) {
//       return 'タグは20文字以内で入力してください';
//     }
//     return null;
//   },
//   tagColor: AppColors.primary,
//   tagTextColor: Colors.white,
// )
