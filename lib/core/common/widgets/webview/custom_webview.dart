import 'dart:io' show Platform;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CustomWebView extends ConsumerStatefulWidget {
  const CustomWebView({
    super.key,
    required this.url,
    this.title,
    this.onPageFinished,
    this.onNavigationRequest,
  });

  final String url;
  final String? title;
  final void Function(String)? onPageFinished;
  final NavigationRequest? Function(NavigationRequest)? onNavigationRequest;

  @override
  ConsumerState<CustomWebView> createState() => _CustomWebViewState();
}

class _CustomWebViewState extends ConsumerState<CustomWebView> {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  void _initWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(AppColors.mono0)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            if (widget.onPageFinished != null) {
              widget.onPageFinished!(url);
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            if (widget.onNavigationRequest != null) {
              return widget.onNavigationRequest!(request) == null
                  ? NavigationDecision.navigate
                  : NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        top: true,
        bottom: Platform.isIOS ? false : true,
        child: Stack(
          children: [
            WebViewWidget(controller: _controller),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }
}


            // Navigator.push(
            //   context,
            //   MaterialPageRoute(
            //     builder: (context) =>  CustomWebView(
            //       url: 'https://flutter.dev',
            //       title: 'Flutter Website',
            //       onPageFinished: (url) {
            //         print('Page finished loading: $url');
            //       },
            //     ),
            //   ),
            // );