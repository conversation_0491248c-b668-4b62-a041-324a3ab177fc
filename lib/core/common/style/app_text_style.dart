import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTextStyles {
  static const _defaultColor = Color(0xff212b36);

  static TextStyle _baseTextStyle({
    required double fontSize,
    required FontWeight fontWeight,
    Color color = _defaultColor,
    FontStyle fontStyle = FontStyle.normal,
    double? height,
    TextDecoration? decoration,
    Color? decorationColor,
  }) {
    return GoogleFonts.publicSans(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontStyle: fontStyle,
      height: height,
      decoration: decoration,
      decorationColor: decorationColor,
    );
  }

  static TextStyle regular(
    double fontSize, {
    Color color = _defaultColor,
    FontStyle fontStyle = FontStyle.normal,
    double? height,
    TextDecoration? decoration,
    Color? decorationColor,
  }) =>
      _baseTextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w400,
        color: color,
        fontStyle: fontStyle,
        height: height,
        decoration: decoration,
        decorationColor: decorationColor,
      );

  static TextStyle medium(
    double fontSize, {
    Color color = _defaultColor,
    FontStyle fontStyle = FontStyle.normal,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    Color? decorationColor,
  }) =>
      _baseTextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w500,
        color: color,
        fontStyle: fontStyle,
        height: height,
        decoration: decoration,
        decorationColor: decorationColor,
      );

  static TextStyle bold(
    double fontSize, {
    Color color = _defaultColor,
    FontStyle fontStyle = FontStyle.normal,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    Color? decorationColor,
  }) =>
      _baseTextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w700,
        color: color,
        fontStyle: fontStyle,
        height: height,
        decoration: decoration,
        decorationColor: decorationColor,
      );
}
