import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BaseSearchWidget extends StatefulWidget {
  final Function(String)? onSearch;
  final Duration debounceTime;

  const BaseSearchWidget({
    super.key,
    this.onSearch,
    this.debounceTime = const Duration(seconds: 1),
  });

  @override
  State<BaseSearchWidget> createState() => _BaseSearchWidgetState();
}

class _BaseSearchWidgetState extends State<BaseSearchWidget> {
  Timer? _debounceTimer;
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();
    _debounceTimer = Timer(widget.debounceTime, () {
      widget.onSearch?.call(query);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40.h,
      width: double.infinity,
      child: TextFormField(
        controller: _searchController,
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
          prefixIcon: const Icon(
            Icons.search,
            color: Colors.grey,
          ),
          hintText: "検索",
          hintStyle: const TextStyle(color: Colors.grey),
          fillColor: const Color(0xffF4F6F8),
          filled: true,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
        ),
      ),
    );
  }
}
