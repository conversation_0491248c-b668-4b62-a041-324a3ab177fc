import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/features/home/<USER>/home_buyer/home_provider.dart';
import 'package:kitemite_app/features/map_store/provider/map_store/map_store_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/features/shopping_cart/provider/cart_provider.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';

class BottomNavScreen extends ConsumerWidget {
  final StatefulNavigationShell navigationShell;
  const BottomNavScreen({required this.navigationShell, super.key});

  int _getActualBranchIndex(
      int visibleIndex, bool isPersonal, bool isSeller, bool isGuest) {
    if (isGuest) {
      // For guest: home(0), map(1), event(2)
      return visibleIndex;
    }

    final visibleToActual = <int>[];
    int actualIndex = 0;

    // Home, Map Store (always visible)
    visibleToActual.addAll([0, 1]);
    actualIndex = 2;

    if (isPersonal)
      visibleToActual.add(actualIndex++);
    else
      actualIndex++;

    visibleToActual.add(actualIndex++); // Event

    if (isSeller) visibleToActual.add(actualIndex);

    return visibleToActual[visibleIndex];
  }

  int _getVisibleIndex(
      int actualIndex, bool isPersonal, bool isSeller, bool isGuest) {
    if (isGuest) {
      // For guest: home(0), map(1), event(2)
      // Map the actual index to visible index for guest
      if (actualIndex == 0) return 0; // Home
      if (actualIndex == 1) return 1; // Map
      if (actualIndex == 3)
        return 2; // Event (actual index 3 maps to visible index 2)
      return 0; // Default to home
    }

    final actualToVisible = <int>[];
    int visibleIndex = 0;

    for (int i = 0; i <= 4; i++) {
      if (i == 2 && !isPersonal) continue;
      if (i == 4 && !isSeller) continue;

      actualToVisible.add(i);
    }

    return actualToVisible.indexOf(actualIndex);
  }

  void _onItemTapped(int index, WidgetRef ref) {
    final mode = ref.read(modeNotifierProvider).mode;
    final profile = ref.read(profileProviderProvider).value?.profile;

    final isPersonal = mode == ModeAccount.personal;
    final isSeller = profile?.accountHasRoleSeller ?? false;
    final isGuest = mode == ModeAccount.guest;

    if (isGuest) {
      // Map visible index to actual branch index for guest
      final guestBranchMap = [0, 1, 3]; // 0: Home, 1: Map, 2: Event
      final actualIndex = guestBranchMap[index];
      if (actualIndex == 0) {
        ref.read(homeNotifierProvider.notifier).loadProducts(isRefresh: false);
      } else if (actualIndex == 1) {
        ref.read(mapStoreNotifierProvider.notifier).reloadMap();
      }
      navigationShell.goBranch(actualIndex);
      return;
    }

    final actualIndex =
        _getActualBranchIndex(index, isPersonal, isSeller, isGuest);

    if (actualIndex == 4 && !isSeller) return;

    if (actualIndex == 0 && navigationShell.currentIndex == 0) {
      ref.read(homeNotifierProvider.notifier).loadProducts(isRefresh: false);
    }

    if (actualIndex == 1) {
      ref.read(mapStoreNotifierProvider.notifier).reloadMap();
    }

    if (actualIndex == 2) {
      ref.read(cartNotifierProvider.notifier).loadCartItems();
    }

    if (actualIndex >= 0 && actualIndex < 5) {
      navigationShell.goBranch(actualIndex);
    }
  }

  List<BottomNavigationBarItem> _buildBottomItems(
      bool isPersonal, bool isSeller, bool isGuest) {
    if (isGuest) {
      return [
        const BottomNavigationBarItem(icon: Icon(Icons.home), label: 'ホーム'),
        const BottomNavigationBarItem(icon: Icon(Icons.map), label: '近くの店舗'),
        const BottomNavigationBarItem(icon: Icon(Icons.event), label: 'イベント'),
      ];
    }

    return [
      const BottomNavigationBarItem(icon: Icon(Icons.home), label: 'ホーム'),
      const BottomNavigationBarItem(icon: Icon(Icons.map), label: '近くの店舗'),
      if (isPersonal)
        const BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart), label: '取り置き'),
      const BottomNavigationBarItem(icon: Icon(Icons.event), label: 'イベント'),
      if (isSeller)
        const BottomNavigationBarItem(
            icon: Icon(Icons.storefront), label: '商品管理'),
    ];
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mode = ref.watch(modeNotifierProvider).mode;
    final profile = ref.watch(profileProviderProvider).value?.profile;

    final isPersonal = mode == ModeAccount.personal;
    final isSeller = profile?.accountHasRoleSeller ?? false;
    final isGuest = mode == ModeAccount.guest;
    final visibleIndex = _getVisibleIndex(
        navigationShell.currentIndex, isPersonal, isSeller, isGuest);

    return BaseScaffold(
      body: navigationShell,
      bottom: SafeArea(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: BottomNavigationBar(
              currentIndex: visibleIndex,
              onTap: (index) => _onItemTapped(index, ref),
              type: BottomNavigationBarType.fixed,
              backgroundColor: Colors.transparent,
              elevation: 0,
              selectedItemColor: AppColors.primary,
              unselectedItemColor: AppColors.actionLightActive,
              selectedLabelStyle: AppTextStyles.regular(10.sp),
              unselectedLabelStyle: AppTextStyles.regular(10.sp),
              items: _buildBottomItems(isPersonal, isSeller, isGuest),
            ),
          ),
        ),
      ),
    );
  }
}
