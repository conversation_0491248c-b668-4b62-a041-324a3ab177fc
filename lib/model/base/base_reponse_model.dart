import 'package:json_annotation/json_annotation.dart';

part 'base_reponse_model.g.dart';

@JsonSerializable(
  genericArgumentFactories: true,
)
class BaseResponse<T> {
  @JsonKey(name: 'message')
  String? message;
  @JsonKey(name: 'data')
  T? data;
 

  BaseResponse({this.message, this.data});

  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$BaseResponseFromJson(json, fromJsonT);
  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$BaseResponseToJson(this, toJsonT);
}


 
@JsonSerializable(
  genericArgumentFactories: true,
)
class BaseListResponse<T> {
  @JsonKey(name: 'CODE')
  int? code;
  @JsonKey(name: 'Message')
  String? message;
  @JsonKey(name: 'data')
  List<T>? data;
  @Json<PERSON>ey(name: 'current_page')
  int? currentPage;
  @Json<PERSON>ey(name: 'per_page')
  int? perPage;
  @J<PERSON><PERSON>ey(name: 'last_page')
  int? lastPage;
  @Json<PERSON>ey(name: 'total')
  int? total;
  BaseListResponse({this.code, this.message, this.data, this.currentPage, this.perPage, this.lastPage, this.total});

  factory BaseListResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$BaseListResponseFromJson(json, fromJsonT);
  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$BaseListResponseToJson(this, toJsonT);
}