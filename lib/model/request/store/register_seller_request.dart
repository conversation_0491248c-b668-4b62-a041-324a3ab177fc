import 'package:freezed_annotation/freezed_annotation.dart';

part 'register_seller_request.freezed.dart';
part 'register_seller_request.g.dart';

@freezed
class RegisterSellerRequest with _$RegisterSellerRequest {
  const factory RegisterSellerRequest({
    String? name,
    String? address,
    String? tel,
    String? email,
    String? img,
    String? responsible,
  }) = _RegisterSellerRequest;

  factory RegisterSellerRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterSellerRequestFromJson(json);
}

@freezed
class WarehousesBulkUpdateRequest with _$WarehousesBulkUpdateRequest {
  const factory WarehousesBulkUpdateRequest({
    required List<int> ids,
  }) = _WarehousesBulkUpdateRequest;

  factory WarehousesBulkUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$WarehousesBulkUpdateRequestFromJson(json);
}
