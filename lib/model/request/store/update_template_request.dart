import 'package:freezed_annotation/freezed_annotation.dart';

part 'update_template_request.freezed.dart';
part 'update_template_request.g.dart';

@freezed
class UpdateTemplateRequest with _$UpdateTemplateRequest {
  const factory UpdateTemplateRequest({
    required String name,
    required String description,
    @JsonKey(name: 'base_price') required double basePrice,
    @JsonKey(name: 'base_img') required String baseImg,
  }) = _UpdateTemplateRequest;

  factory UpdateTemplateRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateTemplateRequestFromJson(json);
}
