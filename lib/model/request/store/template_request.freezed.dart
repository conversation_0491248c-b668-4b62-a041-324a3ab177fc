// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'template_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TemplateRequest _$TemplateRequestFromJson(Map<String, dynamic> json) {
  return _TemplateRequest.fromJson(json);
}

/// @nodoc
mixin _$TemplateRequest {
  int? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'base_price')
  String? get basePrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'base_img')
  String? get baseImg => throw _privateConstructorUsedError;

  /// Serializes this TemplateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TemplateRequestCopyWith<TemplateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TemplateRequestCopyWith<$Res> {
  factory $TemplateRequestCopyWith(
          TemplateRequest value, $Res Function(TemplateRequest) then) =
      _$TemplateRequestCopyWithImpl<$Res, TemplateRequest>;
  @useResult
  $Res call(
      {int? id,
      String? name,
      String? description,
      @JsonKey(name: 'base_price') String? basePrice,
      @JsonKey(name: 'base_img') String? baseImg});
}

/// @nodoc
class _$TemplateRequestCopyWithImpl<$Res, $Val extends TemplateRequest>
    implements $TemplateRequestCopyWith<$Res> {
  _$TemplateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? basePrice = freezed,
    Object? baseImg = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      basePrice: freezed == basePrice
          ? _value.basePrice
          : basePrice // ignore: cast_nullable_to_non_nullable
              as String?,
      baseImg: freezed == baseImg
          ? _value.baseImg
          : baseImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TemplateRequestImplCopyWith<$Res>
    implements $TemplateRequestCopyWith<$Res> {
  factory _$$TemplateRequestImplCopyWith(_$TemplateRequestImpl value,
          $Res Function(_$TemplateRequestImpl) then) =
      __$$TemplateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? name,
      String? description,
      @JsonKey(name: 'base_price') String? basePrice,
      @JsonKey(name: 'base_img') String? baseImg});
}

/// @nodoc
class __$$TemplateRequestImplCopyWithImpl<$Res>
    extends _$TemplateRequestCopyWithImpl<$Res, _$TemplateRequestImpl>
    implements _$$TemplateRequestImplCopyWith<$Res> {
  __$$TemplateRequestImplCopyWithImpl(
      _$TemplateRequestImpl _value, $Res Function(_$TemplateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of TemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? basePrice = freezed,
    Object? baseImg = freezed,
  }) {
    return _then(_$TemplateRequestImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      basePrice: freezed == basePrice
          ? _value.basePrice
          : basePrice // ignore: cast_nullable_to_non_nullable
              as String?,
      baseImg: freezed == baseImg
          ? _value.baseImg
          : baseImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TemplateRequestImpl implements _TemplateRequest {
  const _$TemplateRequestImpl(
      {this.id,
      this.name,
      this.description,
      @JsonKey(name: 'base_price') this.basePrice,
      @JsonKey(name: 'base_img') this.baseImg});

  factory _$TemplateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$TemplateRequestImplFromJson(json);

  @override
  final int? id;
  @override
  final String? name;
  @override
  final String? description;
  @override
  @JsonKey(name: 'base_price')
  final String? basePrice;
  @override
  @JsonKey(name: 'base_img')
  final String? baseImg;

  @override
  String toString() {
    return 'TemplateRequest(id: $id, name: $name, description: $description, basePrice: $basePrice, baseImg: $baseImg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TemplateRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.basePrice, basePrice) ||
                other.basePrice == basePrice) &&
            (identical(other.baseImg, baseImg) || other.baseImg == baseImg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, description, basePrice, baseImg);

  /// Create a copy of TemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TemplateRequestImplCopyWith<_$TemplateRequestImpl> get copyWith =>
      __$$TemplateRequestImplCopyWithImpl<_$TemplateRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TemplateRequestImplToJson(
      this,
    );
  }
}

abstract class _TemplateRequest implements TemplateRequest {
  const factory _TemplateRequest(
          {final int? id,
          final String? name,
          final String? description,
          @JsonKey(name: 'base_price') final String? basePrice,
          @JsonKey(name: 'base_img') final String? baseImg}) =
      _$TemplateRequestImpl;

  factory _TemplateRequest.fromJson(Map<String, dynamic> json) =
      _$TemplateRequestImpl.fromJson;

  @override
  int? get id;
  @override
  String? get name;
  @override
  String? get description;
  @override
  @JsonKey(name: 'base_price')
  String? get basePrice;
  @override
  @JsonKey(name: 'base_img')
  String? get baseImg;

  /// Create a copy of TemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TemplateRequestImplCopyWith<_$TemplateRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
