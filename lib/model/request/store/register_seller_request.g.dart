// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_seller_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RegisterSellerRequestImpl _$$RegisterSellerRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$RegisterSellerRequestImpl(
      name: json['name'] as String?,
      address: json['address'] as String?,
      tel: json['tel'] as String?,
      email: json['email'] as String?,
      img: json['img'] as String?,
      responsible: json['responsible'] as String?,
    );

Map<String, dynamic> _$$RegisterSellerRequestImplToJson(
        _$RegisterSellerRequestImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'address': instance.address,
      'tel': instance.tel,
      'email': instance.email,
      'img': instance.img,
      'responsible': instance.responsible,
    };

_$WarehousesBulkUpdateRequestImpl _$$WarehousesBulkUpdateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$WarehousesBulkUpdateRequestImpl(
      ids: (json['ids'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$$WarehousesBulkUpdateRequestImplToJson(
        _$WarehousesBulkUpdateRequestImpl instance) =>
    <String, dynamic>{
      'ids': instance.ids,
    };
