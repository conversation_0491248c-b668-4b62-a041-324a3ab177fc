// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_seller_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RegisterSellerRequest _$RegisterSellerRequestFromJson(
    Map<String, dynamic> json) {
  return _RegisterSellerRequest.fromJson(json);
}

/// @nodoc
mixin _$RegisterSellerRequest {
  String? get name => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get tel => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  String? get responsible => throw _privateConstructorUsedError;

  /// Serializes this RegisterSellerRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RegisterSellerRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RegisterSellerRequestCopyWith<RegisterSellerRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterSellerRequestCopyWith<$Res> {
  factory $RegisterSellerRequestCopyWith(RegisterSellerRequest value,
          $Res Function(RegisterSellerRequest) then) =
      _$RegisterSellerRequestCopyWithImpl<$Res, RegisterSellerRequest>;
  @useResult
  $Res call(
      {String? name,
      String? address,
      String? tel,
      String? email,
      String? img,
      String? responsible});
}

/// @nodoc
class _$RegisterSellerRequestCopyWithImpl<$Res,
        $Val extends RegisterSellerRequest>
    implements $RegisterSellerRequestCopyWith<$Res> {
  _$RegisterSellerRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RegisterSellerRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? address = freezed,
    Object? tel = freezed,
    Object? email = freezed,
    Object? img = freezed,
    Object? responsible = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      tel: freezed == tel
          ? _value.tel
          : tel // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      responsible: freezed == responsible
          ? _value.responsible
          : responsible // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegisterSellerRequestImplCopyWith<$Res>
    implements $RegisterSellerRequestCopyWith<$Res> {
  factory _$$RegisterSellerRequestImplCopyWith(
          _$RegisterSellerRequestImpl value,
          $Res Function(_$RegisterSellerRequestImpl) then) =
      __$$RegisterSellerRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? name,
      String? address,
      String? tel,
      String? email,
      String? img,
      String? responsible});
}

/// @nodoc
class __$$RegisterSellerRequestImplCopyWithImpl<$Res>
    extends _$RegisterSellerRequestCopyWithImpl<$Res,
        _$RegisterSellerRequestImpl>
    implements _$$RegisterSellerRequestImplCopyWith<$Res> {
  __$$RegisterSellerRequestImplCopyWithImpl(_$RegisterSellerRequestImpl _value,
      $Res Function(_$RegisterSellerRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegisterSellerRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? address = freezed,
    Object? tel = freezed,
    Object? email = freezed,
    Object? img = freezed,
    Object? responsible = freezed,
  }) {
    return _then(_$RegisterSellerRequestImpl(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      tel: freezed == tel
          ? _value.tel
          : tel // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      responsible: freezed == responsible
          ? _value.responsible
          : responsible // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RegisterSellerRequestImpl implements _RegisterSellerRequest {
  const _$RegisterSellerRequestImpl(
      {this.name,
      this.address,
      this.tel,
      this.email,
      this.img,
      this.responsible});

  factory _$RegisterSellerRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$RegisterSellerRequestImplFromJson(json);

  @override
  final String? name;
  @override
  final String? address;
  @override
  final String? tel;
  @override
  final String? email;
  @override
  final String? img;
  @override
  final String? responsible;

  @override
  String toString() {
    return 'RegisterSellerRequest(name: $name, address: $address, tel: $tel, email: $email, img: $img, responsible: $responsible)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterSellerRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.tel, tel) || other.tel == tel) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.responsible, responsible) ||
                other.responsible == responsible));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, address, tel, email, img, responsible);

  /// Create a copy of RegisterSellerRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterSellerRequestImplCopyWith<_$RegisterSellerRequestImpl>
      get copyWith => __$$RegisterSellerRequestImplCopyWithImpl<
          _$RegisterSellerRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RegisterSellerRequestImplToJson(
      this,
    );
  }
}

abstract class _RegisterSellerRequest implements RegisterSellerRequest {
  const factory _RegisterSellerRequest(
      {final String? name,
      final String? address,
      final String? tel,
      final String? email,
      final String? img,
      final String? responsible}) = _$RegisterSellerRequestImpl;

  factory _RegisterSellerRequest.fromJson(Map<String, dynamic> json) =
      _$RegisterSellerRequestImpl.fromJson;

  @override
  String? get name;
  @override
  String? get address;
  @override
  String? get tel;
  @override
  String? get email;
  @override
  String? get img;
  @override
  String? get responsible;

  /// Create a copy of RegisterSellerRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterSellerRequestImplCopyWith<_$RegisterSellerRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

WarehousesBulkUpdateRequest _$WarehousesBulkUpdateRequestFromJson(
    Map<String, dynamic> json) {
  return _WarehousesBulkUpdateRequest.fromJson(json);
}

/// @nodoc
mixin _$WarehousesBulkUpdateRequest {
  List<int> get ids => throw _privateConstructorUsedError;

  /// Serializes this WarehousesBulkUpdateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehousesBulkUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehousesBulkUpdateRequestCopyWith<WarehousesBulkUpdateRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehousesBulkUpdateRequestCopyWith<$Res> {
  factory $WarehousesBulkUpdateRequestCopyWith(
          WarehousesBulkUpdateRequest value,
          $Res Function(WarehousesBulkUpdateRequest) then) =
      _$WarehousesBulkUpdateRequestCopyWithImpl<$Res,
          WarehousesBulkUpdateRequest>;
  @useResult
  $Res call({List<int> ids});
}

/// @nodoc
class _$WarehousesBulkUpdateRequestCopyWithImpl<$Res,
        $Val extends WarehousesBulkUpdateRequest>
    implements $WarehousesBulkUpdateRequestCopyWith<$Res> {
  _$WarehousesBulkUpdateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehousesBulkUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ids = null,
  }) {
    return _then(_value.copyWith(
      ids: null == ids
          ? _value.ids
          : ids // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehousesBulkUpdateRequestImplCopyWith<$Res>
    implements $WarehousesBulkUpdateRequestCopyWith<$Res> {
  factory _$$WarehousesBulkUpdateRequestImplCopyWith(
          _$WarehousesBulkUpdateRequestImpl value,
          $Res Function(_$WarehousesBulkUpdateRequestImpl) then) =
      __$$WarehousesBulkUpdateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<int> ids});
}

/// @nodoc
class __$$WarehousesBulkUpdateRequestImplCopyWithImpl<$Res>
    extends _$WarehousesBulkUpdateRequestCopyWithImpl<$Res,
        _$WarehousesBulkUpdateRequestImpl>
    implements _$$WarehousesBulkUpdateRequestImplCopyWith<$Res> {
  __$$WarehousesBulkUpdateRequestImplCopyWithImpl(
      _$WarehousesBulkUpdateRequestImpl _value,
      $Res Function(_$WarehousesBulkUpdateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehousesBulkUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ids = null,
  }) {
    return _then(_$WarehousesBulkUpdateRequestImpl(
      ids: null == ids
          ? _value._ids
          : ids // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WarehousesBulkUpdateRequestImpl
    implements _WarehousesBulkUpdateRequest {
  const _$WarehousesBulkUpdateRequestImpl({required final List<int> ids})
      : _ids = ids;

  factory _$WarehousesBulkUpdateRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$WarehousesBulkUpdateRequestImplFromJson(json);

  final List<int> _ids;
  @override
  List<int> get ids {
    if (_ids is EqualUnmodifiableListView) return _ids;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_ids);
  }

  @override
  String toString() {
    return 'WarehousesBulkUpdateRequest(ids: $ids)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehousesBulkUpdateRequestImpl &&
            const DeepCollectionEquality().equals(other._ids, _ids));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_ids));

  /// Create a copy of WarehousesBulkUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehousesBulkUpdateRequestImplCopyWith<_$WarehousesBulkUpdateRequestImpl>
      get copyWith => __$$WarehousesBulkUpdateRequestImplCopyWithImpl<
          _$WarehousesBulkUpdateRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehousesBulkUpdateRequestImplToJson(
      this,
    );
  }
}

abstract class _WarehousesBulkUpdateRequest
    implements WarehousesBulkUpdateRequest {
  const factory _WarehousesBulkUpdateRequest({required final List<int> ids}) =
      _$WarehousesBulkUpdateRequestImpl;

  factory _WarehousesBulkUpdateRequest.fromJson(Map<String, dynamic> json) =
      _$WarehousesBulkUpdateRequestImpl.fromJson;

  @override
  List<int> get ids;

  /// Create a copy of WarehousesBulkUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehousesBulkUpdateRequestImplCopyWith<_$WarehousesBulkUpdateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
