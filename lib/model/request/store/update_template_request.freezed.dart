// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_template_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UpdateTemplateRequest _$UpdateTemplateRequestFromJson(
    Map<String, dynamic> json) {
  return _UpdateTemplateRequest.fromJson(json);
}

/// @nodoc
mixin _$UpdateTemplateRequest {
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'base_price')
  double get basePrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'base_img')
  String get baseImg => throw _privateConstructorUsedError;

  /// Serializes this UpdateTemplateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateTemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateTemplateRequestCopyWith<UpdateTemplateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateTemplateRequestCopyWith<$Res> {
  factory $UpdateTemplateRequestCopyWith(UpdateTemplateRequest value,
          $Res Function(UpdateTemplateRequest) then) =
      _$UpdateTemplateRequestCopyWithImpl<$Res, UpdateTemplateRequest>;
  @useResult
  $Res call(
      {String name,
      String description,
      @JsonKey(name: 'base_price') double basePrice,
      @JsonKey(name: 'base_img') String baseImg});
}

/// @nodoc
class _$UpdateTemplateRequestCopyWithImpl<$Res,
        $Val extends UpdateTemplateRequest>
    implements $UpdateTemplateRequestCopyWith<$Res> {
  _$UpdateTemplateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateTemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? basePrice = null,
    Object? baseImg = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      basePrice: null == basePrice
          ? _value.basePrice
          : basePrice // ignore: cast_nullable_to_non_nullable
              as double,
      baseImg: null == baseImg
          ? _value.baseImg
          : baseImg // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateTemplateRequestImplCopyWith<$Res>
    implements $UpdateTemplateRequestCopyWith<$Res> {
  factory _$$UpdateTemplateRequestImplCopyWith(
          _$UpdateTemplateRequestImpl value,
          $Res Function(_$UpdateTemplateRequestImpl) then) =
      __$$UpdateTemplateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String description,
      @JsonKey(name: 'base_price') double basePrice,
      @JsonKey(name: 'base_img') String baseImg});
}

/// @nodoc
class __$$UpdateTemplateRequestImplCopyWithImpl<$Res>
    extends _$UpdateTemplateRequestCopyWithImpl<$Res,
        _$UpdateTemplateRequestImpl>
    implements _$$UpdateTemplateRequestImplCopyWith<$Res> {
  __$$UpdateTemplateRequestImplCopyWithImpl(_$UpdateTemplateRequestImpl _value,
      $Res Function(_$UpdateTemplateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateTemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? basePrice = null,
    Object? baseImg = null,
  }) {
    return _then(_$UpdateTemplateRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      basePrice: null == basePrice
          ? _value.basePrice
          : basePrice // ignore: cast_nullable_to_non_nullable
              as double,
      baseImg: null == baseImg
          ? _value.baseImg
          : baseImg // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateTemplateRequestImpl implements _UpdateTemplateRequest {
  const _$UpdateTemplateRequestImpl(
      {required this.name,
      required this.description,
      @JsonKey(name: 'base_price') required this.basePrice,
      @JsonKey(name: 'base_img') required this.baseImg});

  factory _$UpdateTemplateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateTemplateRequestImplFromJson(json);

  @override
  final String name;
  @override
  final String description;
  @override
  @JsonKey(name: 'base_price')
  final double basePrice;
  @override
  @JsonKey(name: 'base_img')
  final String baseImg;

  @override
  String toString() {
    return 'UpdateTemplateRequest(name: $name, description: $description, basePrice: $basePrice, baseImg: $baseImg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateTemplateRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.basePrice, basePrice) ||
                other.basePrice == basePrice) &&
            (identical(other.baseImg, baseImg) || other.baseImg == baseImg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, description, basePrice, baseImg);

  /// Create a copy of UpdateTemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateTemplateRequestImplCopyWith<_$UpdateTemplateRequestImpl>
      get copyWith => __$$UpdateTemplateRequestImplCopyWithImpl<
          _$UpdateTemplateRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateTemplateRequestImplToJson(
      this,
    );
  }
}

abstract class _UpdateTemplateRequest implements UpdateTemplateRequest {
  const factory _UpdateTemplateRequest(
          {required final String name,
          required final String description,
          @JsonKey(name: 'base_price') required final double basePrice,
          @JsonKey(name: 'base_img') required final String baseImg}) =
      _$UpdateTemplateRequestImpl;

  factory _UpdateTemplateRequest.fromJson(Map<String, dynamic> json) =
      _$UpdateTemplateRequestImpl.fromJson;

  @override
  String get name;
  @override
  String get description;
  @override
  @JsonKey(name: 'base_price')
  double get basePrice;
  @override
  @JsonKey(name: 'base_img')
  String get baseImg;

  /// Create a copy of UpdateTemplateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateTemplateRequestImplCopyWith<_$UpdateTemplateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
