import 'package:freezed_annotation/freezed_annotation.dart';

part 'template_request.freezed.dart';
part 'template_request.g.dart';

@freezed
class TemplateRequest with _$TemplateRequest {
  const factory TemplateRequest({
    int? id,
    String? name,
    String? description,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'base_price') String? basePrice,
    @J<PERSON><PERSON>ey(name: 'base_img') String? baseImg,
  }) = _TemplateRequest;

  factory TemplateRequest.fromJson(Map<String, dynamic> json) =>
      _$TemplateRequestFromJson(json);
}
