// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_order_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderProductImpl _$$OrderProductImplFromJson(Map<String, dynamic> json) =>
    _$OrderProductImpl(
      id: (json['id'] as num).toInt(),
      img: json['img'] as String,
      itemId: json['itemId'] as String,
    );

Map<String, dynamic> _$$OrderProductImplToJson(_$OrderProductImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'img': instance.img,
      'itemId': instance.itemId,
    };

_$DraftOrderImpl _$$DraftOrderImplFromJson(Map<String, dynamic> json) =>
    _$DraftOrderImpl(
      userId: (json['user_id'] as num).toInt(),
      accessId: json['access_id'] as String?,
      transactionId: json['transaction_id'] as String?,
      totalPaymentAmount: (json['total_payment_amount'] as num).toDouble(),
      totalUsagePoint: (json['total_usage_point'] as num).toInt(),
      actualPaymentAmount: (json['actual_payment_amount'] as num).toInt(),
      status: json['status'] as String,
      orderAt: json['order_at'] as String,
    );

Map<String, dynamic> _$$DraftOrderImplToJson(_$DraftOrderImpl instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'access_id': instance.accessId,
      'transaction_id': instance.transactionId,
      'total_payment_amount': instance.totalPaymentAmount,
      'total_usage_point': instance.totalUsagePoint,
      'actual_payment_amount': instance.actualPaymentAmount,
      'status': instance.status,
      'order_at': instance.orderAt,
    };

_$CreateOrderRequestImpl _$$CreateOrderRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateOrderRequestImpl(
      products: (json['products'] as List<dynamic>)
          .map((e) => OrderProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
      draftOrder:
          DraftOrder.fromJson(json['draft_order'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CreateOrderRequestImplToJson(
        _$CreateOrderRequestImpl instance) =>
    <String, dynamic>{
      'products': instance.products,
      'draft_order': instance.draftOrder,
    };
