import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_order_request.freezed.dart';
part 'create_order_request.g.dart';

@freezed
class OrderProduct with _$OrderProduct {
  const factory OrderProduct({
    required int id,
    required String img,
    required String itemId,
  }) = _OrderProduct;

  factory OrderProduct.fromJson(Map<String, dynamic> json) =>
      _$OrderProductFromJson(json);
}

@freezed
class DraftOrder with _$DraftOrder {
  const factory DraftOrder({
    @JsonKey(name: 'user_id') required int userId,
    @Json<PERSON>ey(name: 'access_id') String? accessId,
    @Json<PERSON>ey(name: 'transaction_id') String? transactionId,
    @J<PERSON><PERSON><PERSON>(name: 'total_payment_amount') required double totalPaymentAmount,
    @JsonKey(name: 'total_usage_point') required int totalUsagePoint,
    @JsonKey(name: 'actual_payment_amount') required int actualPaymentAmount,
    required String status,
    @JsonKey(name: 'order_at') required String orderAt,
  }) = _DraftOrder;

  factory DraftOrder.fromJson(Map<String, dynamic> json) =>
      _$DraftOrderFromJson(json);
}

@freezed
class CreateOrderRequest with _$CreateOrderRequest {
  const factory CreateOrderRequest({
    required List<OrderProduct> products,
    @JsonKey(name: 'draft_order') required DraftOrder draftOrder,
  }) = _CreateOrderRequest;

  factory CreateOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderRequestFromJson(json);
}
