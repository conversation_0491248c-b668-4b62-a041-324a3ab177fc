import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_event_request.freezed.dart';
part 'create_event_request.g.dart';

@freezed
class CreateEventRequest with _$CreateEventRequest {
  const factory CreateEventRequest({
    @Json<PERSON>ey(name: 'name') required String title,
    @<PERSON><PERSON><PERSON>ey(name: 'content') required String description,
    @Json<PERSON>ey(name: 'start_date') required DateTime startDate,
    @Json<PERSON>ey(name: 'end_date') required DateTime endDate,
    @<PERSON>son<PERSON><PERSON>(name: 'address') required String location,
    required String latitude,
    required String longitude,
    @J<PERSON><PERSON>ey(name: 'img') String? imageUrl,
    List<String>? tags,
  }) = _CreateEventRequest;

  factory CreateEventRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateEventRequestFromJson(json);
}
