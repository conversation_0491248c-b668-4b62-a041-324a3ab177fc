// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_event_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateEventRequest _$CreateEventRequestFromJson(Map<String, dynamic> json) {
  return _CreateEventRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateEventRequest {
  @JsonKey(name: 'name')
  String get title => throw _privateConstructorUsedError;
  @JsonKey(name: 'content')
  String get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'start_date')
  DateTime get startDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'end_date')
  DateTime get endDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'address')
  String get location => throw _privateConstructorUsedError;
  String get latitude => throw _privateConstructorUsedError;
  String get longitude => throw _privateConstructorUsedError;
  @JsonKey(name: 'img')
  String? get imageUrl => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;

  /// Serializes this CreateEventRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateEventRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateEventRequestCopyWith<CreateEventRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateEventRequestCopyWith<$Res> {
  factory $CreateEventRequestCopyWith(
          CreateEventRequest value, $Res Function(CreateEventRequest) then) =
      _$CreateEventRequestCopyWithImpl<$Res, CreateEventRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'name') String title,
      @JsonKey(name: 'content') String description,
      @JsonKey(name: 'start_date') DateTime startDate,
      @JsonKey(name: 'end_date') DateTime endDate,
      @JsonKey(name: 'address') String location,
      String latitude,
      String longitude,
      @JsonKey(name: 'img') String? imageUrl,
      List<String>? tags});
}

/// @nodoc
class _$CreateEventRequestCopyWithImpl<$Res, $Val extends CreateEventRequest>
    implements $CreateEventRequestCopyWith<$Res> {
  _$CreateEventRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateEventRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? location = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? imageUrl = freezed,
    Object? tags = freezed,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as String,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateEventRequestImplCopyWith<$Res>
    implements $CreateEventRequestCopyWith<$Res> {
  factory _$$CreateEventRequestImplCopyWith(_$CreateEventRequestImpl value,
          $Res Function(_$CreateEventRequestImpl) then) =
      __$$CreateEventRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'name') String title,
      @JsonKey(name: 'content') String description,
      @JsonKey(name: 'start_date') DateTime startDate,
      @JsonKey(name: 'end_date') DateTime endDate,
      @JsonKey(name: 'address') String location,
      String latitude,
      String longitude,
      @JsonKey(name: 'img') String? imageUrl,
      List<String>? tags});
}

/// @nodoc
class __$$CreateEventRequestImplCopyWithImpl<$Res>
    extends _$CreateEventRequestCopyWithImpl<$Res, _$CreateEventRequestImpl>
    implements _$$CreateEventRequestImplCopyWith<$Res> {
  __$$CreateEventRequestImplCopyWithImpl(_$CreateEventRequestImpl _value,
      $Res Function(_$CreateEventRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateEventRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? location = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? imageUrl = freezed,
    Object? tags = freezed,
  }) {
    return _then(_$CreateEventRequestImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as String,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateEventRequestImpl implements _CreateEventRequest {
  const _$CreateEventRequestImpl(
      {@JsonKey(name: 'name') required this.title,
      @JsonKey(name: 'content') required this.description,
      @JsonKey(name: 'start_date') required this.startDate,
      @JsonKey(name: 'end_date') required this.endDate,
      @JsonKey(name: 'address') required this.location,
      required this.latitude,
      required this.longitude,
      @JsonKey(name: 'img') this.imageUrl,
      final List<String>? tags})
      : _tags = tags;

  factory _$CreateEventRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateEventRequestImplFromJson(json);

  @override
  @JsonKey(name: 'name')
  final String title;
  @override
  @JsonKey(name: 'content')
  final String description;
  @override
  @JsonKey(name: 'start_date')
  final DateTime startDate;
  @override
  @JsonKey(name: 'end_date')
  final DateTime endDate;
  @override
  @JsonKey(name: 'address')
  final String location;
  @override
  final String latitude;
  @override
  final String longitude;
  @override
  @JsonKey(name: 'img')
  final String? imageUrl;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CreateEventRequest(title: $title, description: $description, startDate: $startDate, endDate: $endDate, location: $location, latitude: $latitude, longitude: $longitude, imageUrl: $imageUrl, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateEventRequestImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality().equals(other._tags, _tags));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      title,
      description,
      startDate,
      endDate,
      location,
      latitude,
      longitude,
      imageUrl,
      const DeepCollectionEquality().hash(_tags));

  /// Create a copy of CreateEventRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateEventRequestImplCopyWith<_$CreateEventRequestImpl> get copyWith =>
      __$$CreateEventRequestImplCopyWithImpl<_$CreateEventRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateEventRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateEventRequest implements CreateEventRequest {
  const factory _CreateEventRequest(
      {@JsonKey(name: 'name') required final String title,
      @JsonKey(name: 'content') required final String description,
      @JsonKey(name: 'start_date') required final DateTime startDate,
      @JsonKey(name: 'end_date') required final DateTime endDate,
      @JsonKey(name: 'address') required final String location,
      required final String latitude,
      required final String longitude,
      @JsonKey(name: 'img') final String? imageUrl,
      final List<String>? tags}) = _$CreateEventRequestImpl;

  factory _CreateEventRequest.fromJson(Map<String, dynamic> json) =
      _$CreateEventRequestImpl.fromJson;

  @override
  @JsonKey(name: 'name')
  String get title;
  @override
  @JsonKey(name: 'content')
  String get description;
  @override
  @JsonKey(name: 'start_date')
  DateTime get startDate;
  @override
  @JsonKey(name: 'end_date')
  DateTime get endDate;
  @override
  @JsonKey(name: 'address')
  String get location;
  @override
  String get latitude;
  @override
  String get longitude;
  @override
  @JsonKey(name: 'img')
  String? get imageUrl;
  @override
  List<String>? get tags;

  /// Create a copy of CreateEventRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateEventRequestImplCopyWith<_$CreateEventRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
