// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_product_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProductImageRegister _$ProductImageRegisterFromJson(Map<String, dynamic> json) {
  return _ProductImageRegister.fromJson(json);
}

/// @nodoc
mixin _$ProductImageRegister {
  String get path => throw _privateConstructorUsedError;

  /// Serializes this ProductImageRegister to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductImageRegister
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductImageRegisterCopyWith<ProductImageRegister> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductImageRegisterCopyWith<$Res> {
  factory $ProductImageRegisterCopyWith(ProductImageRegister value,
          $Res Function(ProductImageRegister) then) =
      _$ProductImageRegisterCopyWithImpl<$Res, ProductImageRegister>;
  @useResult
  $Res call({String path});
}

/// @nodoc
class _$ProductImageRegisterCopyWithImpl<$Res,
        $Val extends ProductImageRegister>
    implements $ProductImageRegisterCopyWith<$Res> {
  _$ProductImageRegisterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductImageRegister
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? path = null,
  }) {
    return _then(_value.copyWith(
      path: null == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductImageRegisterImplCopyWith<$Res>
    implements $ProductImageRegisterCopyWith<$Res> {
  factory _$$ProductImageRegisterImplCopyWith(_$ProductImageRegisterImpl value,
          $Res Function(_$ProductImageRegisterImpl) then) =
      __$$ProductImageRegisterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String path});
}

/// @nodoc
class __$$ProductImageRegisterImplCopyWithImpl<$Res>
    extends _$ProductImageRegisterCopyWithImpl<$Res, _$ProductImageRegisterImpl>
    implements _$$ProductImageRegisterImplCopyWith<$Res> {
  __$$ProductImageRegisterImplCopyWithImpl(_$ProductImageRegisterImpl _value,
      $Res Function(_$ProductImageRegisterImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductImageRegister
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? path = null,
  }) {
    return _then(_$ProductImageRegisterImpl(
      path: null == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductImageRegisterImpl implements _ProductImageRegister {
  const _$ProductImageRegisterImpl({required this.path});

  factory _$ProductImageRegisterImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductImageRegisterImplFromJson(json);

  @override
  final String path;

  @override
  String toString() {
    return 'ProductImageRegister(path: $path)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductImageRegisterImpl &&
            (identical(other.path, path) || other.path == path));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, path);

  /// Create a copy of ProductImageRegister
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductImageRegisterImplCopyWith<_$ProductImageRegisterImpl>
      get copyWith =>
          __$$ProductImageRegisterImplCopyWithImpl<_$ProductImageRegisterImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductImageRegisterImplToJson(
      this,
    );
  }
}

abstract class _ProductImageRegister implements ProductImageRegister {
  const factory _ProductImageRegister({required final String path}) =
      _$ProductImageRegisterImpl;

  factory _ProductImageRegister.fromJson(Map<String, dynamic> json) =
      _$ProductImageRegisterImpl.fromJson;

  @override
  String get path;

  /// Create a copy of ProductImageRegister
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductImageRegisterImplCopyWith<_$ProductImageRegisterImpl>
      get copyWith => throw _privateConstructorUsedError;
}

RegisterProductRequest _$RegisterProductRequestFromJson(
    Map<String, dynamic> json) {
  return _RegisterProductRequest.fromJson(json);
}

/// @nodoc
mixin _$RegisterProductRequest {
  @JsonKey(name: 'shelf_id')
  int? get shelfId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;
  String? get note => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'expiration_date')
  String? get expirationDate => throw _privateConstructorUsedError;
  String get capacity => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  @JsonKey(name: 'sale_price')
  double get salePrice => throw _privateConstructorUsedError;
  String get img => throw _privateConstructorUsedError;
  List<String>? get tag => throw _privateConstructorUsedError;
  List<ProductImageRegister> get images => throw _privateConstructorUsedError;

  /// Serializes this RegisterProductRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RegisterProductRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RegisterProductRequestCopyWith<RegisterProductRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterProductRequestCopyWith<$Res> {
  factory $RegisterProductRequestCopyWith(RegisterProductRequest value,
          $Res Function(RegisterProductRequest) then) =
      _$RegisterProductRequestCopyWithImpl<$Res, RegisterProductRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'shelf_id') int? shelfId,
      String name,
      String? description,
      String? comment,
      String? note,
      int quantity,
      @JsonKey(name: 'expiration_date') String? expirationDate,
      String capacity,
      double price,
      @JsonKey(name: 'sale_price') double salePrice,
      String img,
      List<String>? tag,
      List<ProductImageRegister> images});
}

/// @nodoc
class _$RegisterProductRequestCopyWithImpl<$Res,
        $Val extends RegisterProductRequest>
    implements $RegisterProductRequestCopyWith<$Res> {
  _$RegisterProductRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RegisterProductRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shelfId = freezed,
    Object? name = null,
    Object? description = freezed,
    Object? comment = freezed,
    Object? note = freezed,
    Object? quantity = null,
    Object? expirationDate = freezed,
    Object? capacity = null,
    Object? price = null,
    Object? salePrice = null,
    Object? img = null,
    Object? tag = freezed,
    Object? images = null,
  }) {
    return _then(_value.copyWith(
      shelfId: freezed == shelfId
          ? _value.shelfId
          : shelfId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
      capacity: null == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      salePrice: null == salePrice
          ? _value.salePrice
          : salePrice // ignore: cast_nullable_to_non_nullable
              as double,
      img: null == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      images: null == images
          ? _value.images
          : images // ignore: cast_nullable_to_non_nullable
              as List<ProductImageRegister>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegisterProductRequestImplCopyWith<$Res>
    implements $RegisterProductRequestCopyWith<$Res> {
  factory _$$RegisterProductRequestImplCopyWith(
          _$RegisterProductRequestImpl value,
          $Res Function(_$RegisterProductRequestImpl) then) =
      __$$RegisterProductRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'shelf_id') int? shelfId,
      String name,
      String? description,
      String? comment,
      String? note,
      int quantity,
      @JsonKey(name: 'expiration_date') String? expirationDate,
      String capacity,
      double price,
      @JsonKey(name: 'sale_price') double salePrice,
      String img,
      List<String>? tag,
      List<ProductImageRegister> images});
}

/// @nodoc
class __$$RegisterProductRequestImplCopyWithImpl<$Res>
    extends _$RegisterProductRequestCopyWithImpl<$Res,
        _$RegisterProductRequestImpl>
    implements _$$RegisterProductRequestImplCopyWith<$Res> {
  __$$RegisterProductRequestImplCopyWithImpl(
      _$RegisterProductRequestImpl _value,
      $Res Function(_$RegisterProductRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegisterProductRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shelfId = freezed,
    Object? name = null,
    Object? description = freezed,
    Object? comment = freezed,
    Object? note = freezed,
    Object? quantity = null,
    Object? expirationDate = freezed,
    Object? capacity = null,
    Object? price = null,
    Object? salePrice = null,
    Object? img = null,
    Object? tag = freezed,
    Object? images = null,
  }) {
    return _then(_$RegisterProductRequestImpl(
      shelfId: freezed == shelfId
          ? _value.shelfId
          : shelfId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
      capacity: null == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      salePrice: null == salePrice
          ? _value.salePrice
          : salePrice // ignore: cast_nullable_to_non_nullable
              as double,
      img: null == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String,
      tag: freezed == tag
          ? _value._tag
          : tag // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      images: null == images
          ? _value._images
          : images // ignore: cast_nullable_to_non_nullable
              as List<ProductImageRegister>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RegisterProductRequestImpl implements _RegisterProductRequest {
  const _$RegisterProductRequestImpl(
      {@JsonKey(name: 'shelf_id') this.shelfId,
      required this.name,
      this.description,
      this.comment,
      this.note,
      required this.quantity,
      @JsonKey(name: 'expiration_date') this.expirationDate,
      required this.capacity,
      required this.price,
      @JsonKey(name: 'sale_price') required this.salePrice,
      required this.img,
      final List<String>? tag,
      required final List<ProductImageRegister> images})
      : _tag = tag,
        _images = images;

  factory _$RegisterProductRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$RegisterProductRequestImplFromJson(json);

  @override
  @JsonKey(name: 'shelf_id')
  final int? shelfId;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String? comment;
  @override
  final String? note;
  @override
  final int quantity;
  @override
  @JsonKey(name: 'expiration_date')
  final String? expirationDate;
  @override
  final String capacity;
  @override
  final double price;
  @override
  @JsonKey(name: 'sale_price')
  final double salePrice;
  @override
  final String img;
  final List<String>? _tag;
  @override
  List<String>? get tag {
    final value = _tag;
    if (value == null) return null;
    if (_tag is EqualUnmodifiableListView) return _tag;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductImageRegister> _images;
  @override
  List<ProductImageRegister> get images {
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_images);
  }

  @override
  String toString() {
    return 'RegisterProductRequest(shelfId: $shelfId, name: $name, description: $description, comment: $comment, note: $note, quantity: $quantity, expirationDate: $expirationDate, capacity: $capacity, price: $price, salePrice: $salePrice, img: $img, tag: $tag, images: $images)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterProductRequestImpl &&
            (identical(other.shelfId, shelfId) || other.shelfId == shelfId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.expirationDate, expirationDate) ||
                other.expirationDate == expirationDate) &&
            (identical(other.capacity, capacity) ||
                other.capacity == capacity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.salePrice, salePrice) ||
                other.salePrice == salePrice) &&
            (identical(other.img, img) || other.img == img) &&
            const DeepCollectionEquality().equals(other._tag, _tag) &&
            const DeepCollectionEquality().equals(other._images, _images));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      shelfId,
      name,
      description,
      comment,
      note,
      quantity,
      expirationDate,
      capacity,
      price,
      salePrice,
      img,
      const DeepCollectionEquality().hash(_tag),
      const DeepCollectionEquality().hash(_images));

  /// Create a copy of RegisterProductRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterProductRequestImplCopyWith<_$RegisterProductRequestImpl>
      get copyWith => __$$RegisterProductRequestImplCopyWithImpl<
          _$RegisterProductRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RegisterProductRequestImplToJson(
      this,
    );
  }
}

abstract class _RegisterProductRequest implements RegisterProductRequest {
  const factory _RegisterProductRequest(
          {@JsonKey(name: 'shelf_id') final int? shelfId,
          required final String name,
          final String? description,
          final String? comment,
          final String? note,
          required final int quantity,
          @JsonKey(name: 'expiration_date') final String? expirationDate,
          required final String capacity,
          required final double price,
          @JsonKey(name: 'sale_price') required final double salePrice,
          required final String img,
          final List<String>? tag,
          required final List<ProductImageRegister> images}) =
      _$RegisterProductRequestImpl;

  factory _RegisterProductRequest.fromJson(Map<String, dynamic> json) =
      _$RegisterProductRequestImpl.fromJson;

  @override
  @JsonKey(name: 'shelf_id')
  int? get shelfId;
  @override
  String get name;
  @override
  String? get description;
  @override
  String? get comment;
  @override
  String? get note;
  @override
  int get quantity;
  @override
  @JsonKey(name: 'expiration_date')
  String? get expirationDate;
  @override
  String get capacity;
  @override
  double get price;
  @override
  @JsonKey(name: 'sale_price')
  double get salePrice;
  @override
  String get img;
  @override
  List<String>? get tag;
  @override
  List<ProductImageRegister> get images;

  /// Create a copy of RegisterProductRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterProductRequestImplCopyWith<_$RegisterProductRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
