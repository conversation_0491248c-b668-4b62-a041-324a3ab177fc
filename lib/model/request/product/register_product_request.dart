import 'package:freezed_annotation/freezed_annotation.dart';

part 'register_product_request.freezed.dart';
part 'register_product_request.g.dart';

@freezed
class ProductImageRegister with _$ProductImageRegister {
  const factory ProductImageRegister({
    required String path,
  }) = _ProductImageRegister;

  factory ProductImageRegister.fromJson(Map<String, dynamic> json) =>
      _$ProductImageRegisterFromJson(json);
}

@freezed
class RegisterProductRequest with _$RegisterProductRequest {
  const factory RegisterProductRequest({
    @JsonKey(name: 'shelf_id')  int? shelfId,
    required String name,
     String? description,
     String? comment,
     String? note,
    required int quantity,
    @JsonKey(name: 'expiration_date') String? expirationDate,
    required String capacity,
    required double price,
    @JsonKey(name: 'sale_price') required double salePrice,
    required String img,
     List<String>? tag,
    required List<ProductImageRegister> images,
  }) = _RegisterProductRequest;

  factory RegisterProductRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterProductRequestFromJson(json);
}
