// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_product_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductImageRegisterImpl _$$ProductImageRegisterImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductImageRegisterImpl(
      path: json['path'] as String,
    );

Map<String, dynamic> _$$ProductImageRegisterImplToJson(
        _$ProductImageRegisterImpl instance) =>
    <String, dynamic>{
      'path': instance.path,
    };

_$RegisterProductRequestImpl _$$RegisterProductRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$RegisterProductRequestImpl(
      shelfId: (json['shelf_id'] as num?)?.toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      comment: json['comment'] as String?,
      note: json['note'] as String?,
      quantity: (json['quantity'] as num).toInt(),
      expirationDate: json['expiration_date'] as String?,
      capacity: json['capacity'] as String,
      price: (json['price'] as num).toDouble(),
      salePrice: (json['sale_price'] as num).toDouble(),
      img: json['img'] as String,
      tag: (json['tag'] as List<dynamic>?)?.map((e) => e as String).toList(),
      images: (json['images'] as List<dynamic>)
          .map((e) => ProductImageRegister.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$RegisterProductRequestImplToJson(
        _$RegisterProductRequestImpl instance) =>
    <String, dynamic>{
      'shelf_id': instance.shelfId,
      'name': instance.name,
      'description': instance.description,
      'comment': instance.comment,
      'note': instance.note,
      'quantity': instance.quantity,
      'expiration_date': instance.expirationDate,
      'capacity': instance.capacity,
      'price': instance.price,
      'sale_price': instance.salePrice,
      'img': instance.img,
      'tag': instance.tag,
      'images': instance.images,
    };
