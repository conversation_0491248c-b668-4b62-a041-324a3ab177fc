import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';

part 'event_comment_model.freezed.dart';
part 'event_comment_model.g.dart';

@freezed
class EventCommentModel with _$EventCommentModel {
  const factory EventCommentModel({
    @JsonKey(name: 'id') int? id,
    @Json<PERSON>ey(name: 'content') String? content,
    @Json<PERSON>ey(name: 'user') UserInfoModel? user,
  }) = _EventCommentModel;

  factory EventCommentModel.fromJson(Map<String, dynamic> json) =>
      _$EventCommentModelFromJson(json);
}
