import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';

part 'event_detail_model.freezed.dart';
part 'event_detail_model.g.dart';

@freezed
class EventDetailModel with _$EventDetailModel {
  const factory EventDetailModel({
    @Json<PERSON>ey(name: 'id') int? id,
    @Json<PERSON>ey(name: 'name') String? name,
    @<PERSON>son<PERSON>ey(name: 'content') String? content,
    @<PERSON>son<PERSON><PERSON>(name: 'img') String? img,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'start_date') String? startDate,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'end_date') String? endDate,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'address') String? address,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'latitude') String? latitude,
    @J<PERSON><PERSON>ey(name: 'longitude') String? longitude,
    @<PERSON>son<PERSON>ey(name: 'tags') List<dynamic>? tags,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user') UserInfoModel? user,
  }) = _EventDetailModel;

  factory EventDetailModel.fromJson(Map<String, dynamic> json) =>
      _$EventDetailModelFromJson(json);
}
