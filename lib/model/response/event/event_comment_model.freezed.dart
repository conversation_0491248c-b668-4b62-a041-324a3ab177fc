// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'event_comment_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EventCommentModel _$EventCommentModelFromJson(Map<String, dynamic> json) {
  return _EventCommentModel.fromJson(json);
}

/// @nodoc
mixin _$EventCommentModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'content')
  String? get content => throw _privateConstructorUsedError;
  @JsonKey(name: 'user')
  UserInfoModel? get user => throw _privateConstructorUsedError;

  /// Serializes this EventCommentModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EventCommentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EventCommentModelCopyWith<EventCommentModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EventCommentModelCopyWith<$Res> {
  factory $EventCommentModelCopyWith(
          EventCommentModel value, $Res Function(EventCommentModel) then) =
      _$EventCommentModelCopyWithImpl<$Res, EventCommentModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'content') String? content,
      @JsonKey(name: 'user') UserInfoModel? user});
}

/// @nodoc
class _$EventCommentModelCopyWithImpl<$Res, $Val extends EventCommentModel>
    implements $EventCommentModelCopyWith<$Res> {
  _$EventCommentModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EventCommentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? content = freezed,
    Object? user = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserInfoModel?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EventCommentModelImplCopyWith<$Res>
    implements $EventCommentModelCopyWith<$Res> {
  factory _$$EventCommentModelImplCopyWith(_$EventCommentModelImpl value,
          $Res Function(_$EventCommentModelImpl) then) =
      __$$EventCommentModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'content') String? content,
      @JsonKey(name: 'user') UserInfoModel? user});
}

/// @nodoc
class __$$EventCommentModelImplCopyWithImpl<$Res>
    extends _$EventCommentModelCopyWithImpl<$Res, _$EventCommentModelImpl>
    implements _$$EventCommentModelImplCopyWith<$Res> {
  __$$EventCommentModelImplCopyWithImpl(_$EventCommentModelImpl _value,
      $Res Function(_$EventCommentModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of EventCommentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? content = freezed,
    Object? user = freezed,
  }) {
    return _then(_$EventCommentModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserInfoModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EventCommentModelImpl implements _EventCommentModel {
  const _$EventCommentModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'content') this.content,
      @JsonKey(name: 'user') this.user});

  factory _$EventCommentModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$EventCommentModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'content')
  final String? content;
  @override
  @JsonKey(name: 'user')
  final UserInfoModel? user;

  @override
  String toString() {
    return 'EventCommentModel(id: $id, content: $content, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EventCommentModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, content, user);

  /// Create a copy of EventCommentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EventCommentModelImplCopyWith<_$EventCommentModelImpl> get copyWith =>
      __$$EventCommentModelImplCopyWithImpl<_$EventCommentModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EventCommentModelImplToJson(
      this,
    );
  }
}

abstract class _EventCommentModel implements EventCommentModel {
  const factory _EventCommentModel(
          {@JsonKey(name: 'id') final int? id,
          @JsonKey(name: 'content') final String? content,
          @JsonKey(name: 'user') final UserInfoModel? user}) =
      _$EventCommentModelImpl;

  factory _EventCommentModel.fromJson(Map<String, dynamic> json) =
      _$EventCommentModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'content')
  String? get content;
  @override
  @JsonKey(name: 'user')
  UserInfoModel? get user;

  /// Create a copy of EventCommentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EventCommentModelImplCopyWith<_$EventCommentModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
