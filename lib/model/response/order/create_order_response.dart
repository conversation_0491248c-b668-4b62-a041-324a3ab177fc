import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_order_response.freezed.dart';
part 'create_order_response.g.dart';

@freezed
class CreateOrderData with _$CreateOrderData {
  const factory CreateOrderData({
    required int id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'redirect_url') String? redirectUrl,
    @<PERSON><PERSON><PERSON>ey(name: 'status') String? status,
  }) = _CreateOrderData;

  factory CreateOrderData.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderDataFromJson(json);
}

extension CreateOrderDataExtension on CreateOrderData {
  bool get isSuccess => status == 'SUCCESS';
}
