import 'package:json_annotation/json_annotation.dart';

part 'draft_order_response.g.dart';

@JsonSerializable()
class DraftOrderResponse {
  @J<PERSON><PERSON><PERSON>(name: 'user_id')
  final int? userId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'access_id')
  final String? accessId;

  @J<PERSON><PERSON><PERSON>(name: 'transaction_id')
  final String? transactionId;

  @Json<PERSON><PERSON>(name: 'total_payment_amount')
  final int? totalPaymentAmount;

  @<PERSON>son<PERSON><PERSON>(name: 'total_usage_point')
  final int? totalUsagePoint;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'actual_payment_amount')
  final int? actualPaymentAmount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'status')
  final String? status;

  @Json<PERSON>ey(name: 'order_at')
  final String? orderAt;

  DraftOrderResponse({
     this.userId,
     this.accessId,
     this.transactionId,
    this.totalPaymentAmount,
    this.totalUsagePoint,
    this.actualPaymentAmount,
    this.status,
    this.orderAt,
  });

  factory DraftOrderResponse.fromJson(Map<String, dynamic> json) =>
      _$DraftOrderResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DraftOrderResponseToJson(this);
}
