// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_order_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateOrderData _$CreateOrderDataFromJson(Map<String, dynamic> json) {
  return _CreateOrderData.fromJson(json);
}

/// @nodoc
mixin _$CreateOrderData {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'redirect_url')
  String? get redirectUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  String? get status => throw _privateConstructorUsedError;

  /// Serializes this CreateOrderData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateOrderData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateOrderDataCopyWith<CreateOrderData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateOrderDataCopyWith<$Res> {
  factory $CreateOrderDataCopyWith(
          CreateOrderData value, $Res Function(CreateOrderData) then) =
      _$CreateOrderDataCopyWithImpl<$Res, CreateOrderData>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'redirect_url') String? redirectUrl,
      @JsonKey(name: 'status') String? status});
}

/// @nodoc
class _$CreateOrderDataCopyWithImpl<$Res, $Val extends CreateOrderData>
    implements $CreateOrderDataCopyWith<$Res> {
  _$CreateOrderDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateOrderData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? redirectUrl = freezed,
    Object? status = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateOrderDataImplCopyWith<$Res>
    implements $CreateOrderDataCopyWith<$Res> {
  factory _$$CreateOrderDataImplCopyWith(_$CreateOrderDataImpl value,
          $Res Function(_$CreateOrderDataImpl) then) =
      __$$CreateOrderDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'redirect_url') String? redirectUrl,
      @JsonKey(name: 'status') String? status});
}

/// @nodoc
class __$$CreateOrderDataImplCopyWithImpl<$Res>
    extends _$CreateOrderDataCopyWithImpl<$Res, _$CreateOrderDataImpl>
    implements _$$CreateOrderDataImplCopyWith<$Res> {
  __$$CreateOrderDataImplCopyWithImpl(
      _$CreateOrderDataImpl _value, $Res Function(_$CreateOrderDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateOrderData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? redirectUrl = freezed,
    Object? status = freezed,
  }) {
    return _then(_$CreateOrderDataImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateOrderDataImpl implements _CreateOrderData {
  const _$CreateOrderDataImpl(
      {required this.id,
      @JsonKey(name: 'redirect_url') this.redirectUrl,
      @JsonKey(name: 'status') this.status});

  factory _$CreateOrderDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateOrderDataImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'redirect_url')
  final String? redirectUrl;
  @override
  @JsonKey(name: 'status')
  final String? status;

  @override
  String toString() {
    return 'CreateOrderData(id: $id, redirectUrl: $redirectUrl, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateOrderDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, redirectUrl, status);

  /// Create a copy of CreateOrderData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateOrderDataImplCopyWith<_$CreateOrderDataImpl> get copyWith =>
      __$$CreateOrderDataImplCopyWithImpl<_$CreateOrderDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateOrderDataImplToJson(
      this,
    );
  }
}

abstract class _CreateOrderData implements CreateOrderData {
  const factory _CreateOrderData(
      {required final int id,
      @JsonKey(name: 'redirect_url') final String? redirectUrl,
      @JsonKey(name: 'status') final String? status}) = _$CreateOrderDataImpl;

  factory _CreateOrderData.fromJson(Map<String, dynamic> json) =
      _$CreateOrderDataImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'redirect_url')
  String? get redirectUrl;
  @override
  @JsonKey(name: 'status')
  String? get status;

  /// Create a copy of CreateOrderData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateOrderDataImplCopyWith<_$CreateOrderDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
