import 'package:json_annotation/json_annotation.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';

part 'token_model.g.dart';

@JsonSerializable()
class TokenModel {
  String? token;
  UserInfoModel? user;

  TokenModel({this.token, this.user});

  factory TokenModel.fromJson(Map<String, dynamic> json) =>
      _$TokenModelFromJson(json);
  Map<String, dynamic> toJson() => _$TokenModelToJson(this);
}
