import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_info_model.freezed.dart';
part 'payment_info_model.g.dart';

@freezed
class PaymentInfoModel with _$PaymentInfoModel {
  const factory PaymentInfoModel({
    required int id,
    @<PERSON>son<PERSON>ey(name: 'card_no') required String cardNo,
    required String brand,
    @JsonKey(name: 'default_flag') required String defaultFlag,
  }) = _PaymentInfoModel;

  factory PaymentInfoModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentInfoModelFromJson(json);
}

extension PaymentInfoModelX on PaymentInfoModel {
  bool get isDefault => defaultFlag == '1';
}
