// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PaymentInfoModel _$PaymentInfoModelFromJson(Map<String, dynamic> json) {
  return _PaymentInfoModel.fromJson(json);
}

/// @nodoc
mixin _$PaymentInfoModel {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'card_no')
  String get cardNo => throw _privateConstructorUsedError;
  String get brand => throw _privateConstructorUsedError;
  @JsonKey(name: 'default_flag')
  String get defaultFlag => throw _privateConstructorUsedError;

  /// Serializes this PaymentInfoModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentInfoModelCopyWith<PaymentInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentInfoModelCopyWith<$Res> {
  factory $PaymentInfoModelCopyWith(
          PaymentInfoModel value, $Res Function(PaymentInfoModel) then) =
      _$PaymentInfoModelCopyWithImpl<$Res, PaymentInfoModel>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'card_no') String cardNo,
      String brand,
      @JsonKey(name: 'default_flag') String defaultFlag});
}

/// @nodoc
class _$PaymentInfoModelCopyWithImpl<$Res, $Val extends PaymentInfoModel>
    implements $PaymentInfoModelCopyWith<$Res> {
  _$PaymentInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cardNo = null,
    Object? brand = null,
    Object? defaultFlag = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      cardNo: null == cardNo
          ? _value.cardNo
          : cardNo // ignore: cast_nullable_to_non_nullable
              as String,
      brand: null == brand
          ? _value.brand
          : brand // ignore: cast_nullable_to_non_nullable
              as String,
      defaultFlag: null == defaultFlag
          ? _value.defaultFlag
          : defaultFlag // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaymentInfoModelImplCopyWith<$Res>
    implements $PaymentInfoModelCopyWith<$Res> {
  factory _$$PaymentInfoModelImplCopyWith(_$PaymentInfoModelImpl value,
          $Res Function(_$PaymentInfoModelImpl) then) =
      __$$PaymentInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'card_no') String cardNo,
      String brand,
      @JsonKey(name: 'default_flag') String defaultFlag});
}

/// @nodoc
class __$$PaymentInfoModelImplCopyWithImpl<$Res>
    extends _$PaymentInfoModelCopyWithImpl<$Res, _$PaymentInfoModelImpl>
    implements _$$PaymentInfoModelImplCopyWith<$Res> {
  __$$PaymentInfoModelImplCopyWithImpl(_$PaymentInfoModelImpl _value,
      $Res Function(_$PaymentInfoModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cardNo = null,
    Object? brand = null,
    Object? defaultFlag = null,
  }) {
    return _then(_$PaymentInfoModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      cardNo: null == cardNo
          ? _value.cardNo
          : cardNo // ignore: cast_nullable_to_non_nullable
              as String,
      brand: null == brand
          ? _value.brand
          : brand // ignore: cast_nullable_to_non_nullable
              as String,
      defaultFlag: null == defaultFlag
          ? _value.defaultFlag
          : defaultFlag // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentInfoModelImpl implements _PaymentInfoModel {
  const _$PaymentInfoModelImpl(
      {required this.id,
      @JsonKey(name: 'card_no') required this.cardNo,
      required this.brand,
      @JsonKey(name: 'default_flag') required this.defaultFlag});

  factory _$PaymentInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentInfoModelImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'card_no')
  final String cardNo;
  @override
  final String brand;
  @override
  @JsonKey(name: 'default_flag')
  final String defaultFlag;

  @override
  String toString() {
    return 'PaymentInfoModel(id: $id, cardNo: $cardNo, brand: $brand, defaultFlag: $defaultFlag)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentInfoModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cardNo, cardNo) || other.cardNo == cardNo) &&
            (identical(other.brand, brand) || other.brand == brand) &&
            (identical(other.defaultFlag, defaultFlag) ||
                other.defaultFlag == defaultFlag));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, cardNo, brand, defaultFlag);

  /// Create a copy of PaymentInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentInfoModelImplCopyWith<_$PaymentInfoModelImpl> get copyWith =>
      __$$PaymentInfoModelImplCopyWithImpl<_$PaymentInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentInfoModelImplToJson(
      this,
    );
  }
}

abstract class _PaymentInfoModel implements PaymentInfoModel {
  const factory _PaymentInfoModel(
          {required final int id,
          @JsonKey(name: 'card_no') required final String cardNo,
          required final String brand,
          @JsonKey(name: 'default_flag') required final String defaultFlag}) =
      _$PaymentInfoModelImpl;

  factory _PaymentInfoModel.fromJson(Map<String, dynamic> json) =
      _$PaymentInfoModelImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'card_no')
  String get cardNo;
  @override
  String get brand;
  @override
  @JsonKey(name: 'default_flag')
  String get defaultFlag;

  /// Create a copy of PaymentInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentInfoModelImplCopyWith<_$PaymentInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
