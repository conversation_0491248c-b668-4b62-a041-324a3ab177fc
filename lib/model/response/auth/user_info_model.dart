import 'package:json_annotation/json_annotation.dart';
import 'package:kitemite_app/model/response/notification/notification_model.dart';
import 'package:kitemite_app/model/response/store/store_model.dart';

part 'user_info_model.g.dart';

@JsonSerializable()
class UserInfoModel {
  final int id;
  final String? username;
  final String? email;
  @Json<PERSON>ey(name: 'email_verified_at')
  final String? emailVerifiedAt;
  final String? yob;
  final String? province;
  final String gender;
  final String? tel;
  final String? img;
  final String? job;
  final int? point;
  final StoreModel? store;

  UserInfoModel({
    required this.id,
    this.username,
    this.email,
    this.emailVerifiedAt,
    this.yob,
    this.province,
    required this.gender,
    this.tel,
    this.img,
    this.job,
    this.point,
    this.store,
  });

  factory UserInfoModel.fromJson(Map<String, dynamic> json) =>
      _$UserInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserInfoModelToJson(this);
}

extension UserInfoModelExtension on UserInfoModel {
  bool get accountHasRoleSeller => store?.id != null;
}
