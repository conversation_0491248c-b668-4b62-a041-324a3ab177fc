// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'link_card_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LinkCardModel _$LinkCardModelFromJson(Map<String, dynamic> json) {
  return _LinkCardModel.fromJson(json);
}

/// @nodoc
mixin _$LinkCardModel {
  @JsonKey(name: 'link_url')
  String get linkUrl => throw _privateConstructorUsedError;

  /// Serializes this LinkCardModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LinkCardModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LinkCardModelCopyWith<LinkCardModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LinkCardModelCopyWith<$Res> {
  factory $LinkCardModelCopyWith(
          LinkCardModel value, $Res Function(LinkCardModel) then) =
      _$LinkCardModelCopyWithImpl<$Res, LinkCardModel>;
  @useResult
  $Res call({@JsonKey(name: 'link_url') String linkUrl});
}

/// @nodoc
class _$LinkCardModelCopyWithImpl<$Res, $Val extends LinkCardModel>
    implements $LinkCardModelCopyWith<$Res> {
  _$LinkCardModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LinkCardModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? linkUrl = null,
  }) {
    return _then(_value.copyWith(
      linkUrl: null == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LinkCardModelImplCopyWith<$Res>
    implements $LinkCardModelCopyWith<$Res> {
  factory _$$LinkCardModelImplCopyWith(
          _$LinkCardModelImpl value, $Res Function(_$LinkCardModelImpl) then) =
      __$$LinkCardModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'link_url') String linkUrl});
}

/// @nodoc
class __$$LinkCardModelImplCopyWithImpl<$Res>
    extends _$LinkCardModelCopyWithImpl<$Res, _$LinkCardModelImpl>
    implements _$$LinkCardModelImplCopyWith<$Res> {
  __$$LinkCardModelImplCopyWithImpl(
      _$LinkCardModelImpl _value, $Res Function(_$LinkCardModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of LinkCardModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? linkUrl = null,
  }) {
    return _then(_$LinkCardModelImpl(
      linkUrl: null == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LinkCardModelImpl implements _LinkCardModel {
  const _$LinkCardModelImpl({@JsonKey(name: 'link_url') required this.linkUrl});

  factory _$LinkCardModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LinkCardModelImplFromJson(json);

  @override
  @JsonKey(name: 'link_url')
  final String linkUrl;

  @override
  String toString() {
    return 'LinkCardModel(linkUrl: $linkUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LinkCardModelImpl &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, linkUrl);

  /// Create a copy of LinkCardModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LinkCardModelImplCopyWith<_$LinkCardModelImpl> get copyWith =>
      __$$LinkCardModelImplCopyWithImpl<_$LinkCardModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LinkCardModelImplToJson(
      this,
    );
  }
}

abstract class _LinkCardModel implements LinkCardModel {
  const factory _LinkCardModel(
          {@JsonKey(name: 'link_url') required final String linkUrl}) =
      _$LinkCardModelImpl;

  factory _LinkCardModel.fromJson(Map<String, dynamic> json) =
      _$LinkCardModelImpl.fromJson;

  @override
  @JsonKey(name: 'link_url')
  String get linkUrl;

  /// Create a copy of LinkCardModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LinkCardModelImplCopyWith<_$LinkCardModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
