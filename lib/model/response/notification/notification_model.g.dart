// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationModelImpl _$$NotificationModelImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationModelImpl(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      message: json['message'] as String?,
      screen: json['screen'] as String?,
      config: json['config'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      pivot: json['pivot'] == null
          ? null
          : NotificationPivot.fromJson(json['pivot'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$NotificationModelImplToJson(
        _$NotificationModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'screen': instance.screen,
      'config': instance.config,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'pivot': instance.pivot,
    };

_$NotificationPivotImpl _$$NotificationPivotImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationPivotImpl(
      userId: (json['user_id'] as num?)?.toInt(),
      notificationId: (json['notification_id'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$NotificationPivotImplToJson(
        _$NotificationPivotImpl instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'notification_id': instance.notificationId,
      'status': instance.status,
    };
