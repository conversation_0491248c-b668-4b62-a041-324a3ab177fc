// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WarehouseModelImpl _$$WarehouseModelImplFromJson(Map<String, dynamic> json) =>
    _$WarehouseModelImpl(
      id: (json['id'] as num?)?.toInt(),
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
      name: json['name'] as String?,
      zipcode: json['zipcode'] as String?,
      province: json['province'] as String?,
      city: json['city'] as String?,
      street: json['street'] as String?,
      building: json['building'] as String?,
      tel: json['tel'] as String?,
      mobile: json['mobile'] as String?,
      email: json['email'] as String?,
      img: json['img'] as String?,
      open: json['open'] as String?,
      close: json['close'] as String?,
      productsCount: (json['products_count'] as num?)?.toInt(),
      productsQuantity: (json['products_quantity'] as num?)?.toInt(),
      status: json['status'] as String?,
      cabinets: (json['cabinets'] as List<dynamic>?)
          ?.map((e) => CabinetModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$WarehouseModelImplToJson(
        _$WarehouseModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'name': instance.name,
      'zipcode': instance.zipcode,
      'province': instance.province,
      'city': instance.city,
      'street': instance.street,
      'building': instance.building,
      'tel': instance.tel,
      'mobile': instance.mobile,
      'email': instance.email,
      'img': instance.img,
      'open': instance.open,
      'close': instance.close,
      'products_count': instance.productsCount,
      'products_quantity': instance.productsQuantity,
      'status': instance.status,
      'cabinets': instance.cabinets,
    };

_$CabinetModelImpl _$$CabinetModelImplFromJson(Map<String, dynamic> json) =>
    _$CabinetModelImpl(
      id: (json['id'] as num?)?.toInt(),
      cabinetCode: json['cabinet_code'] as String?,
      description: json['description'] as String?,
      type: json['type'] as String?,
      products: (json['products'] as List<dynamic>?)
          ?.map(
              (e) => WarehouseProductModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      lock: json['lock'] == null
          ? null
          : LockModel.fromJson(json['lock'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CabinetModelImplToJson(_$CabinetModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cabinet_code': instance.cabinetCode,
      'description': instance.description,
      'type': instance.type,
      'products': instance.products,
      'lock': instance.lock,
    };

_$LockModelImpl _$$LockModelImplFromJson(Map<String, dynamic> json) =>
    _$LockModelImpl(
      id: (json['id'] as num?)?.toInt(),
      macAddress: json['mac_address'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$$LockModelImplToJson(_$LockModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'mac_address': instance.macAddress,
      'name': instance.name,
    };

_$WarehouseProductModelImpl _$$WarehouseProductModelImplFromJson(
        Map<String, dynamic> json) =>
    _$WarehouseProductModelImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      quantity: (json['quantity'] as num?)?.toInt(),
      img: json['img'] as String?,
      expirationDate: json['expiration_date'] as String?,
      price: json['price'] as String?,
      shelf: json['shelf'] == null
          ? null
          : ShelfModel.fromJson(json['shelf'] as Map<String, dynamic>),
      stockHistory: json['stock_history'] == null
          ? null
          : StockHistoryModel.fromJson(
              json['stock_history'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$WarehouseProductModelImplToJson(
        _$WarehouseProductModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'quantity': instance.quantity,
      'img': instance.img,
      'expiration_date': instance.expirationDate,
      'price': instance.price,
      'shelf': instance.shelf,
      'stock_history': instance.stockHistory,
    };

_$ShelfModelImpl _$$ShelfModelImplFromJson(Map<String, dynamic> json) =>
    _$ShelfModelImpl(
      id: (json['id'] as num?)?.toInt(),
      shelfCode: json['shelf_code'] as String?,
      status: json['status'] as String?,
    );

Map<String, dynamic> _$$ShelfModelImplToJson(_$ShelfModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'shelf_code': instance.shelfCode,
      'status': instance.status,
    };

_$StockHistoryModelImpl _$$StockHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StockHistoryModelImpl(
      id: (json['id'] as num?)?.toInt(),
      type: json['type'] as String?,
      amount: (json['amount'] as num?)?.toInt(),
      reason: json['reason'] as String?,
      date: json['date'] as String?,
    );

Map<String, dynamic> _$$StockHistoryModelImplToJson(
        _$StockHistoryModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'amount': instance.amount,
      'reason': instance.reason,
      'date': instance.date,
    };
