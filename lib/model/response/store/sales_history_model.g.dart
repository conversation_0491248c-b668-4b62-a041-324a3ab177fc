// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sales_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SalesHistoryModelImpl _$$SalesHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SalesHistoryModelImpl(
      date: json['date'] as String?,
      products: (json['products'] as List<dynamic>?)
              ?.map((e) => SoldProductModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$SalesHistoryModelImplToJson(
        _$SalesHistoryModelImpl instance) =>
    <String, dynamic>{
      'date': instance.date,
      'products': instance.products,
    };

_$SoldProductModelImpl _$$SoldProductModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SoldProductModelImpl(
      id: (json['id'] as num?)?.toInt(),
      orderId: (json['order_id'] as num?)?.toInt(),
      name: json['name'] as String?,
      salePrice: json['sale_price'] as String?,
      img: json['img'] as String?,
      shelf: json['shelf'] == null
          ? null
          : ShelfModel.fromJson(json['shelf'] as Map<String, dynamic>),
      cabinet: json['cabinet'] == null
          ? null
          : CabinetModel.fromJson(json['cabinet'] as Map<String, dynamic>),
      place: json['place'] == null
          ? null
          : PlaceModel.fromJson(json['place'] as Map<String, dynamic>),
      saleQuantity: (json['sale_quantity'] as num?)?.toInt(),
      totalAmountPrice: (json['total_amount_price'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$SoldProductModelImplToJson(
        _$SoldProductModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'order_id': instance.orderId,
      'name': instance.name,
      'sale_price': instance.salePrice,
      'img': instance.img,
      'shelf': instance.shelf,
      'cabinet': instance.cabinet,
      'place': instance.place,
      'sale_quantity': instance.saleQuantity,
      'total_amount_price': instance.totalAmountPrice,
    };

_$ShelfModelImpl _$$ShelfModelImplFromJson(Map<String, dynamic> json) =>
    _$ShelfModelImpl(
      id: (json['id'] as num?)?.toInt(),
      shelfCode: json['shelf_code'] as String?,
      status: json['status'] as String?,
      stage: (json['stage'] as num?)?.toInt(),
      description: json['description'] as String?,
      height: (json['height'] as num?)?.toInt(),
      width: (json['width'] as num?)?.toInt(),
      depth: (json['depth'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ShelfModelImplToJson(_$ShelfModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'shelf_code': instance.shelfCode,
      'status': instance.status,
      'stage': instance.stage,
      'description': instance.description,
      'height': instance.height,
      'width': instance.width,
      'depth': instance.depth,
    };

_$CabinetModelImpl _$$CabinetModelImplFromJson(Map<String, dynamic> json) =>
    _$CabinetModelImpl(
      id: (json['id'] as num?)?.toInt(),
      cabinetCode: json['cabinet_code'] as String?,
      description: json['description'] as String?,
      type: json['type'] as String?,
    );

Map<String, dynamic> _$$CabinetModelImplToJson(_$CabinetModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cabinet_code': instance.cabinetCode,
      'description': instance.description,
      'type': instance.type,
    };

_$PlaceModelImpl _$$PlaceModelImplFromJson(Map<String, dynamic> json) =>
    _$PlaceModelImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$PlaceModelImplToJson(_$PlaceModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'images': instance.images,
    };
