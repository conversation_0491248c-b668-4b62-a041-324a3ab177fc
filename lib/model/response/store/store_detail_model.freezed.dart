// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'store_detail_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StoreDetailModel _$StoreDetailModelFromJson(Map<String, dynamic> json) {
  return _StoreDetailModel.fromJson(json);
}

/// @nodoc
mixin _$StoreDetailModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'latitude')
  String? get latitude => throw _privateConstructorUsedError;
  @JsonKey(name: 'longitude')
  String? get longitude => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'zipcode')
  String? get zipcode => throw _privateConstructorUsedError;
  @JsonKey(name: 'province')
  String? get province => throw _privateConstructorUsedError;
  @JsonKey(name: 'city')
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: 'street')
  String? get street => throw _privateConstructorUsedError;
  @JsonKey(name: 'building')
  String? get building => throw _privateConstructorUsedError;
  @JsonKey(name: 'tel')
  String? get tel => throw _privateConstructorUsedError;
  @JsonKey(name: 'mobile')
  String? get mobile => throw _privateConstructorUsedError;
  @JsonKey(name: 'email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'img')
  String? get img => throw _privateConstructorUsedError;
  @JsonKey(name: 'open')
  String? get open => throw _privateConstructorUsedError;
  @JsonKey(name: 'close')
  String? get close => throw _privateConstructorUsedError;
  @JsonKey(name: 'cabinets')
  List<StoreCabinetModel>? get cabinets => throw _privateConstructorUsedError;
  @JsonKey(name: 'available_shelves_count')
  int? get availableShelvesCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'images')
  List<String>? get images => throw _privateConstructorUsedError;

  /// Serializes this StoreDetailModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoreDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoreDetailModelCopyWith<StoreDetailModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoreDetailModelCopyWith<$Res> {
  factory $StoreDetailModelCopyWith(
          StoreDetailModel value, $Res Function(StoreDetailModel) then) =
      _$StoreDetailModelCopyWithImpl<$Res, StoreDetailModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'latitude') String? latitude,
      @JsonKey(name: 'longitude') String? longitude,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: 'zipcode') String? zipcode,
      @JsonKey(name: 'province') String? province,
      @JsonKey(name: 'city') String? city,
      @JsonKey(name: 'street') String? street,
      @JsonKey(name: 'building') String? building,
      @JsonKey(name: 'tel') String? tel,
      @JsonKey(name: 'mobile') String? mobile,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'img') String? img,
      @JsonKey(name: 'open') String? open,
      @JsonKey(name: 'close') String? close,
      @JsonKey(name: 'cabinets') List<StoreCabinetModel>? cabinets,
      @JsonKey(name: 'available_shelves_count') int? availableShelvesCount,
      @JsonKey(name: 'images') List<String>? images});
}

/// @nodoc
class _$StoreDetailModelCopyWithImpl<$Res, $Val extends StoreDetailModel>
    implements $StoreDetailModelCopyWith<$Res> {
  _$StoreDetailModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoreDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? name = freezed,
    Object? zipcode = freezed,
    Object? province = freezed,
    Object? city = freezed,
    Object? street = freezed,
    Object? building = freezed,
    Object? tel = freezed,
    Object? mobile = freezed,
    Object? email = freezed,
    Object? img = freezed,
    Object? open = freezed,
    Object? close = freezed,
    Object? cabinets = freezed,
    Object? availableShelvesCount = freezed,
    Object? images = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as String?,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      zipcode: freezed == zipcode
          ? _value.zipcode
          : zipcode // ignore: cast_nullable_to_non_nullable
              as String?,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      building: freezed == building
          ? _value.building
          : building // ignore: cast_nullable_to_non_nullable
              as String?,
      tel: freezed == tel
          ? _value.tel
          : tel // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      open: freezed == open
          ? _value.open
          : open // ignore: cast_nullable_to_non_nullable
              as String?,
      close: freezed == close
          ? _value.close
          : close // ignore: cast_nullable_to_non_nullable
              as String?,
      cabinets: freezed == cabinets
          ? _value.cabinets
          : cabinets // ignore: cast_nullable_to_non_nullable
              as List<StoreCabinetModel>?,
      availableShelvesCount: freezed == availableShelvesCount
          ? _value.availableShelvesCount
          : availableShelvesCount // ignore: cast_nullable_to_non_nullable
              as int?,
      images: freezed == images
          ? _value.images
          : images // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StoreDetailModelImplCopyWith<$Res>
    implements $StoreDetailModelCopyWith<$Res> {
  factory _$$StoreDetailModelImplCopyWith(_$StoreDetailModelImpl value,
          $Res Function(_$StoreDetailModelImpl) then) =
      __$$StoreDetailModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'latitude') String? latitude,
      @JsonKey(name: 'longitude') String? longitude,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: 'zipcode') String? zipcode,
      @JsonKey(name: 'province') String? province,
      @JsonKey(name: 'city') String? city,
      @JsonKey(name: 'street') String? street,
      @JsonKey(name: 'building') String? building,
      @JsonKey(name: 'tel') String? tel,
      @JsonKey(name: 'mobile') String? mobile,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'img') String? img,
      @JsonKey(name: 'open') String? open,
      @JsonKey(name: 'close') String? close,
      @JsonKey(name: 'cabinets') List<StoreCabinetModel>? cabinets,
      @JsonKey(name: 'available_shelves_count') int? availableShelvesCount,
      @JsonKey(name: 'images') List<String>? images});
}

/// @nodoc
class __$$StoreDetailModelImplCopyWithImpl<$Res>
    extends _$StoreDetailModelCopyWithImpl<$Res, _$StoreDetailModelImpl>
    implements _$$StoreDetailModelImplCopyWith<$Res> {
  __$$StoreDetailModelImplCopyWithImpl(_$StoreDetailModelImpl _value,
      $Res Function(_$StoreDetailModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StoreDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? name = freezed,
    Object? zipcode = freezed,
    Object? province = freezed,
    Object? city = freezed,
    Object? street = freezed,
    Object? building = freezed,
    Object? tel = freezed,
    Object? mobile = freezed,
    Object? email = freezed,
    Object? img = freezed,
    Object? open = freezed,
    Object? close = freezed,
    Object? cabinets = freezed,
    Object? availableShelvesCount = freezed,
    Object? images = freezed,
  }) {
    return _then(_$StoreDetailModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as String?,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      zipcode: freezed == zipcode
          ? _value.zipcode
          : zipcode // ignore: cast_nullable_to_non_nullable
              as String?,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      building: freezed == building
          ? _value.building
          : building // ignore: cast_nullable_to_non_nullable
              as String?,
      tel: freezed == tel
          ? _value.tel
          : tel // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      open: freezed == open
          ? _value.open
          : open // ignore: cast_nullable_to_non_nullable
              as String?,
      close: freezed == close
          ? _value.close
          : close // ignore: cast_nullable_to_non_nullable
              as String?,
      cabinets: freezed == cabinets
          ? _value._cabinets
          : cabinets // ignore: cast_nullable_to_non_nullable
              as List<StoreCabinetModel>?,
      availableShelvesCount: freezed == availableShelvesCount
          ? _value.availableShelvesCount
          : availableShelvesCount // ignore: cast_nullable_to_non_nullable
              as int?,
      images: freezed == images
          ? _value._images
          : images // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StoreDetailModelImpl implements _StoreDetailModel {
  const _$StoreDetailModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'latitude') this.latitude,
      @JsonKey(name: 'longitude') this.longitude,
      @JsonKey(name: 'name') this.name,
      @JsonKey(name: 'zipcode') this.zipcode,
      @JsonKey(name: 'province') this.province,
      @JsonKey(name: 'city') this.city,
      @JsonKey(name: 'street') this.street,
      @JsonKey(name: 'building') this.building,
      @JsonKey(name: 'tel') this.tel,
      @JsonKey(name: 'mobile') this.mobile,
      @JsonKey(name: 'email') this.email,
      @JsonKey(name: 'img') this.img,
      @JsonKey(name: 'open') this.open,
      @JsonKey(name: 'close') this.close,
      @JsonKey(name: 'cabinets') final List<StoreCabinetModel>? cabinets,
      @JsonKey(name: 'available_shelves_count') this.availableShelvesCount,
      @JsonKey(name: 'images') final List<String>? images})
      : _cabinets = cabinets,
        _images = images;

  factory _$StoreDetailModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoreDetailModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'latitude')
  final String? latitude;
  @override
  @JsonKey(name: 'longitude')
  final String? longitude;
  @override
  @JsonKey(name: 'name')
  final String? name;
  @override
  @JsonKey(name: 'zipcode')
  final String? zipcode;
  @override
  @JsonKey(name: 'province')
  final String? province;
  @override
  @JsonKey(name: 'city')
  final String? city;
  @override
  @JsonKey(name: 'street')
  final String? street;
  @override
  @JsonKey(name: 'building')
  final String? building;
  @override
  @JsonKey(name: 'tel')
  final String? tel;
  @override
  @JsonKey(name: 'mobile')
  final String? mobile;
  @override
  @JsonKey(name: 'email')
  final String? email;
  @override
  @JsonKey(name: 'img')
  final String? img;
  @override
  @JsonKey(name: 'open')
  final String? open;
  @override
  @JsonKey(name: 'close')
  final String? close;
  final List<StoreCabinetModel>? _cabinets;
  @override
  @JsonKey(name: 'cabinets')
  List<StoreCabinetModel>? get cabinets {
    final value = _cabinets;
    if (value == null) return null;
    if (_cabinets is EqualUnmodifiableListView) return _cabinets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'available_shelves_count')
  final int? availableShelvesCount;
  final List<String>? _images;
  @override
  @JsonKey(name: 'images')
  List<String>? get images {
    final value = _images;
    if (value == null) return null;
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'StoreDetailModel(id: $id, latitude: $latitude, longitude: $longitude, name: $name, zipcode: $zipcode, province: $province, city: $city, street: $street, building: $building, tel: $tel, mobile: $mobile, email: $email, img: $img, open: $open, close: $close, cabinets: $cabinets, availableShelvesCount: $availableShelvesCount, images: $images)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoreDetailModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.zipcode, zipcode) || other.zipcode == zipcode) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.building, building) ||
                other.building == building) &&
            (identical(other.tel, tel) || other.tel == tel) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.open, open) || other.open == open) &&
            (identical(other.close, close) || other.close == close) &&
            const DeepCollectionEquality().equals(other._cabinets, _cabinets) &&
            (identical(other.availableShelvesCount, availableShelvesCount) ||
                other.availableShelvesCount == availableShelvesCount) &&
            const DeepCollectionEquality().equals(other._images, _images));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      latitude,
      longitude,
      name,
      zipcode,
      province,
      city,
      street,
      building,
      tel,
      mobile,
      email,
      img,
      open,
      close,
      const DeepCollectionEquality().hash(_cabinets),
      availableShelvesCount,
      const DeepCollectionEquality().hash(_images));

  /// Create a copy of StoreDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoreDetailModelImplCopyWith<_$StoreDetailModelImpl> get copyWith =>
      __$$StoreDetailModelImplCopyWithImpl<_$StoreDetailModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoreDetailModelImplToJson(
      this,
    );
  }
}

abstract class _StoreDetailModel implements StoreDetailModel {
  const factory _StoreDetailModel(
          {@JsonKey(name: 'id') final int? id,
          @JsonKey(name: 'latitude') final String? latitude,
          @JsonKey(name: 'longitude') final String? longitude,
          @JsonKey(name: 'name') final String? name,
          @JsonKey(name: 'zipcode') final String? zipcode,
          @JsonKey(name: 'province') final String? province,
          @JsonKey(name: 'city') final String? city,
          @JsonKey(name: 'street') final String? street,
          @JsonKey(name: 'building') final String? building,
          @JsonKey(name: 'tel') final String? tel,
          @JsonKey(name: 'mobile') final String? mobile,
          @JsonKey(name: 'email') final String? email,
          @JsonKey(name: 'img') final String? img,
          @JsonKey(name: 'open') final String? open,
          @JsonKey(name: 'close') final String? close,
          @JsonKey(name: 'cabinets') final List<StoreCabinetModel>? cabinets,
          @JsonKey(name: 'available_shelves_count')
          final int? availableShelvesCount,
          @JsonKey(name: 'images') final List<String>? images}) =
      _$StoreDetailModelImpl;

  factory _StoreDetailModel.fromJson(Map<String, dynamic> json) =
      _$StoreDetailModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'latitude')
  String? get latitude;
  @override
  @JsonKey(name: 'longitude')
  String? get longitude;
  @override
  @JsonKey(name: 'name')
  String? get name;
  @override
  @JsonKey(name: 'zipcode')
  String? get zipcode;
  @override
  @JsonKey(name: 'province')
  String? get province;
  @override
  @JsonKey(name: 'city')
  String? get city;
  @override
  @JsonKey(name: 'street')
  String? get street;
  @override
  @JsonKey(name: 'building')
  String? get building;
  @override
  @JsonKey(name: 'tel')
  String? get tel;
  @override
  @JsonKey(name: 'mobile')
  String? get mobile;
  @override
  @JsonKey(name: 'email')
  String? get email;
  @override
  @JsonKey(name: 'img')
  String? get img;
  @override
  @JsonKey(name: 'open')
  String? get open;
  @override
  @JsonKey(name: 'close')
  String? get close;
  @override
  @JsonKey(name: 'cabinets')
  List<StoreCabinetModel>? get cabinets;
  @override
  @JsonKey(name: 'available_shelves_count')
  int? get availableShelvesCount;
  @override
  @JsonKey(name: 'images')
  List<String>? get images;

  /// Create a copy of StoreDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoreDetailModelImplCopyWith<_$StoreDetailModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoreCabinetModel _$StoreCabinetModelFromJson(Map<String, dynamic> json) {
  return _StoreCabinetModel.fromJson(json);
}

/// @nodoc
mixin _$StoreCabinetModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'cabinet_code')
  String? get cabinetCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'description')
  String? get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'type')
  String? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'products')
  List<StoreProductModel>? get products => throw _privateConstructorUsedError;
  @JsonKey(name: 'shelves')
  List<StoreShelfModel>? get shelves => throw _privateConstructorUsedError;

  /// Serializes this StoreCabinetModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoreCabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoreCabinetModelCopyWith<StoreCabinetModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoreCabinetModelCopyWith<$Res> {
  factory $StoreCabinetModelCopyWith(
          StoreCabinetModel value, $Res Function(StoreCabinetModel) then) =
      _$StoreCabinetModelCopyWithImpl<$Res, StoreCabinetModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'cabinet_code') String? cabinetCode,
      @JsonKey(name: 'description') String? description,
      @JsonKey(name: 'type') String? type,
      @JsonKey(name: 'products') List<StoreProductModel>? products,
      @JsonKey(name: 'shelves') List<StoreShelfModel>? shelves});
}

/// @nodoc
class _$StoreCabinetModelCopyWithImpl<$Res, $Val extends StoreCabinetModel>
    implements $StoreCabinetModelCopyWith<$Res> {
  _$StoreCabinetModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoreCabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cabinetCode = freezed,
    Object? description = freezed,
    Object? type = freezed,
    Object? products = freezed,
    Object? shelves = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      products: freezed == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<StoreProductModel>?,
      shelves: freezed == shelves
          ? _value.shelves
          : shelves // ignore: cast_nullable_to_non_nullable
              as List<StoreShelfModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StoreCabinetModelImplCopyWith<$Res>
    implements $StoreCabinetModelCopyWith<$Res> {
  factory _$$StoreCabinetModelImplCopyWith(_$StoreCabinetModelImpl value,
          $Res Function(_$StoreCabinetModelImpl) then) =
      __$$StoreCabinetModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'cabinet_code') String? cabinetCode,
      @JsonKey(name: 'description') String? description,
      @JsonKey(name: 'type') String? type,
      @JsonKey(name: 'products') List<StoreProductModel>? products,
      @JsonKey(name: 'shelves') List<StoreShelfModel>? shelves});
}

/// @nodoc
class __$$StoreCabinetModelImplCopyWithImpl<$Res>
    extends _$StoreCabinetModelCopyWithImpl<$Res, _$StoreCabinetModelImpl>
    implements _$$StoreCabinetModelImplCopyWith<$Res> {
  __$$StoreCabinetModelImplCopyWithImpl(_$StoreCabinetModelImpl _value,
      $Res Function(_$StoreCabinetModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StoreCabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cabinetCode = freezed,
    Object? description = freezed,
    Object? type = freezed,
    Object? products = freezed,
    Object? shelves = freezed,
  }) {
    return _then(_$StoreCabinetModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      products: freezed == products
          ? _value._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<StoreProductModel>?,
      shelves: freezed == shelves
          ? _value._shelves
          : shelves // ignore: cast_nullable_to_non_nullable
              as List<StoreShelfModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StoreCabinetModelImpl implements _StoreCabinetModel {
  const _$StoreCabinetModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'cabinet_code') this.cabinetCode,
      @JsonKey(name: 'description') this.description,
      @JsonKey(name: 'type') this.type,
      @JsonKey(name: 'products') final List<StoreProductModel>? products,
      @JsonKey(name: 'shelves') final List<StoreShelfModel>? shelves})
      : _products = products,
        _shelves = shelves;

  factory _$StoreCabinetModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoreCabinetModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'cabinet_code')
  final String? cabinetCode;
  @override
  @JsonKey(name: 'description')
  final String? description;
  @override
  @JsonKey(name: 'type')
  final String? type;
  final List<StoreProductModel>? _products;
  @override
  @JsonKey(name: 'products')
  List<StoreProductModel>? get products {
    final value = _products;
    if (value == null) return null;
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<StoreShelfModel>? _shelves;
  @override
  @JsonKey(name: 'shelves')
  List<StoreShelfModel>? get shelves {
    final value = _shelves;
    if (value == null) return null;
    if (_shelves is EqualUnmodifiableListView) return _shelves;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'StoreCabinetModel(id: $id, cabinetCode: $cabinetCode, description: $description, type: $type, products: $products, shelves: $shelves)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoreCabinetModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cabinetCode, cabinetCode) ||
                other.cabinetCode == cabinetCode) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            const DeepCollectionEquality().equals(other._shelves, _shelves));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      cabinetCode,
      description,
      type,
      const DeepCollectionEquality().hash(_products),
      const DeepCollectionEquality().hash(_shelves));

  /// Create a copy of StoreCabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoreCabinetModelImplCopyWith<_$StoreCabinetModelImpl> get copyWith =>
      __$$StoreCabinetModelImplCopyWithImpl<_$StoreCabinetModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoreCabinetModelImplToJson(
      this,
    );
  }
}

abstract class _StoreCabinetModel implements StoreCabinetModel {
  const factory _StoreCabinetModel(
          {@JsonKey(name: 'id') final int? id,
          @JsonKey(name: 'cabinet_code') final String? cabinetCode,
          @JsonKey(name: 'description') final String? description,
          @JsonKey(name: 'type') final String? type,
          @JsonKey(name: 'products') final List<StoreProductModel>? products,
          @JsonKey(name: 'shelves') final List<StoreShelfModel>? shelves}) =
      _$StoreCabinetModelImpl;

  factory _StoreCabinetModel.fromJson(Map<String, dynamic> json) =
      _$StoreCabinetModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'cabinet_code')
  String? get cabinetCode;
  @override
  @JsonKey(name: 'description')
  String? get description;
  @override
  @JsonKey(name: 'type')
  String? get type;
  @override
  @JsonKey(name: 'products')
  List<StoreProductModel>? get products;
  @override
  @JsonKey(name: 'shelves')
  List<StoreShelfModel>? get shelves;

  /// Create a copy of StoreCabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoreCabinetModelImplCopyWith<_$StoreCabinetModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoreProductModel _$StoreProductModelFromJson(Map<String, dynamic> json) {
  return _StoreProductModel.fromJson(json);
}

/// @nodoc
mixin _$StoreProductModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'description')
  String? get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'comment')
  String? get comment => throw _privateConstructorUsedError;
  @JsonKey(name: 'note')
  String? get note => throw _privateConstructorUsedError;
  @JsonKey(name: 'quantity')
  int? get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'expiration_date')
  String? get expirationDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'tag')
  List<String>? get tag => throw _privateConstructorUsedError;
  @JsonKey(name: 'capacity')
  String? get capacity => throw _privateConstructorUsedError;
  @JsonKey(name: 'price')
  String? get price => throw _privateConstructorUsedError;
  @JsonKey(name: 'sale_price')
  String? get salePrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'img')
  String? get img => throw _privateConstructorUsedError;

  /// Serializes this StoreProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoreProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoreProductModelCopyWith<StoreProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoreProductModelCopyWith<$Res> {
  factory $StoreProductModelCopyWith(
          StoreProductModel value, $Res Function(StoreProductModel) then) =
      _$StoreProductModelCopyWithImpl<$Res, StoreProductModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: 'description') String? description,
      @JsonKey(name: 'comment') String? comment,
      @JsonKey(name: 'note') String? note,
      @JsonKey(name: 'quantity') int? quantity,
      @JsonKey(name: 'expiration_date') String? expirationDate,
      @JsonKey(name: 'tag') List<String>? tag,
      @JsonKey(name: 'capacity') String? capacity,
      @JsonKey(name: 'price') String? price,
      @JsonKey(name: 'sale_price') String? salePrice,
      @JsonKey(name: 'img') String? img});
}

/// @nodoc
class _$StoreProductModelCopyWithImpl<$Res, $Val extends StoreProductModel>
    implements $StoreProductModelCopyWith<$Res> {
  _$StoreProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoreProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? comment = freezed,
    Object? note = freezed,
    Object? quantity = freezed,
    Object? expirationDate = freezed,
    Object? tag = freezed,
    Object? capacity = freezed,
    Object? price = freezed,
    Object? salePrice = freezed,
    Object? img = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      capacity: freezed == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      salePrice: freezed == salePrice
          ? _value.salePrice
          : salePrice // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StoreProductModelImplCopyWith<$Res>
    implements $StoreProductModelCopyWith<$Res> {
  factory _$$StoreProductModelImplCopyWith(_$StoreProductModelImpl value,
          $Res Function(_$StoreProductModelImpl) then) =
      __$$StoreProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: 'description') String? description,
      @JsonKey(name: 'comment') String? comment,
      @JsonKey(name: 'note') String? note,
      @JsonKey(name: 'quantity') int? quantity,
      @JsonKey(name: 'expiration_date') String? expirationDate,
      @JsonKey(name: 'tag') List<String>? tag,
      @JsonKey(name: 'capacity') String? capacity,
      @JsonKey(name: 'price') String? price,
      @JsonKey(name: 'sale_price') String? salePrice,
      @JsonKey(name: 'img') String? img});
}

/// @nodoc
class __$$StoreProductModelImplCopyWithImpl<$Res>
    extends _$StoreProductModelCopyWithImpl<$Res, _$StoreProductModelImpl>
    implements _$$StoreProductModelImplCopyWith<$Res> {
  __$$StoreProductModelImplCopyWithImpl(_$StoreProductModelImpl _value,
      $Res Function(_$StoreProductModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StoreProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? comment = freezed,
    Object? note = freezed,
    Object? quantity = freezed,
    Object? expirationDate = freezed,
    Object? tag = freezed,
    Object? capacity = freezed,
    Object? price = freezed,
    Object? salePrice = freezed,
    Object? img = freezed,
  }) {
    return _then(_$StoreProductModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value._tag
          : tag // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      capacity: freezed == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      salePrice: freezed == salePrice
          ? _value.salePrice
          : salePrice // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StoreProductModelImpl implements _StoreProductModel {
  const _$StoreProductModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'name') this.name,
      @JsonKey(name: 'description') this.description,
      @JsonKey(name: 'comment') this.comment,
      @JsonKey(name: 'note') this.note,
      @JsonKey(name: 'quantity') this.quantity,
      @JsonKey(name: 'expiration_date') this.expirationDate,
      @JsonKey(name: 'tag') final List<String>? tag,
      @JsonKey(name: 'capacity') this.capacity,
      @JsonKey(name: 'price') this.price,
      @JsonKey(name: 'sale_price') this.salePrice,
      @JsonKey(name: 'img') this.img})
      : _tag = tag;

  factory _$StoreProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoreProductModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'name')
  final String? name;
  @override
  @JsonKey(name: 'description')
  final String? description;
  @override
  @JsonKey(name: 'comment')
  final String? comment;
  @override
  @JsonKey(name: 'note')
  final String? note;
  @override
  @JsonKey(name: 'quantity')
  final int? quantity;
  @override
  @JsonKey(name: 'expiration_date')
  final String? expirationDate;
  final List<String>? _tag;
  @override
  @JsonKey(name: 'tag')
  List<String>? get tag {
    final value = _tag;
    if (value == null) return null;
    if (_tag is EqualUnmodifiableListView) return _tag;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'capacity')
  final String? capacity;
  @override
  @JsonKey(name: 'price')
  final String? price;
  @override
  @JsonKey(name: 'sale_price')
  final String? salePrice;
  @override
  @JsonKey(name: 'img')
  final String? img;

  @override
  String toString() {
    return 'StoreProductModel(id: $id, name: $name, description: $description, comment: $comment, note: $note, quantity: $quantity, expirationDate: $expirationDate, tag: $tag, capacity: $capacity, price: $price, salePrice: $salePrice, img: $img)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoreProductModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.expirationDate, expirationDate) ||
                other.expirationDate == expirationDate) &&
            const DeepCollectionEquality().equals(other._tag, _tag) &&
            (identical(other.capacity, capacity) ||
                other.capacity == capacity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.salePrice, salePrice) ||
                other.salePrice == salePrice) &&
            (identical(other.img, img) || other.img == img));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      comment,
      note,
      quantity,
      expirationDate,
      const DeepCollectionEquality().hash(_tag),
      capacity,
      price,
      salePrice,
      img);

  /// Create a copy of StoreProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoreProductModelImplCopyWith<_$StoreProductModelImpl> get copyWith =>
      __$$StoreProductModelImplCopyWithImpl<_$StoreProductModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoreProductModelImplToJson(
      this,
    );
  }
}

abstract class _StoreProductModel implements StoreProductModel {
  const factory _StoreProductModel(
      {@JsonKey(name: 'id') final int? id,
      @JsonKey(name: 'name') final String? name,
      @JsonKey(name: 'description') final String? description,
      @JsonKey(name: 'comment') final String? comment,
      @JsonKey(name: 'note') final String? note,
      @JsonKey(name: 'quantity') final int? quantity,
      @JsonKey(name: 'expiration_date') final String? expirationDate,
      @JsonKey(name: 'tag') final List<String>? tag,
      @JsonKey(name: 'capacity') final String? capacity,
      @JsonKey(name: 'price') final String? price,
      @JsonKey(name: 'sale_price') final String? salePrice,
      @JsonKey(name: 'img') final String? img}) = _$StoreProductModelImpl;

  factory _StoreProductModel.fromJson(Map<String, dynamic> json) =
      _$StoreProductModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'name')
  String? get name;
  @override
  @JsonKey(name: 'description')
  String? get description;
  @override
  @JsonKey(name: 'comment')
  String? get comment;
  @override
  @JsonKey(name: 'note')
  String? get note;
  @override
  @JsonKey(name: 'quantity')
  int? get quantity;
  @override
  @JsonKey(name: 'expiration_date')
  String? get expirationDate;
  @override
  @JsonKey(name: 'tag')
  List<String>? get tag;
  @override
  @JsonKey(name: 'capacity')
  String? get capacity;
  @override
  @JsonKey(name: 'price')
  String? get price;
  @override
  @JsonKey(name: 'sale_price')
  String? get salePrice;
  @override
  @JsonKey(name: 'img')
  String? get img;

  /// Create a copy of StoreProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoreProductModelImplCopyWith<_$StoreProductModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoreShelfModel _$StoreShelfModelFromJson(Map<String, dynamic> json) {
  return _StoreShelfModel.fromJson(json);
}

/// @nodoc
mixin _$StoreShelfModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'shelf_code')
  String? get shelfCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  String? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'description')
  String? get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'height')
  int? get height => throw _privateConstructorUsedError;
  @JsonKey(name: 'width')
  int? get width => throw _privateConstructorUsedError;
  @JsonKey(name: 'depth')
  int? get depth => throw _privateConstructorUsedError;
  @JsonKey(name: 'stage')
  int? get stage => throw _privateConstructorUsedError;

  /// Serializes this StoreShelfModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoreShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoreShelfModelCopyWith<StoreShelfModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoreShelfModelCopyWith<$Res> {
  factory $StoreShelfModelCopyWith(
          StoreShelfModel value, $Res Function(StoreShelfModel) then) =
      _$StoreShelfModelCopyWithImpl<$Res, StoreShelfModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'shelf_code') String? shelfCode,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: 'description') String? description,
      @JsonKey(name: 'height') int? height,
      @JsonKey(name: 'width') int? width,
      @JsonKey(name: 'depth') int? depth,
      @JsonKey(name: 'stage') int? stage});
}

/// @nodoc
class _$StoreShelfModelCopyWithImpl<$Res, $Val extends StoreShelfModel>
    implements $StoreShelfModelCopyWith<$Res> {
  _$StoreShelfModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoreShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? shelfCode = freezed,
    Object? status = freezed,
    Object? description = freezed,
    Object? height = freezed,
    Object? width = freezed,
    Object? depth = freezed,
    Object? stage = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      shelfCode: freezed == shelfCode
          ? _value.shelfCode
          : shelfCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      depth: freezed == depth
          ? _value.depth
          : depth // ignore: cast_nullable_to_non_nullable
              as int?,
      stage: freezed == stage
          ? _value.stage
          : stage // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StoreShelfModelImplCopyWith<$Res>
    implements $StoreShelfModelCopyWith<$Res> {
  factory _$$StoreShelfModelImplCopyWith(_$StoreShelfModelImpl value,
          $Res Function(_$StoreShelfModelImpl) then) =
      __$$StoreShelfModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'shelf_code') String? shelfCode,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: 'description') String? description,
      @JsonKey(name: 'height') int? height,
      @JsonKey(name: 'width') int? width,
      @JsonKey(name: 'depth') int? depth,
      @JsonKey(name: 'stage') int? stage});
}

/// @nodoc
class __$$StoreShelfModelImplCopyWithImpl<$Res>
    extends _$StoreShelfModelCopyWithImpl<$Res, _$StoreShelfModelImpl>
    implements _$$StoreShelfModelImplCopyWith<$Res> {
  __$$StoreShelfModelImplCopyWithImpl(
      _$StoreShelfModelImpl _value, $Res Function(_$StoreShelfModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StoreShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? shelfCode = freezed,
    Object? status = freezed,
    Object? description = freezed,
    Object? height = freezed,
    Object? width = freezed,
    Object? depth = freezed,
    Object? stage = freezed,
  }) {
    return _then(_$StoreShelfModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      shelfCode: freezed == shelfCode
          ? _value.shelfCode
          : shelfCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      depth: freezed == depth
          ? _value.depth
          : depth // ignore: cast_nullable_to_non_nullable
              as int?,
      stage: freezed == stage
          ? _value.stage
          : stage // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StoreShelfModelImpl implements _StoreShelfModel {
  const _$StoreShelfModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'shelf_code') this.shelfCode,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: 'description') this.description,
      @JsonKey(name: 'height') this.height,
      @JsonKey(name: 'width') this.width,
      @JsonKey(name: 'depth') this.depth,
      @JsonKey(name: 'stage') this.stage});

  factory _$StoreShelfModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoreShelfModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'shelf_code')
  final String? shelfCode;
  @override
  @JsonKey(name: 'status')
  final String? status;
  @override
  @JsonKey(name: 'description')
  final String? description;
  @override
  @JsonKey(name: 'height')
  final int? height;
  @override
  @JsonKey(name: 'width')
  final int? width;
  @override
  @JsonKey(name: 'depth')
  final int? depth;
  @override
  @JsonKey(name: 'stage')
  final int? stage;

  @override
  String toString() {
    return 'StoreShelfModel(id: $id, shelfCode: $shelfCode, status: $status, description: $description, height: $height, width: $width, depth: $depth, stage: $stage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoreShelfModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.shelfCode, shelfCode) ||
                other.shelfCode == shelfCode) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.depth, depth) || other.depth == depth) &&
            (identical(other.stage, stage) || other.stage == stage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, shelfCode, status,
      description, height, width, depth, stage);

  /// Create a copy of StoreShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoreShelfModelImplCopyWith<_$StoreShelfModelImpl> get copyWith =>
      __$$StoreShelfModelImplCopyWithImpl<_$StoreShelfModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoreShelfModelImplToJson(
      this,
    );
  }
}

abstract class _StoreShelfModel implements StoreShelfModel {
  const factory _StoreShelfModel(
      {@JsonKey(name: 'id') final int? id,
      @JsonKey(name: 'shelf_code') final String? shelfCode,
      @JsonKey(name: 'status') final String? status,
      @JsonKey(name: 'description') final String? description,
      @JsonKey(name: 'height') final int? height,
      @JsonKey(name: 'width') final int? width,
      @JsonKey(name: 'depth') final int? depth,
      @JsonKey(name: 'stage') final int? stage}) = _$StoreShelfModelImpl;

  factory _StoreShelfModel.fromJson(Map<String, dynamic> json) =
      _$StoreShelfModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'shelf_code')
  String? get shelfCode;
  @override
  @JsonKey(name: 'status')
  String? get status;
  @override
  @JsonKey(name: 'description')
  String? get description;
  @override
  @JsonKey(name: 'height')
  int? get height;
  @override
  @JsonKey(name: 'width')
  int? get width;
  @override
  @JsonKey(name: 'depth')
  int? get depth;
  @override
  @JsonKey(name: 'stage')
  int? get stage;

  /// Create a copy of StoreShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoreShelfModelImplCopyWith<_$StoreShelfModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
