import 'package:freezed_annotation/freezed_annotation.dart';

part 'warehouse_model.freezed.dart';
part 'warehouse_model.g.dart';

@freezed
class WarehouseModel with _$WarehouseModel {
  const factory WarehouseModel({
    @J<PERSON><PERSON><PERSON>(name: 'id') int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'latitude') String? latitude,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'longitude') String? longitude,
    @Json<PERSON>ey(name: 'name') String? name,
    @JsonKey(name: 'zipcode') String? zipcode,
    @<PERSON>sonKey(name: 'province') String? province,
    @JsonKey(name: 'city') String? city,
    @JsonKey(name: 'street') String? street,
    @<PERSON>son<PERSON><PERSON>(name: 'building') String? building,
    @Json<PERSON>ey(name: 'tel') String? tel,
    @JsonKey(name: 'mobile') String? mobile,
    @JsonKey(name: 'email') String? email,
    @<PERSON>sonKey(name: 'img') String? img,
    @<PERSON>son<PERSON><PERSON>(name: 'open') String? open,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'close') String? close,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'products_count') int? productsCount,
    @<PERSON>son<PERSON><PERSON>(name: 'products_quantity') int? productsQuantity,
    @<PERSON>son<PERSON><PERSON>(name: 'status') String? status,
    @JsonKey(name: 'cabinets') List<CabinetModel>? cabinets,
  }) = _WarehouseModel;

  factory WarehouseModel.fromJson(Map<String, dynamic> json) =>
      _$WarehouseModelFromJson(json);
}

@freezed
class CabinetModel with _$CabinetModel {
  const factory CabinetModel({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'cabinet_code') String? cabinetCode,
    @JsonKey(name: 'description') String? description,
    @JsonKey(name: 'type') String? type,
    @JsonKey(name: 'products') List<WarehouseProductModel>? products,
    @JsonKey(name: 'lock') LockModel? lock,
  }) = _CabinetModel;

  factory CabinetModel.fromJson(Map<String, dynamic> json) =>
      _$CabinetModelFromJson(json);
}

@freezed
class LockModel with _$LockModel {
  const factory LockModel({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'mac_address') String? macAddress,
    @JsonKey(name: 'name') String? name,
  }) = _LockModel;

  factory LockModel.fromJson(Map<String, dynamic> json) =>
      _$LockModelFromJson(json);
}

@freezed
class WarehouseProductModel with _$WarehouseProductModel {
  const factory WarehouseProductModel({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'quantity') int? quantity,
    @JsonKey(name: 'img') String? img,
    @JsonKey(name: 'expiration_date') String? expirationDate,
    @JsonKey(name: 'price') String? price,
    @JsonKey(name: 'shelf') ShelfModel? shelf,
    @JsonKey(ignore: true) String? cabinetCode,
    @JsonKey(name: 'stock_history') StockHistoryModel? stockHistory,
  }) = _WarehouseProductModel;

  factory WarehouseProductModel.fromJson(Map<String, dynamic> json) =>
      _$WarehouseProductModelFromJson(json);
}

@freezed
class ShelfModel with _$ShelfModel {
  const factory ShelfModel({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'shelf_code') String? shelfCode,
    @JsonKey(name: 'status') String? status,
  }) = _ShelfModel;

  factory ShelfModel.fromJson(Map<String, dynamic> json) =>
      _$ShelfModelFromJson(json);
}

@freezed
class StockHistoryModel with _$StockHistoryModel {
  const factory StockHistoryModel({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'type') String? type,
    @JsonKey(name: 'amount') int? amount,
    @JsonKey(name: 'reason') String? reason,
    @JsonKey(name: 'date') String? date,
  }) = _StockHistoryModel;

  factory StockHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$StockHistoryModelFromJson(json);
}

extension WarehouseModelExtension on WarehouseModel {
  List<String> get tags {
    if (cabinets == null || cabinets!.isEmpty) return [];

    // Get all types from cabinets, filter out null values and duplicates
    return cabinets!
        .map((cabinet) => cabinet.type)
        .where((type) => type != null)
        .map((type) => type!)
        .toSet() // Remove duplicates
        .toList();
  }

  List<String> get listImage {
    if (cabinets == null || cabinets!.isEmpty) return [];

    // Get all images from products in all cabinets
    return cabinets!
        .expand((cabinet) =>
            cabinet.products ??
            <WarehouseProductModel>[]) // Flatten all products
        .map((product) => product.img)
        .where((img) =>
            img != null && img.isNotEmpty) // Filter out null and empty strings
        .map((img) => img!)
        .toList();
  }

  List<WarehouseProductModel> get products {
    if (cabinets == null || cabinets!.isEmpty) return [];

    return cabinets!.expand((cabinet) {
      final code = cabinet.cabinetCode;
      final products = cabinet.products ?? [];

      return products.map(
          (product) => product.copyWith(cabinetCode: code)); // Gắn cabinetCode
    }).toList();
  }

  bool get isWaiting => status?.toLowerCase() == 'waiting';
  bool get isActive => status?.toLowerCase() == 'active';
}

extension CabinetModelExtension on CabinetModel {
  bool get isCabinetActive {
    if (products == null || products!.isEmpty) {
      return false;
    }
    return products!.every((product) => product.shelf?.isShelfActive ?? false);
  }
}

extension ShelfModelExtension on ShelfModel {
  bool get isShelfActive => status?.toLowerCase() == 'booked';
}
