// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'store_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StoreDetailModelImpl _$$StoreDetailModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StoreDetailModelImpl(
      id: (json['id'] as num?)?.toInt(),
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
      name: json['name'] as String?,
      zipcode: json['zipcode'] as String?,
      province: json['province'] as String?,
      city: json['city'] as String?,
      street: json['street'] as String?,
      building: json['building'] as String?,
      tel: json['tel'] as String?,
      mobile: json['mobile'] as String?,
      email: json['email'] as String?,
      img: json['img'] as String?,
      open: json['open'] as String?,
      close: json['close'] as String?,
      cabinets: (json['cabinets'] as List<dynamic>?)
          ?.map((e) => StoreCabinetModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      availableShelvesCount: (json['available_shelves_count'] as num?)?.toInt(),
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$StoreDetailModelImplToJson(
        _$StoreDetailModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'name': instance.name,
      'zipcode': instance.zipcode,
      'province': instance.province,
      'city': instance.city,
      'street': instance.street,
      'building': instance.building,
      'tel': instance.tel,
      'mobile': instance.mobile,
      'email': instance.email,
      'img': instance.img,
      'open': instance.open,
      'close': instance.close,
      'cabinets': instance.cabinets,
      'available_shelves_count': instance.availableShelvesCount,
      'images': instance.images,
    };

_$StoreCabinetModelImpl _$$StoreCabinetModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StoreCabinetModelImpl(
      id: (json['id'] as num?)?.toInt(),
      cabinetCode: json['cabinet_code'] as String?,
      description: json['description'] as String?,
      type: json['type'] as String?,
      products: (json['products'] as List<dynamic>?)
          ?.map((e) => StoreProductModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      shelves: (json['shelves'] as List<dynamic>?)
          ?.map((e) => StoreShelfModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StoreCabinetModelImplToJson(
        _$StoreCabinetModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cabinet_code': instance.cabinetCode,
      'description': instance.description,
      'type': instance.type,
      'products': instance.products,
      'shelves': instance.shelves,
    };

_$StoreProductModelImpl _$$StoreProductModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StoreProductModelImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      description: json['description'] as String?,
      comment: json['comment'] as String?,
      note: json['note'] as String?,
      quantity: (json['quantity'] as num?)?.toInt(),
      expirationDate: json['expiration_date'] as String?,
      tag: (json['tag'] as List<dynamic>?)?.map((e) => e as String).toList(),
      capacity: json['capacity'] as String?,
      price: json['price'] as String?,
      salePrice: json['sale_price'] as String?,
      img: json['img'] as String?,
    );

Map<String, dynamic> _$$StoreProductModelImplToJson(
        _$StoreProductModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'comment': instance.comment,
      'note': instance.note,
      'quantity': instance.quantity,
      'expiration_date': instance.expirationDate,
      'tag': instance.tag,
      'capacity': instance.capacity,
      'price': instance.price,
      'sale_price': instance.salePrice,
      'img': instance.img,
    };

_$StoreShelfModelImpl _$$StoreShelfModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StoreShelfModelImpl(
      id: (json['id'] as num?)?.toInt(),
      shelfCode: json['shelf_code'] as String?,
      status: json['status'] as String?,
      description: json['description'] as String?,
      height: (json['height'] as num?)?.toInt(),
      width: (json['width'] as num?)?.toInt(),
      depth: (json['depth'] as num?)?.toInt(),
      stage: (json['stage'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$StoreShelfModelImplToJson(
        _$StoreShelfModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'shelf_code': instance.shelfCode,
      'status': instance.status,
      'description': instance.description,
      'height': instance.height,
      'width': instance.width,
      'depth': instance.depth,
      'stage': instance.stage,
    };
