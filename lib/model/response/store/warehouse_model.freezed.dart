// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'warehouse_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WarehouseModel _$WarehouseModelFromJson(Map<String, dynamic> json) {
  return _WarehouseModel.fromJson(json);
}

/// @nodoc
mixin _$WarehouseModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'latitude')
  String? get latitude => throw _privateConstructorUsedError;
  @JsonKey(name: 'longitude')
  String? get longitude => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'zipcode')
  String? get zipcode => throw _privateConstructorUsedError;
  @JsonKey(name: 'province')
  String? get province => throw _privateConstructorUsedError;
  @JsonKey(name: 'city')
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: 'street')
  String? get street => throw _privateConstructorUsedError;
  @JsonKey(name: 'building')
  String? get building => throw _privateConstructorUsedError;
  @JsonKey(name: 'tel')
  String? get tel => throw _privateConstructorUsedError;
  @JsonKey(name: 'mobile')
  String? get mobile => throw _privateConstructorUsedError;
  @JsonKey(name: 'email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'img')
  String? get img => throw _privateConstructorUsedError;
  @JsonKey(name: 'open')
  String? get open => throw _privateConstructorUsedError;
  @JsonKey(name: 'close')
  String? get close => throw _privateConstructorUsedError;
  @JsonKey(name: 'products_count')
  int? get productsCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'products_quantity')
  int? get productsQuantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  String? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'cabinets')
  List<CabinetModel>? get cabinets => throw _privateConstructorUsedError;

  /// Serializes this WarehouseModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehouseModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseModelCopyWith<WarehouseModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseModelCopyWith<$Res> {
  factory $WarehouseModelCopyWith(
          WarehouseModel value, $Res Function(WarehouseModel) then) =
      _$WarehouseModelCopyWithImpl<$Res, WarehouseModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'latitude') String? latitude,
      @JsonKey(name: 'longitude') String? longitude,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: 'zipcode') String? zipcode,
      @JsonKey(name: 'province') String? province,
      @JsonKey(name: 'city') String? city,
      @JsonKey(name: 'street') String? street,
      @JsonKey(name: 'building') String? building,
      @JsonKey(name: 'tel') String? tel,
      @JsonKey(name: 'mobile') String? mobile,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'img') String? img,
      @JsonKey(name: 'open') String? open,
      @JsonKey(name: 'close') String? close,
      @JsonKey(name: 'products_count') int? productsCount,
      @JsonKey(name: 'products_quantity') int? productsQuantity,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: 'cabinets') List<CabinetModel>? cabinets});
}

/// @nodoc
class _$WarehouseModelCopyWithImpl<$Res, $Val extends WarehouseModel>
    implements $WarehouseModelCopyWith<$Res> {
  _$WarehouseModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehouseModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? name = freezed,
    Object? zipcode = freezed,
    Object? province = freezed,
    Object? city = freezed,
    Object? street = freezed,
    Object? building = freezed,
    Object? tel = freezed,
    Object? mobile = freezed,
    Object? email = freezed,
    Object? img = freezed,
    Object? open = freezed,
    Object? close = freezed,
    Object? productsCount = freezed,
    Object? productsQuantity = freezed,
    Object? status = freezed,
    Object? cabinets = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as String?,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      zipcode: freezed == zipcode
          ? _value.zipcode
          : zipcode // ignore: cast_nullable_to_non_nullable
              as String?,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      building: freezed == building
          ? _value.building
          : building // ignore: cast_nullable_to_non_nullable
              as String?,
      tel: freezed == tel
          ? _value.tel
          : tel // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      open: freezed == open
          ? _value.open
          : open // ignore: cast_nullable_to_non_nullable
              as String?,
      close: freezed == close
          ? _value.close
          : close // ignore: cast_nullable_to_non_nullable
              as String?,
      productsCount: freezed == productsCount
          ? _value.productsCount
          : productsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      productsQuantity: freezed == productsQuantity
          ? _value.productsQuantity
          : productsQuantity // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      cabinets: freezed == cabinets
          ? _value.cabinets
          : cabinets // ignore: cast_nullable_to_non_nullable
              as List<CabinetModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehouseModelImplCopyWith<$Res>
    implements $WarehouseModelCopyWith<$Res> {
  factory _$$WarehouseModelImplCopyWith(_$WarehouseModelImpl value,
          $Res Function(_$WarehouseModelImpl) then) =
      __$$WarehouseModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'latitude') String? latitude,
      @JsonKey(name: 'longitude') String? longitude,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: 'zipcode') String? zipcode,
      @JsonKey(name: 'province') String? province,
      @JsonKey(name: 'city') String? city,
      @JsonKey(name: 'street') String? street,
      @JsonKey(name: 'building') String? building,
      @JsonKey(name: 'tel') String? tel,
      @JsonKey(name: 'mobile') String? mobile,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'img') String? img,
      @JsonKey(name: 'open') String? open,
      @JsonKey(name: 'close') String? close,
      @JsonKey(name: 'products_count') int? productsCount,
      @JsonKey(name: 'products_quantity') int? productsQuantity,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: 'cabinets') List<CabinetModel>? cabinets});
}

/// @nodoc
class __$$WarehouseModelImplCopyWithImpl<$Res>
    extends _$WarehouseModelCopyWithImpl<$Res, _$WarehouseModelImpl>
    implements _$$WarehouseModelImplCopyWith<$Res> {
  __$$WarehouseModelImplCopyWithImpl(
      _$WarehouseModelImpl _value, $Res Function(_$WarehouseModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehouseModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? name = freezed,
    Object? zipcode = freezed,
    Object? province = freezed,
    Object? city = freezed,
    Object? street = freezed,
    Object? building = freezed,
    Object? tel = freezed,
    Object? mobile = freezed,
    Object? email = freezed,
    Object? img = freezed,
    Object? open = freezed,
    Object? close = freezed,
    Object? productsCount = freezed,
    Object? productsQuantity = freezed,
    Object? status = freezed,
    Object? cabinets = freezed,
  }) {
    return _then(_$WarehouseModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as String?,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      zipcode: freezed == zipcode
          ? _value.zipcode
          : zipcode // ignore: cast_nullable_to_non_nullable
              as String?,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      building: freezed == building
          ? _value.building
          : building // ignore: cast_nullable_to_non_nullable
              as String?,
      tel: freezed == tel
          ? _value.tel
          : tel // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      open: freezed == open
          ? _value.open
          : open // ignore: cast_nullable_to_non_nullable
              as String?,
      close: freezed == close
          ? _value.close
          : close // ignore: cast_nullable_to_non_nullable
              as String?,
      productsCount: freezed == productsCount
          ? _value.productsCount
          : productsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      productsQuantity: freezed == productsQuantity
          ? _value.productsQuantity
          : productsQuantity // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      cabinets: freezed == cabinets
          ? _value._cabinets
          : cabinets // ignore: cast_nullable_to_non_nullable
              as List<CabinetModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WarehouseModelImpl implements _WarehouseModel {
  const _$WarehouseModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'latitude') this.latitude,
      @JsonKey(name: 'longitude') this.longitude,
      @JsonKey(name: 'name') this.name,
      @JsonKey(name: 'zipcode') this.zipcode,
      @JsonKey(name: 'province') this.province,
      @JsonKey(name: 'city') this.city,
      @JsonKey(name: 'street') this.street,
      @JsonKey(name: 'building') this.building,
      @JsonKey(name: 'tel') this.tel,
      @JsonKey(name: 'mobile') this.mobile,
      @JsonKey(name: 'email') this.email,
      @JsonKey(name: 'img') this.img,
      @JsonKey(name: 'open') this.open,
      @JsonKey(name: 'close') this.close,
      @JsonKey(name: 'products_count') this.productsCount,
      @JsonKey(name: 'products_quantity') this.productsQuantity,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: 'cabinets') final List<CabinetModel>? cabinets})
      : _cabinets = cabinets;

  factory _$WarehouseModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'latitude')
  final String? latitude;
  @override
  @JsonKey(name: 'longitude')
  final String? longitude;
  @override
  @JsonKey(name: 'name')
  final String? name;
  @override
  @JsonKey(name: 'zipcode')
  final String? zipcode;
  @override
  @JsonKey(name: 'province')
  final String? province;
  @override
  @JsonKey(name: 'city')
  final String? city;
  @override
  @JsonKey(name: 'street')
  final String? street;
  @override
  @JsonKey(name: 'building')
  final String? building;
  @override
  @JsonKey(name: 'tel')
  final String? tel;
  @override
  @JsonKey(name: 'mobile')
  final String? mobile;
  @override
  @JsonKey(name: 'email')
  final String? email;
  @override
  @JsonKey(name: 'img')
  final String? img;
  @override
  @JsonKey(name: 'open')
  final String? open;
  @override
  @JsonKey(name: 'close')
  final String? close;
  @override
  @JsonKey(name: 'products_count')
  final int? productsCount;
  @override
  @JsonKey(name: 'products_quantity')
  final int? productsQuantity;
  @override
  @JsonKey(name: 'status')
  final String? status;
  final List<CabinetModel>? _cabinets;
  @override
  @JsonKey(name: 'cabinets')
  List<CabinetModel>? get cabinets {
    final value = _cabinets;
    if (value == null) return null;
    if (_cabinets is EqualUnmodifiableListView) return _cabinets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'WarehouseModel(id: $id, latitude: $latitude, longitude: $longitude, name: $name, zipcode: $zipcode, province: $province, city: $city, street: $street, building: $building, tel: $tel, mobile: $mobile, email: $email, img: $img, open: $open, close: $close, productsCount: $productsCount, productsQuantity: $productsQuantity, status: $status, cabinets: $cabinets)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.zipcode, zipcode) || other.zipcode == zipcode) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.building, building) ||
                other.building == building) &&
            (identical(other.tel, tel) || other.tel == tel) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.open, open) || other.open == open) &&
            (identical(other.close, close) || other.close == close) &&
            (identical(other.productsCount, productsCount) ||
                other.productsCount == productsCount) &&
            (identical(other.productsQuantity, productsQuantity) ||
                other.productsQuantity == productsQuantity) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._cabinets, _cabinets));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        latitude,
        longitude,
        name,
        zipcode,
        province,
        city,
        street,
        building,
        tel,
        mobile,
        email,
        img,
        open,
        close,
        productsCount,
        productsQuantity,
        status,
        const DeepCollectionEquality().hash(_cabinets)
      ]);

  /// Create a copy of WarehouseModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseModelImplCopyWith<_$WarehouseModelImpl> get copyWith =>
      __$$WarehouseModelImplCopyWithImpl<_$WarehouseModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseModelImplToJson(
      this,
    );
  }
}

abstract class _WarehouseModel implements WarehouseModel {
  const factory _WarehouseModel(
          {@JsonKey(name: 'id') final int? id,
          @JsonKey(name: 'latitude') final String? latitude,
          @JsonKey(name: 'longitude') final String? longitude,
          @JsonKey(name: 'name') final String? name,
          @JsonKey(name: 'zipcode') final String? zipcode,
          @JsonKey(name: 'province') final String? province,
          @JsonKey(name: 'city') final String? city,
          @JsonKey(name: 'street') final String? street,
          @JsonKey(name: 'building') final String? building,
          @JsonKey(name: 'tel') final String? tel,
          @JsonKey(name: 'mobile') final String? mobile,
          @JsonKey(name: 'email') final String? email,
          @JsonKey(name: 'img') final String? img,
          @JsonKey(name: 'open') final String? open,
          @JsonKey(name: 'close') final String? close,
          @JsonKey(name: 'products_count') final int? productsCount,
          @JsonKey(name: 'products_quantity') final int? productsQuantity,
          @JsonKey(name: 'status') final String? status,
          @JsonKey(name: 'cabinets') final List<CabinetModel>? cabinets}) =
      _$WarehouseModelImpl;

  factory _WarehouseModel.fromJson(Map<String, dynamic> json) =
      _$WarehouseModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'latitude')
  String? get latitude;
  @override
  @JsonKey(name: 'longitude')
  String? get longitude;
  @override
  @JsonKey(name: 'name')
  String? get name;
  @override
  @JsonKey(name: 'zipcode')
  String? get zipcode;
  @override
  @JsonKey(name: 'province')
  String? get province;
  @override
  @JsonKey(name: 'city')
  String? get city;
  @override
  @JsonKey(name: 'street')
  String? get street;
  @override
  @JsonKey(name: 'building')
  String? get building;
  @override
  @JsonKey(name: 'tel')
  String? get tel;
  @override
  @JsonKey(name: 'mobile')
  String? get mobile;
  @override
  @JsonKey(name: 'email')
  String? get email;
  @override
  @JsonKey(name: 'img')
  String? get img;
  @override
  @JsonKey(name: 'open')
  String? get open;
  @override
  @JsonKey(name: 'close')
  String? get close;
  @override
  @JsonKey(name: 'products_count')
  int? get productsCount;
  @override
  @JsonKey(name: 'products_quantity')
  int? get productsQuantity;
  @override
  @JsonKey(name: 'status')
  String? get status;
  @override
  @JsonKey(name: 'cabinets')
  List<CabinetModel>? get cabinets;

  /// Create a copy of WarehouseModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseModelImplCopyWith<_$WarehouseModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CabinetModel _$CabinetModelFromJson(Map<String, dynamic> json) {
  return _CabinetModel.fromJson(json);
}

/// @nodoc
mixin _$CabinetModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'cabinet_code')
  String? get cabinetCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'description')
  String? get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'type')
  String? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'products')
  List<WarehouseProductModel>? get products =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'lock')
  LockModel? get lock => throw _privateConstructorUsedError;

  /// Serializes this CabinetModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CabinetModelCopyWith<CabinetModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CabinetModelCopyWith<$Res> {
  factory $CabinetModelCopyWith(
          CabinetModel value, $Res Function(CabinetModel) then) =
      _$CabinetModelCopyWithImpl<$Res, CabinetModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'cabinet_code') String? cabinetCode,
      @JsonKey(name: 'description') String? description,
      @JsonKey(name: 'type') String? type,
      @JsonKey(name: 'products') List<WarehouseProductModel>? products,
      @JsonKey(name: 'lock') LockModel? lock});

  $LockModelCopyWith<$Res>? get lock;
}

/// @nodoc
class _$CabinetModelCopyWithImpl<$Res, $Val extends CabinetModel>
    implements $CabinetModelCopyWith<$Res> {
  _$CabinetModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cabinetCode = freezed,
    Object? description = freezed,
    Object? type = freezed,
    Object? products = freezed,
    Object? lock = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      products: freezed == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<WarehouseProductModel>?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as LockModel?,
    ) as $Val);
  }

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LockModelCopyWith<$Res>? get lock {
    if (_value.lock == null) {
      return null;
    }

    return $LockModelCopyWith<$Res>(_value.lock!, (value) {
      return _then(_value.copyWith(lock: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CabinetModelImplCopyWith<$Res>
    implements $CabinetModelCopyWith<$Res> {
  factory _$$CabinetModelImplCopyWith(
          _$CabinetModelImpl value, $Res Function(_$CabinetModelImpl) then) =
      __$$CabinetModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'cabinet_code') String? cabinetCode,
      @JsonKey(name: 'description') String? description,
      @JsonKey(name: 'type') String? type,
      @JsonKey(name: 'products') List<WarehouseProductModel>? products,
      @JsonKey(name: 'lock') LockModel? lock});

  @override
  $LockModelCopyWith<$Res>? get lock;
}

/// @nodoc
class __$$CabinetModelImplCopyWithImpl<$Res>
    extends _$CabinetModelCopyWithImpl<$Res, _$CabinetModelImpl>
    implements _$$CabinetModelImplCopyWith<$Res> {
  __$$CabinetModelImplCopyWithImpl(
      _$CabinetModelImpl _value, $Res Function(_$CabinetModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cabinetCode = freezed,
    Object? description = freezed,
    Object? type = freezed,
    Object? products = freezed,
    Object? lock = freezed,
  }) {
    return _then(_$CabinetModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      products: freezed == products
          ? _value._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<WarehouseProductModel>?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as LockModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CabinetModelImpl implements _CabinetModel {
  const _$CabinetModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'cabinet_code') this.cabinetCode,
      @JsonKey(name: 'description') this.description,
      @JsonKey(name: 'type') this.type,
      @JsonKey(name: 'products') final List<WarehouseProductModel>? products,
      @JsonKey(name: 'lock') this.lock})
      : _products = products;

  factory _$CabinetModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CabinetModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'cabinet_code')
  final String? cabinetCode;
  @override
  @JsonKey(name: 'description')
  final String? description;
  @override
  @JsonKey(name: 'type')
  final String? type;
  final List<WarehouseProductModel>? _products;
  @override
  @JsonKey(name: 'products')
  List<WarehouseProductModel>? get products {
    final value = _products;
    if (value == null) return null;
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'lock')
  final LockModel? lock;

  @override
  String toString() {
    return 'CabinetModel(id: $id, cabinetCode: $cabinetCode, description: $description, type: $type, products: $products, lock: $lock)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CabinetModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cabinetCode, cabinetCode) ||
                other.cabinetCode == cabinetCode) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            (identical(other.lock, lock) || other.lock == lock));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, cabinetCode, description,
      type, const DeepCollectionEquality().hash(_products), lock);

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CabinetModelImplCopyWith<_$CabinetModelImpl> get copyWith =>
      __$$CabinetModelImplCopyWithImpl<_$CabinetModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CabinetModelImplToJson(
      this,
    );
  }
}

abstract class _CabinetModel implements CabinetModel {
  const factory _CabinetModel(
      {@JsonKey(name: 'id') final int? id,
      @JsonKey(name: 'cabinet_code') final String? cabinetCode,
      @JsonKey(name: 'description') final String? description,
      @JsonKey(name: 'type') final String? type,
      @JsonKey(name: 'products') final List<WarehouseProductModel>? products,
      @JsonKey(name: 'lock') final LockModel? lock}) = _$CabinetModelImpl;

  factory _CabinetModel.fromJson(Map<String, dynamic> json) =
      _$CabinetModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'cabinet_code')
  String? get cabinetCode;
  @override
  @JsonKey(name: 'description')
  String? get description;
  @override
  @JsonKey(name: 'type')
  String? get type;
  @override
  @JsonKey(name: 'products')
  List<WarehouseProductModel>? get products;
  @override
  @JsonKey(name: 'lock')
  LockModel? get lock;

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CabinetModelImplCopyWith<_$CabinetModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LockModel _$LockModelFromJson(Map<String, dynamic> json) {
  return _LockModel.fromJson(json);
}

/// @nodoc
mixin _$LockModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'mac_address')
  String? get macAddress => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String? get name => throw _privateConstructorUsedError;

  /// Serializes this LockModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LockModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LockModelCopyWith<LockModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LockModelCopyWith<$Res> {
  factory $LockModelCopyWith(LockModel value, $Res Function(LockModel) then) =
      _$LockModelCopyWithImpl<$Res, LockModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'mac_address') String? macAddress,
      @JsonKey(name: 'name') String? name});
}

/// @nodoc
class _$LockModelCopyWithImpl<$Res, $Val extends LockModel>
    implements $LockModelCopyWith<$Res> {
  _$LockModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LockModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? macAddress = freezed,
    Object? name = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      macAddress: freezed == macAddress
          ? _value.macAddress
          : macAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LockModelImplCopyWith<$Res>
    implements $LockModelCopyWith<$Res> {
  factory _$$LockModelImplCopyWith(
          _$LockModelImpl value, $Res Function(_$LockModelImpl) then) =
      __$$LockModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'mac_address') String? macAddress,
      @JsonKey(name: 'name') String? name});
}

/// @nodoc
class __$$LockModelImplCopyWithImpl<$Res>
    extends _$LockModelCopyWithImpl<$Res, _$LockModelImpl>
    implements _$$LockModelImplCopyWith<$Res> {
  __$$LockModelImplCopyWithImpl(
      _$LockModelImpl _value, $Res Function(_$LockModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of LockModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? macAddress = freezed,
    Object? name = freezed,
  }) {
    return _then(_$LockModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      macAddress: freezed == macAddress
          ? _value.macAddress
          : macAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LockModelImpl implements _LockModel {
  const _$LockModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'mac_address') this.macAddress,
      @JsonKey(name: 'name') this.name});

  factory _$LockModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LockModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'mac_address')
  final String? macAddress;
  @override
  @JsonKey(name: 'name')
  final String? name;

  @override
  String toString() {
    return 'LockModel(id: $id, macAddress: $macAddress, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LockModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.macAddress, macAddress) ||
                other.macAddress == macAddress) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, macAddress, name);

  /// Create a copy of LockModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LockModelImplCopyWith<_$LockModelImpl> get copyWith =>
      __$$LockModelImplCopyWithImpl<_$LockModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LockModelImplToJson(
      this,
    );
  }
}

abstract class _LockModel implements LockModel {
  const factory _LockModel(
      {@JsonKey(name: 'id') final int? id,
      @JsonKey(name: 'mac_address') final String? macAddress,
      @JsonKey(name: 'name') final String? name}) = _$LockModelImpl;

  factory _LockModel.fromJson(Map<String, dynamic> json) =
      _$LockModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'mac_address')
  String? get macAddress;
  @override
  @JsonKey(name: 'name')
  String? get name;

  /// Create a copy of LockModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LockModelImplCopyWith<_$LockModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WarehouseProductModel _$WarehouseProductModelFromJson(
    Map<String, dynamic> json) {
  return _WarehouseProductModel.fromJson(json);
}

/// @nodoc
mixin _$WarehouseProductModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'quantity')
  int? get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'img')
  String? get img => throw _privateConstructorUsedError;
  @JsonKey(name: 'expiration_date')
  String? get expirationDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'price')
  String? get price => throw _privateConstructorUsedError;
  @JsonKey(name: 'shelf')
  ShelfModel? get shelf => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  String? get cabinetCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock_history')
  StockHistoryModel? get stockHistory => throw _privateConstructorUsedError;

  /// Serializes this WarehouseProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehouseProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseProductModelCopyWith<WarehouseProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseProductModelCopyWith<$Res> {
  factory $WarehouseProductModelCopyWith(WarehouseProductModel value,
          $Res Function(WarehouseProductModel) then) =
      _$WarehouseProductModelCopyWithImpl<$Res, WarehouseProductModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: 'quantity') int? quantity,
      @JsonKey(name: 'img') String? img,
      @JsonKey(name: 'expiration_date') String? expirationDate,
      @JsonKey(name: 'price') String? price,
      @JsonKey(name: 'shelf') ShelfModel? shelf,
      @JsonKey(ignore: true) String? cabinetCode,
      @JsonKey(name: 'stock_history') StockHistoryModel? stockHistory});

  $ShelfModelCopyWith<$Res>? get shelf;
  $StockHistoryModelCopyWith<$Res>? get stockHistory;
}

/// @nodoc
class _$WarehouseProductModelCopyWithImpl<$Res,
        $Val extends WarehouseProductModel>
    implements $WarehouseProductModelCopyWith<$Res> {
  _$WarehouseProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehouseProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? quantity = freezed,
    Object? img = freezed,
    Object? expirationDate = freezed,
    Object? price = freezed,
    Object? shelf = freezed,
    Object? cabinetCode = freezed,
    Object? stockHistory = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      shelf: freezed == shelf
          ? _value.shelf
          : shelf // ignore: cast_nullable_to_non_nullable
              as ShelfModel?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      stockHistory: freezed == stockHistory
          ? _value.stockHistory
          : stockHistory // ignore: cast_nullable_to_non_nullable
              as StockHistoryModel?,
    ) as $Val);
  }

  /// Create a copy of WarehouseProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShelfModelCopyWith<$Res>? get shelf {
    if (_value.shelf == null) {
      return null;
    }

    return $ShelfModelCopyWith<$Res>(_value.shelf!, (value) {
      return _then(_value.copyWith(shelf: value) as $Val);
    });
  }

  /// Create a copy of WarehouseProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $StockHistoryModelCopyWith<$Res>? get stockHistory {
    if (_value.stockHistory == null) {
      return null;
    }

    return $StockHistoryModelCopyWith<$Res>(_value.stockHistory!, (value) {
      return _then(_value.copyWith(stockHistory: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WarehouseProductModelImplCopyWith<$Res>
    implements $WarehouseProductModelCopyWith<$Res> {
  factory _$$WarehouseProductModelImplCopyWith(
          _$WarehouseProductModelImpl value,
          $Res Function(_$WarehouseProductModelImpl) then) =
      __$$WarehouseProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: 'quantity') int? quantity,
      @JsonKey(name: 'img') String? img,
      @JsonKey(name: 'expiration_date') String? expirationDate,
      @JsonKey(name: 'price') String? price,
      @JsonKey(name: 'shelf') ShelfModel? shelf,
      @JsonKey(ignore: true) String? cabinetCode,
      @JsonKey(name: 'stock_history') StockHistoryModel? stockHistory});

  @override
  $ShelfModelCopyWith<$Res>? get shelf;
  @override
  $StockHistoryModelCopyWith<$Res>? get stockHistory;
}

/// @nodoc
class __$$WarehouseProductModelImplCopyWithImpl<$Res>
    extends _$WarehouseProductModelCopyWithImpl<$Res,
        _$WarehouseProductModelImpl>
    implements _$$WarehouseProductModelImplCopyWith<$Res> {
  __$$WarehouseProductModelImplCopyWithImpl(_$WarehouseProductModelImpl _value,
      $Res Function(_$WarehouseProductModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehouseProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? quantity = freezed,
    Object? img = freezed,
    Object? expirationDate = freezed,
    Object? price = freezed,
    Object? shelf = freezed,
    Object? cabinetCode = freezed,
    Object? stockHistory = freezed,
  }) {
    return _then(_$WarehouseProductModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      shelf: freezed == shelf
          ? _value.shelf
          : shelf // ignore: cast_nullable_to_non_nullable
              as ShelfModel?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      stockHistory: freezed == stockHistory
          ? _value.stockHistory
          : stockHistory // ignore: cast_nullable_to_non_nullable
              as StockHistoryModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WarehouseProductModelImpl implements _WarehouseProductModel {
  const _$WarehouseProductModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'name') this.name,
      @JsonKey(name: 'quantity') this.quantity,
      @JsonKey(name: 'img') this.img,
      @JsonKey(name: 'expiration_date') this.expirationDate,
      @JsonKey(name: 'price') this.price,
      @JsonKey(name: 'shelf') this.shelf,
      @JsonKey(ignore: true) this.cabinetCode,
      @JsonKey(name: 'stock_history') this.stockHistory});

  factory _$WarehouseProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseProductModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'name')
  final String? name;
  @override
  @JsonKey(name: 'quantity')
  final int? quantity;
  @override
  @JsonKey(name: 'img')
  final String? img;
  @override
  @JsonKey(name: 'expiration_date')
  final String? expirationDate;
  @override
  @JsonKey(name: 'price')
  final String? price;
  @override
  @JsonKey(name: 'shelf')
  final ShelfModel? shelf;
  @override
  @JsonKey(ignore: true)
  final String? cabinetCode;
  @override
  @JsonKey(name: 'stock_history')
  final StockHistoryModel? stockHistory;

  @override
  String toString() {
    return 'WarehouseProductModel(id: $id, name: $name, quantity: $quantity, img: $img, expirationDate: $expirationDate, price: $price, shelf: $shelf, cabinetCode: $cabinetCode, stockHistory: $stockHistory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseProductModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.expirationDate, expirationDate) ||
                other.expirationDate == expirationDate) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.shelf, shelf) || other.shelf == shelf) &&
            (identical(other.cabinetCode, cabinetCode) ||
                other.cabinetCode == cabinetCode) &&
            (identical(other.stockHistory, stockHistory) ||
                other.stockHistory == stockHistory));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, quantity, img,
      expirationDate, price, shelf, cabinetCode, stockHistory);

  /// Create a copy of WarehouseProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseProductModelImplCopyWith<_$WarehouseProductModelImpl>
      get copyWith => __$$WarehouseProductModelImplCopyWithImpl<
          _$WarehouseProductModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseProductModelImplToJson(
      this,
    );
  }
}

abstract class _WarehouseProductModel implements WarehouseProductModel {
  const factory _WarehouseProductModel(
      {@JsonKey(name: 'id') final int? id,
      @JsonKey(name: 'name') final String? name,
      @JsonKey(name: 'quantity') final int? quantity,
      @JsonKey(name: 'img') final String? img,
      @JsonKey(name: 'expiration_date') final String? expirationDate,
      @JsonKey(name: 'price') final String? price,
      @JsonKey(name: 'shelf') final ShelfModel? shelf,
      @JsonKey(ignore: true) final String? cabinetCode,
      @JsonKey(name: 'stock_history')
      final StockHistoryModel? stockHistory}) = _$WarehouseProductModelImpl;

  factory _WarehouseProductModel.fromJson(Map<String, dynamic> json) =
      _$WarehouseProductModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'name')
  String? get name;
  @override
  @JsonKey(name: 'quantity')
  int? get quantity;
  @override
  @JsonKey(name: 'img')
  String? get img;
  @override
  @JsonKey(name: 'expiration_date')
  String? get expirationDate;
  @override
  @JsonKey(name: 'price')
  String? get price;
  @override
  @JsonKey(name: 'shelf')
  ShelfModel? get shelf;
  @override
  @JsonKey(ignore: true)
  String? get cabinetCode;
  @override
  @JsonKey(name: 'stock_history')
  StockHistoryModel? get stockHistory;

  /// Create a copy of WarehouseProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseProductModelImplCopyWith<_$WarehouseProductModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ShelfModel _$ShelfModelFromJson(Map<String, dynamic> json) {
  return _ShelfModel.fromJson(json);
}

/// @nodoc
mixin _$ShelfModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'shelf_code')
  String? get shelfCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  String? get status => throw _privateConstructorUsedError;

  /// Serializes this ShelfModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShelfModelCopyWith<ShelfModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShelfModelCopyWith<$Res> {
  factory $ShelfModelCopyWith(
          ShelfModel value, $Res Function(ShelfModel) then) =
      _$ShelfModelCopyWithImpl<$Res, ShelfModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'shelf_code') String? shelfCode,
      @JsonKey(name: 'status') String? status});
}

/// @nodoc
class _$ShelfModelCopyWithImpl<$Res, $Val extends ShelfModel>
    implements $ShelfModelCopyWith<$Res> {
  _$ShelfModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? shelfCode = freezed,
    Object? status = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      shelfCode: freezed == shelfCode
          ? _value.shelfCode
          : shelfCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShelfModelImplCopyWith<$Res>
    implements $ShelfModelCopyWith<$Res> {
  factory _$$ShelfModelImplCopyWith(
          _$ShelfModelImpl value, $Res Function(_$ShelfModelImpl) then) =
      __$$ShelfModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'shelf_code') String? shelfCode,
      @JsonKey(name: 'status') String? status});
}

/// @nodoc
class __$$ShelfModelImplCopyWithImpl<$Res>
    extends _$ShelfModelCopyWithImpl<$Res, _$ShelfModelImpl>
    implements _$$ShelfModelImplCopyWith<$Res> {
  __$$ShelfModelImplCopyWithImpl(
      _$ShelfModelImpl _value, $Res Function(_$ShelfModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? shelfCode = freezed,
    Object? status = freezed,
  }) {
    return _then(_$ShelfModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      shelfCode: freezed == shelfCode
          ? _value.shelfCode
          : shelfCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShelfModelImpl implements _ShelfModel {
  const _$ShelfModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'shelf_code') this.shelfCode,
      @JsonKey(name: 'status') this.status});

  factory _$ShelfModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShelfModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'shelf_code')
  final String? shelfCode;
  @override
  @JsonKey(name: 'status')
  final String? status;

  @override
  String toString() {
    return 'ShelfModel(id: $id, shelfCode: $shelfCode, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShelfModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.shelfCode, shelfCode) ||
                other.shelfCode == shelfCode) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, shelfCode, status);

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShelfModelImplCopyWith<_$ShelfModelImpl> get copyWith =>
      __$$ShelfModelImplCopyWithImpl<_$ShelfModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShelfModelImplToJson(
      this,
    );
  }
}

abstract class _ShelfModel implements ShelfModel {
  const factory _ShelfModel(
      {@JsonKey(name: 'id') final int? id,
      @JsonKey(name: 'shelf_code') final String? shelfCode,
      @JsonKey(name: 'status') final String? status}) = _$ShelfModelImpl;

  factory _ShelfModel.fromJson(Map<String, dynamic> json) =
      _$ShelfModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'shelf_code')
  String? get shelfCode;
  @override
  @JsonKey(name: 'status')
  String? get status;

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShelfModelImplCopyWith<_$ShelfModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StockHistoryModel _$StockHistoryModelFromJson(Map<String, dynamic> json) {
  return _StockHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$StockHistoryModel {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'type')
  String? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'amount')
  int? get amount => throw _privateConstructorUsedError;
  @JsonKey(name: 'reason')
  String? get reason => throw _privateConstructorUsedError;
  @JsonKey(name: 'date')
  String? get date => throw _privateConstructorUsedError;

  /// Serializes this StockHistoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StockHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StockHistoryModelCopyWith<StockHistoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StockHistoryModelCopyWith<$Res> {
  factory $StockHistoryModelCopyWith(
          StockHistoryModel value, $Res Function(StockHistoryModel) then) =
      _$StockHistoryModelCopyWithImpl<$Res, StockHistoryModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'type') String? type,
      @JsonKey(name: 'amount') int? amount,
      @JsonKey(name: 'reason') String? reason,
      @JsonKey(name: 'date') String? date});
}

/// @nodoc
class _$StockHistoryModelCopyWithImpl<$Res, $Val extends StockHistoryModel>
    implements $StockHistoryModelCopyWith<$Res> {
  _$StockHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StockHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? amount = freezed,
    Object? reason = freezed,
    Object? date = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StockHistoryModelImplCopyWith<$Res>
    implements $StockHistoryModelCopyWith<$Res> {
  factory _$$StockHistoryModelImplCopyWith(_$StockHistoryModelImpl value,
          $Res Function(_$StockHistoryModelImpl) then) =
      __$$StockHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'type') String? type,
      @JsonKey(name: 'amount') int? amount,
      @JsonKey(name: 'reason') String? reason,
      @JsonKey(name: 'date') String? date});
}

/// @nodoc
class __$$StockHistoryModelImplCopyWithImpl<$Res>
    extends _$StockHistoryModelCopyWithImpl<$Res, _$StockHistoryModelImpl>
    implements _$$StockHistoryModelImplCopyWith<$Res> {
  __$$StockHistoryModelImplCopyWithImpl(_$StockHistoryModelImpl _value,
      $Res Function(_$StockHistoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StockHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? amount = freezed,
    Object? reason = freezed,
    Object? date = freezed,
  }) {
    return _then(_$StockHistoryModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StockHistoryModelImpl implements _StockHistoryModel {
  const _$StockHistoryModelImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'type') this.type,
      @JsonKey(name: 'amount') this.amount,
      @JsonKey(name: 'reason') this.reason,
      @JsonKey(name: 'date') this.date});

  factory _$StockHistoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StockHistoryModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'type')
  final String? type;
  @override
  @JsonKey(name: 'amount')
  final int? amount;
  @override
  @JsonKey(name: 'reason')
  final String? reason;
  @override
  @JsonKey(name: 'date')
  final String? date;

  @override
  String toString() {
    return 'StockHistoryModel(id: $id, type: $type, amount: $amount, reason: $reason, date: $date)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StockHistoryModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.date, date) || other.date == date));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, type, amount, reason, date);

  /// Create a copy of StockHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StockHistoryModelImplCopyWith<_$StockHistoryModelImpl> get copyWith =>
      __$$StockHistoryModelImplCopyWithImpl<_$StockHistoryModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StockHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _StockHistoryModel implements StockHistoryModel {
  const factory _StockHistoryModel(
      {@JsonKey(name: 'id') final int? id,
      @JsonKey(name: 'type') final String? type,
      @JsonKey(name: 'amount') final int? amount,
      @JsonKey(name: 'reason') final String? reason,
      @JsonKey(name: 'date') final String? date}) = _$StockHistoryModelImpl;

  factory _StockHistoryModel.fromJson(Map<String, dynamic> json) =
      _$StockHistoryModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'type')
  String? get type;
  @override
  @JsonKey(name: 'amount')
  int? get amount;
  @override
  @JsonKey(name: 'reason')
  String? get reason;
  @override
  @JsonKey(name: 'date')
  String? get date;

  /// Create a copy of StockHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StockHistoryModelImplCopyWith<_$StockHistoryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
