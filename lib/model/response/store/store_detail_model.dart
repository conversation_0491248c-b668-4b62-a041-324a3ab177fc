import 'package:freezed_annotation/freezed_annotation.dart';

part 'store_detail_model.freezed.dart';
part 'store_detail_model.g.dart';

@freezed
class StoreDetailModel with _$StoreDetailModel {
  const factory StoreDetailModel({
    @Json<PERSON>ey(name: 'id') int? id,
    @J<PERSON><PERSON><PERSON>(name: 'latitude') String? latitude,
    @Json<PERSON>ey(name: 'longitude') String? longitude,
    @JsonKey(name: 'name') String? name,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'zipcode') String? zipcode,
    @Json<PERSON>ey(name: 'province') String? province,
    @<PERSON>sonKey(name: 'city') String? city,
    @<PERSON>son<PERSON><PERSON>(name: 'street') String? street,
    @Json<PERSON>ey(name: 'building') String? building,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'tel') String? tel,
    @JsonKey(name: 'mobile') String? mobile,
    @<PERSON>son<PERSON>ey(name: 'email') String? email,
    @<PERSON>sonKey(name: 'img') String? img,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'open') String? open,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'close') String? close,
    @Json<PERSON><PERSON>(name: 'cabinets') List<StoreCabinetModel>? cabinets,
    @Json<PERSON>ey(name: 'available_shelves_count') int? availableShelvesCount,
    @JsonKey(name: 'images') List<String>? images,
  }) = _StoreDetailModel;

  factory StoreDetailModel.fromJson(Map<String, dynamic> json) =>
      _$StoreDetailModelFromJson(json);
}

extension StoreDetailModelExtension on StoreDetailModel {
  String get addressStoreFormatGGMap => '+$province,+$city,+$street,+$zipcode';
}

@freezed
class StoreCabinetModel with _$StoreCabinetModel {
  const factory StoreCabinetModel({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'cabinet_code') String? cabinetCode,
    @JsonKey(name: 'description') String? description,
    @JsonKey(name: 'type') String? type,
    @JsonKey(name: 'products') List<StoreProductModel>? products,
    @JsonKey(name: 'shelves') List<StoreShelfModel>? shelves,
  }) = _StoreCabinetModel;

  factory StoreCabinetModel.fromJson(Map<String, dynamic> json) =>
      _$StoreCabinetModelFromJson(json);
}

@freezed
class StoreProductModel with _$StoreProductModel {
  const factory StoreProductModel({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'description') String? description,
    @JsonKey(name: 'comment') String? comment,
    @JsonKey(name: 'note') String? note,
    @JsonKey(name: 'quantity') int? quantity,
    @JsonKey(name: 'expiration_date') String? expirationDate,
    @JsonKey(name: 'tag') List<String>? tag,
    @JsonKey(name: 'capacity') String? capacity,
    @JsonKey(name: 'price') String? price,
    @JsonKey(name: 'sale_price') String? salePrice,
    @JsonKey(name: 'img') String? img,
  }) = _StoreProductModel;

  factory StoreProductModel.fromJson(Map<String, dynamic> json) =>
      _$StoreProductModelFromJson(json);
}

@freezed
class StoreShelfModel with _$StoreShelfModel {
  const factory StoreShelfModel({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'shelf_code') String? shelfCode,
    @JsonKey(name: 'status') String? status,
    @JsonKey(name: 'description') String? description,
    @JsonKey(name: 'height') int? height,
    @JsonKey(name: 'width') int? width,
    @JsonKey(name: 'depth') int? depth,
    @JsonKey(name: 'stage') int? stage,
  }) = _StoreShelfModel;

  factory StoreShelfModel.fromJson(Map<String, dynamic> json) =>
      _$StoreShelfModelFromJson(json);
}

extension StoreShelfModelExtension on StoreShelfModel {
  StoreShelfStatus get statusEnum =>
      StoreShelfStatus.values.byName(status ?? '');
}

enum StoreShelfStatus {
  available,
  booked,
  registered,
}
// 'available', 'registered', 'booked'
