import 'package:json_annotation/json_annotation.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';

part 'nearby_store_model.g.dart';

@JsonSerializable()
class NearbyStoreModel {
  final int? id;
  final String? latitude;
  final String? longitude;
  final String? name;
  final String? zipcode;
  final String? province;
  final String? city;
  final String? street;
  final String? building;
  final String? tel;
  final String? mobile;
  final String? email;
  final String? img;
  final String? open;
  final String? close;
  final List<CabinetModel>? cabinets;
  @JsonKey(name: 'available_shelves_count')
  final int? availableShelvesCount;

  NearbyStoreModel({
    this.id,
    this.latitude,
    this.longitude,
    this.name,
    this.zipcode,
    this.province,
    this.city,
    this.street,
    this.building,
    this.tel,
    this.mobile,
    this.email,
    this.img,
    this.open,
    this.close,
    this.cabinets,
    this.availableShelvesCount,
  });

  factory NearbyStoreModel.fromJson(Map<String, dynamic> json) =>
      _$NearbyStoreModelFromJson(json);
  Map<String, dynamic> toJson() => _$NearbyStoreModelToJson(this);
}

@JsonSerializable()
class CabinetModel {
  final int? id;
  @Json<PERSON>ey(name: 'cabinet_code')
  final String? cabinetCode;
  final String? description;
  final String? type;
  final List<ProductModel>? products;

  CabinetModel({
    this.id,
    this.cabinetCode,
    this.description,
    this.type,
    this.products,
  });

  factory CabinetModel.fromJson(Map<String, dynamic> json) =>
      _$CabinetModelFromJson(json);
  Map<String, dynamic> toJson() => _$CabinetModelToJson(this);
}

extension CabinetModelExtension on NearbyStoreModel {
  List<String> get tags {
    if (cabinets == null || cabinets!.isEmpty) return [];

    // Get all types from cabinets, filter out null values and duplicates
    return cabinets!
        .map((cabinet) => cabinet.type)
        .where((type) => type != null)
        .map((type) => type!)
        .toSet() // Remove duplicates
        .toList();
  }

  List<String> get listImage {
    if (cabinets == null || cabinets!.isEmpty) return [];

    // Get all images from products in all cabinets
    return cabinets!
        .expand((cabinet) =>
            cabinet.products ?? <ProductModel>[]) // Flatten all products
        .map((product) => product.image)
        .where((image) =>
            image != null &&
            image.isNotEmpty) // Filter out null and empty strings
        .map((image) => image!)
        .toList();
  }

  String get addressStoreFormatGGMap {
    return '+$province,+$city,+$street,+$zipcode';
  }
}
