import 'package:json_annotation/json_annotation.dart';

part 'template_model.g.dart';

@JsonSerializable()
class TemplateModel {
    final int? id;
  final String? name;
  final String? description;
  @JsonKey(name: 'base_price')
  final String? basePrice;
  @Json<PERSON>ey(name: 'base_img')
  final String? baseImg;
 

  TemplateModel({
    this.id,
    this.name,
    this.description,
    this.basePrice,
    this.baseImg,
  });

  factory TemplateModel.fromJson(Map<String, dynamic> json) =>
      _$TemplateModelFromJson(json);
  Map<String, dynamic> toJson() => _$TemplateModelToJson(this);
}
 