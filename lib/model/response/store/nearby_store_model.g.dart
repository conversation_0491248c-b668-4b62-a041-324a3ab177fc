// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nearby_store_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NearbyStoreModel _$NearbyStoreModelFromJson(Map<String, dynamic> json) =>
    NearbyStoreModel(
      id: (json['id'] as num?)?.toInt(),
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
      name: json['name'] as String?,
      zipcode: json['zipcode'] as String?,
      province: json['province'] as String?,
      city: json['city'] as String?,
      street: json['street'] as String?,
      building: json['building'] as String?,
      tel: json['tel'] as String?,
      mobile: json['mobile'] as String?,
      email: json['email'] as String?,
      img: json['img'] as String?,
      open: json['open'] as String?,
      close: json['close'] as String?,
      cabinets: (json['cabinets'] as List<dynamic>?)
          ?.map((e) => CabinetModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      availableShelvesCount: (json['available_shelves_count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$NearbyStoreModelToJson(NearbyStoreModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'name': instance.name,
      'zipcode': instance.zipcode,
      'province': instance.province,
      'city': instance.city,
      'street': instance.street,
      'building': instance.building,
      'tel': instance.tel,
      'mobile': instance.mobile,
      'email': instance.email,
      'img': instance.img,
      'open': instance.open,
      'close': instance.close,
      'cabinets': instance.cabinets,
      'available_shelves_count': instance.availableShelvesCount,
    };

CabinetModel _$CabinetModelFromJson(Map<String, dynamic> json) => CabinetModel(
      id: (json['id'] as num?)?.toInt(),
      cabinetCode: json['cabinet_code'] as String?,
      description: json['description'] as String?,
      type: json['type'] as String?,
      products: (json['products'] as List<dynamic>?)
          ?.map((e) => ProductModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CabinetModelToJson(CabinetModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cabinet_code': instance.cabinetCode,
      'description': instance.description,
      'type': instance.type,
      'products': instance.products,
    };
