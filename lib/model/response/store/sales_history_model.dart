import 'package:freezed_annotation/freezed_annotation.dart';

part 'sales_history_model.freezed.dart';
part 'sales_history_model.g.dart';

@freezed
class SalesHistoryModel with _$SalesHistoryModel {
  const factory SalesHistoryModel({
    String? date,
    @Default([]) List<SoldProductModel> products,
  }) = _SalesHistoryModel;

  factory SalesHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$SalesHistoryModelFromJson(json);
}

@freezed
class SoldProductModel with _$SoldProductModel {
  const factory SoldProductModel({
    int? id,
    @JsonKey(name: 'order_id') int? orderId,
    String? name,
    @JsonKey(name: 'sale_price') String? salePrice,
    String? img,
    ShelfModel? shelf,
    CabinetModel? cabinet,
    PlaceModel? place,
    @JsonKey(name: 'sale_quantity') int? saleQuantity,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'total_amount_price') double? totalAmountPrice,
  }) = _SoldProductModel;

  factory SoldProductModel.fromJson(Map<String, dynamic> json) =>
      _$SoldProductModelFromJson(json);
}

@freezed
class ShelfModel with _$ShelfModel {
  const factory ShelfModel({
    int? id,
    @JsonKey(name: 'shelf_code') String? shelfCode,
    String? status,
    int? stage,
    String? description,
    int? height,
    int? width,
    int? depth,
  }) = _ShelfModel;

  factory ShelfModel.fromJson(Map<String, dynamic> json) =>
      _$ShelfModelFromJson(json);
}

@freezed
class CabinetModel with _$CabinetModel {
  const factory CabinetModel({
    int? id,
    @JsonKey(name: 'cabinet_code') String? cabinetCode,
    String? description,
    String? type,
  }) = _CabinetModel;

  factory CabinetModel.fromJson(Map<String, dynamic> json) =>
      _$CabinetModelFromJson(json);
}

@freezed
class PlaceModel with _$PlaceModel {
  const factory PlaceModel({
    int? id,
    String? name,
    @Default([]) List<String> images,
  }) = _PlaceModel;

  factory PlaceModel.fromJson(Map<String, dynamic> json) =>
      _$PlaceModelFromJson(json);
}
