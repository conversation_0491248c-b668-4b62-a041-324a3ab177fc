// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sales_history_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SalesHistoryModel _$SalesHistoryModelFromJson(Map<String, dynamic> json) {
  return _SalesHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$SalesHistoryModel {
  String? get date => throw _privateConstructorUsedError;
  List<SoldProductModel> get products => throw _privateConstructorUsedError;

  /// Serializes this SalesHistoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SalesHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SalesHistoryModelCopyWith<SalesHistoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SalesHistoryModelCopyWith<$Res> {
  factory $SalesHistoryModelCopyWith(
          SalesHistoryModel value, $Res Function(SalesHistoryModel) then) =
      _$SalesHistoryModelCopyWithImpl<$Res, SalesHistoryModel>;
  @useResult
  $Res call({String? date, List<SoldProductModel> products});
}

/// @nodoc
class _$SalesHistoryModelCopyWithImpl<$Res, $Val extends SalesHistoryModel>
    implements $SalesHistoryModelCopyWith<$Res> {
  _$SalesHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SalesHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? products = null,
  }) {
    return _then(_value.copyWith(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
      products: null == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<SoldProductModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SalesHistoryModelImplCopyWith<$Res>
    implements $SalesHistoryModelCopyWith<$Res> {
  factory _$$SalesHistoryModelImplCopyWith(_$SalesHistoryModelImpl value,
          $Res Function(_$SalesHistoryModelImpl) then) =
      __$$SalesHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? date, List<SoldProductModel> products});
}

/// @nodoc
class __$$SalesHistoryModelImplCopyWithImpl<$Res>
    extends _$SalesHistoryModelCopyWithImpl<$Res, _$SalesHistoryModelImpl>
    implements _$$SalesHistoryModelImplCopyWith<$Res> {
  __$$SalesHistoryModelImplCopyWithImpl(_$SalesHistoryModelImpl _value,
      $Res Function(_$SalesHistoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SalesHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? products = null,
  }) {
    return _then(_$SalesHistoryModelImpl(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
      products: null == products
          ? _value._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<SoldProductModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SalesHistoryModelImpl implements _SalesHistoryModel {
  const _$SalesHistoryModelImpl(
      {this.date, final List<SoldProductModel> products = const []})
      : _products = products;

  factory _$SalesHistoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SalesHistoryModelImplFromJson(json);

  @override
  final String? date;
  final List<SoldProductModel> _products;
  @override
  @JsonKey()
  List<SoldProductModel> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  @override
  String toString() {
    return 'SalesHistoryModel(date: $date, products: $products)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SalesHistoryModelImpl &&
            (identical(other.date, date) || other.date == date) &&
            const DeepCollectionEquality().equals(other._products, _products));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, date, const DeepCollectionEquality().hash(_products));

  /// Create a copy of SalesHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SalesHistoryModelImplCopyWith<_$SalesHistoryModelImpl> get copyWith =>
      __$$SalesHistoryModelImplCopyWithImpl<_$SalesHistoryModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SalesHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _SalesHistoryModel implements SalesHistoryModel {
  const factory _SalesHistoryModel(
      {final String? date,
      final List<SoldProductModel> products}) = _$SalesHistoryModelImpl;

  factory _SalesHistoryModel.fromJson(Map<String, dynamic> json) =
      _$SalesHistoryModelImpl.fromJson;

  @override
  String? get date;
  @override
  List<SoldProductModel> get products;

  /// Create a copy of SalesHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SalesHistoryModelImplCopyWith<_$SalesHistoryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SoldProductModel _$SoldProductModelFromJson(Map<String, dynamic> json) {
  return _SoldProductModel.fromJson(json);
}

/// @nodoc
mixin _$SoldProductModel {
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_id')
  int? get orderId => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'sale_price')
  String? get salePrice => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  ShelfModel? get shelf => throw _privateConstructorUsedError;
  CabinetModel? get cabinet => throw _privateConstructorUsedError;
  PlaceModel? get place => throw _privateConstructorUsedError;
  @JsonKey(name: 'sale_quantity')
  int? get saleQuantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_amount_price')
  double? get totalAmountPrice => throw _privateConstructorUsedError;

  /// Serializes this SoldProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SoldProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SoldProductModelCopyWith<SoldProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SoldProductModelCopyWith<$Res> {
  factory $SoldProductModelCopyWith(
          SoldProductModel value, $Res Function(SoldProductModel) then) =
      _$SoldProductModelCopyWithImpl<$Res, SoldProductModel>;
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'order_id') int? orderId,
      String? name,
      @JsonKey(name: 'sale_price') String? salePrice,
      String? img,
      ShelfModel? shelf,
      CabinetModel? cabinet,
      PlaceModel? place,
      @JsonKey(name: 'sale_quantity') int? saleQuantity,
      @JsonKey(name: 'total_amount_price') double? totalAmountPrice});

  $ShelfModelCopyWith<$Res>? get shelf;
  $CabinetModelCopyWith<$Res>? get cabinet;
  $PlaceModelCopyWith<$Res>? get place;
}

/// @nodoc
class _$SoldProductModelCopyWithImpl<$Res, $Val extends SoldProductModel>
    implements $SoldProductModelCopyWith<$Res> {
  _$SoldProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SoldProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? orderId = freezed,
    Object? name = freezed,
    Object? salePrice = freezed,
    Object? img = freezed,
    Object? shelf = freezed,
    Object? cabinet = freezed,
    Object? place = freezed,
    Object? saleQuantity = freezed,
    Object? totalAmountPrice = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      salePrice: freezed == salePrice
          ? _value.salePrice
          : salePrice // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      shelf: freezed == shelf
          ? _value.shelf
          : shelf // ignore: cast_nullable_to_non_nullable
              as ShelfModel?,
      cabinet: freezed == cabinet
          ? _value.cabinet
          : cabinet // ignore: cast_nullable_to_non_nullable
              as CabinetModel?,
      place: freezed == place
          ? _value.place
          : place // ignore: cast_nullable_to_non_nullable
              as PlaceModel?,
      saleQuantity: freezed == saleQuantity
          ? _value.saleQuantity
          : saleQuantity // ignore: cast_nullable_to_non_nullable
              as int?,
      totalAmountPrice: freezed == totalAmountPrice
          ? _value.totalAmountPrice
          : totalAmountPrice // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }

  /// Create a copy of SoldProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShelfModelCopyWith<$Res>? get shelf {
    if (_value.shelf == null) {
      return null;
    }

    return $ShelfModelCopyWith<$Res>(_value.shelf!, (value) {
      return _then(_value.copyWith(shelf: value) as $Val);
    });
  }

  /// Create a copy of SoldProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CabinetModelCopyWith<$Res>? get cabinet {
    if (_value.cabinet == null) {
      return null;
    }

    return $CabinetModelCopyWith<$Res>(_value.cabinet!, (value) {
      return _then(_value.copyWith(cabinet: value) as $Val);
    });
  }

  /// Create a copy of SoldProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PlaceModelCopyWith<$Res>? get place {
    if (_value.place == null) {
      return null;
    }

    return $PlaceModelCopyWith<$Res>(_value.place!, (value) {
      return _then(_value.copyWith(place: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SoldProductModelImplCopyWith<$Res>
    implements $SoldProductModelCopyWith<$Res> {
  factory _$$SoldProductModelImplCopyWith(_$SoldProductModelImpl value,
          $Res Function(_$SoldProductModelImpl) then) =
      __$$SoldProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'order_id') int? orderId,
      String? name,
      @JsonKey(name: 'sale_price') String? salePrice,
      String? img,
      ShelfModel? shelf,
      CabinetModel? cabinet,
      PlaceModel? place,
      @JsonKey(name: 'sale_quantity') int? saleQuantity,
      @JsonKey(name: 'total_amount_price') double? totalAmountPrice});

  @override
  $ShelfModelCopyWith<$Res>? get shelf;
  @override
  $CabinetModelCopyWith<$Res>? get cabinet;
  @override
  $PlaceModelCopyWith<$Res>? get place;
}

/// @nodoc
class __$$SoldProductModelImplCopyWithImpl<$Res>
    extends _$SoldProductModelCopyWithImpl<$Res, _$SoldProductModelImpl>
    implements _$$SoldProductModelImplCopyWith<$Res> {
  __$$SoldProductModelImplCopyWithImpl(_$SoldProductModelImpl _value,
      $Res Function(_$SoldProductModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SoldProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? orderId = freezed,
    Object? name = freezed,
    Object? salePrice = freezed,
    Object? img = freezed,
    Object? shelf = freezed,
    Object? cabinet = freezed,
    Object? place = freezed,
    Object? saleQuantity = freezed,
    Object? totalAmountPrice = freezed,
  }) {
    return _then(_$SoldProductModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      salePrice: freezed == salePrice
          ? _value.salePrice
          : salePrice // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      shelf: freezed == shelf
          ? _value.shelf
          : shelf // ignore: cast_nullable_to_non_nullable
              as ShelfModel?,
      cabinet: freezed == cabinet
          ? _value.cabinet
          : cabinet // ignore: cast_nullable_to_non_nullable
              as CabinetModel?,
      place: freezed == place
          ? _value.place
          : place // ignore: cast_nullable_to_non_nullable
              as PlaceModel?,
      saleQuantity: freezed == saleQuantity
          ? _value.saleQuantity
          : saleQuantity // ignore: cast_nullable_to_non_nullable
              as int?,
      totalAmountPrice: freezed == totalAmountPrice
          ? _value.totalAmountPrice
          : totalAmountPrice // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SoldProductModelImpl implements _SoldProductModel {
  const _$SoldProductModelImpl(
      {this.id,
      @JsonKey(name: 'order_id') this.orderId,
      this.name,
      @JsonKey(name: 'sale_price') this.salePrice,
      this.img,
      this.shelf,
      this.cabinet,
      this.place,
      @JsonKey(name: 'sale_quantity') this.saleQuantity,
      @JsonKey(name: 'total_amount_price') this.totalAmountPrice});

  factory _$SoldProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SoldProductModelImplFromJson(json);

  @override
  final int? id;
  @override
  @JsonKey(name: 'order_id')
  final int? orderId;
  @override
  final String? name;
  @override
  @JsonKey(name: 'sale_price')
  final String? salePrice;
  @override
  final String? img;
  @override
  final ShelfModel? shelf;
  @override
  final CabinetModel? cabinet;
  @override
  final PlaceModel? place;
  @override
  @JsonKey(name: 'sale_quantity')
  final int? saleQuantity;
  @override
  @JsonKey(name: 'total_amount_price')
  final double? totalAmountPrice;

  @override
  String toString() {
    return 'SoldProductModel(id: $id, orderId: $orderId, name: $name, salePrice: $salePrice, img: $img, shelf: $shelf, cabinet: $cabinet, place: $place, saleQuantity: $saleQuantity, totalAmountPrice: $totalAmountPrice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SoldProductModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.salePrice, salePrice) ||
                other.salePrice == salePrice) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.shelf, shelf) || other.shelf == shelf) &&
            (identical(other.cabinet, cabinet) || other.cabinet == cabinet) &&
            (identical(other.place, place) || other.place == place) &&
            (identical(other.saleQuantity, saleQuantity) ||
                other.saleQuantity == saleQuantity) &&
            (identical(other.totalAmountPrice, totalAmountPrice) ||
                other.totalAmountPrice == totalAmountPrice));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, orderId, name, salePrice,
      img, shelf, cabinet, place, saleQuantity, totalAmountPrice);

  /// Create a copy of SoldProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SoldProductModelImplCopyWith<_$SoldProductModelImpl> get copyWith =>
      __$$SoldProductModelImplCopyWithImpl<_$SoldProductModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SoldProductModelImplToJson(
      this,
    );
  }
}

abstract class _SoldProductModel implements SoldProductModel {
  const factory _SoldProductModel(
      {final int? id,
      @JsonKey(name: 'order_id') final int? orderId,
      final String? name,
      @JsonKey(name: 'sale_price') final String? salePrice,
      final String? img,
      final ShelfModel? shelf,
      final CabinetModel? cabinet,
      final PlaceModel? place,
      @JsonKey(name: 'sale_quantity') final int? saleQuantity,
      @JsonKey(name: 'total_amount_price')
      final double? totalAmountPrice}) = _$SoldProductModelImpl;

  factory _SoldProductModel.fromJson(Map<String, dynamic> json) =
      _$SoldProductModelImpl.fromJson;

  @override
  int? get id;
  @override
  @JsonKey(name: 'order_id')
  int? get orderId;
  @override
  String? get name;
  @override
  @JsonKey(name: 'sale_price')
  String? get salePrice;
  @override
  String? get img;
  @override
  ShelfModel? get shelf;
  @override
  CabinetModel? get cabinet;
  @override
  PlaceModel? get place;
  @override
  @JsonKey(name: 'sale_quantity')
  int? get saleQuantity;
  @override
  @JsonKey(name: 'total_amount_price')
  double? get totalAmountPrice;

  /// Create a copy of SoldProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SoldProductModelImplCopyWith<_$SoldProductModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ShelfModel _$ShelfModelFromJson(Map<String, dynamic> json) {
  return _ShelfModel.fromJson(json);
}

/// @nodoc
mixin _$ShelfModel {
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'shelf_code')
  String? get shelfCode => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  int? get stage => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int? get height => throw _privateConstructorUsedError;
  int? get width => throw _privateConstructorUsedError;
  int? get depth => throw _privateConstructorUsedError;

  /// Serializes this ShelfModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShelfModelCopyWith<ShelfModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShelfModelCopyWith<$Res> {
  factory $ShelfModelCopyWith(
          ShelfModel value, $Res Function(ShelfModel) then) =
      _$ShelfModelCopyWithImpl<$Res, ShelfModel>;
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'shelf_code') String? shelfCode,
      String? status,
      int? stage,
      String? description,
      int? height,
      int? width,
      int? depth});
}

/// @nodoc
class _$ShelfModelCopyWithImpl<$Res, $Val extends ShelfModel>
    implements $ShelfModelCopyWith<$Res> {
  _$ShelfModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? shelfCode = freezed,
    Object? status = freezed,
    Object? stage = freezed,
    Object? description = freezed,
    Object? height = freezed,
    Object? width = freezed,
    Object? depth = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      shelfCode: freezed == shelfCode
          ? _value.shelfCode
          : shelfCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      stage: freezed == stage
          ? _value.stage
          : stage // ignore: cast_nullable_to_non_nullable
              as int?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      depth: freezed == depth
          ? _value.depth
          : depth // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShelfModelImplCopyWith<$Res>
    implements $ShelfModelCopyWith<$Res> {
  factory _$$ShelfModelImplCopyWith(
          _$ShelfModelImpl value, $Res Function(_$ShelfModelImpl) then) =
      __$$ShelfModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'shelf_code') String? shelfCode,
      String? status,
      int? stage,
      String? description,
      int? height,
      int? width,
      int? depth});
}

/// @nodoc
class __$$ShelfModelImplCopyWithImpl<$Res>
    extends _$ShelfModelCopyWithImpl<$Res, _$ShelfModelImpl>
    implements _$$ShelfModelImplCopyWith<$Res> {
  __$$ShelfModelImplCopyWithImpl(
      _$ShelfModelImpl _value, $Res Function(_$ShelfModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? shelfCode = freezed,
    Object? status = freezed,
    Object? stage = freezed,
    Object? description = freezed,
    Object? height = freezed,
    Object? width = freezed,
    Object? depth = freezed,
  }) {
    return _then(_$ShelfModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      shelfCode: freezed == shelfCode
          ? _value.shelfCode
          : shelfCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      stage: freezed == stage
          ? _value.stage
          : stage // ignore: cast_nullable_to_non_nullable
              as int?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      depth: freezed == depth
          ? _value.depth
          : depth // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShelfModelImpl implements _ShelfModel {
  const _$ShelfModelImpl(
      {this.id,
      @JsonKey(name: 'shelf_code') this.shelfCode,
      this.status,
      this.stage,
      this.description,
      this.height,
      this.width,
      this.depth});

  factory _$ShelfModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShelfModelImplFromJson(json);

  @override
  final int? id;
  @override
  @JsonKey(name: 'shelf_code')
  final String? shelfCode;
  @override
  final String? status;
  @override
  final int? stage;
  @override
  final String? description;
  @override
  final int? height;
  @override
  final int? width;
  @override
  final int? depth;

  @override
  String toString() {
    return 'ShelfModel(id: $id, shelfCode: $shelfCode, status: $status, stage: $stage, description: $description, height: $height, width: $width, depth: $depth)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShelfModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.shelfCode, shelfCode) ||
                other.shelfCode == shelfCode) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.stage, stage) || other.stage == stage) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.depth, depth) || other.depth == depth));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, shelfCode, status, stage,
      description, height, width, depth);

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShelfModelImplCopyWith<_$ShelfModelImpl> get copyWith =>
      __$$ShelfModelImplCopyWithImpl<_$ShelfModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShelfModelImplToJson(
      this,
    );
  }
}

abstract class _ShelfModel implements ShelfModel {
  const factory _ShelfModel(
      {final int? id,
      @JsonKey(name: 'shelf_code') final String? shelfCode,
      final String? status,
      final int? stage,
      final String? description,
      final int? height,
      final int? width,
      final int? depth}) = _$ShelfModelImpl;

  factory _ShelfModel.fromJson(Map<String, dynamic> json) =
      _$ShelfModelImpl.fromJson;

  @override
  int? get id;
  @override
  @JsonKey(name: 'shelf_code')
  String? get shelfCode;
  @override
  String? get status;
  @override
  int? get stage;
  @override
  String? get description;
  @override
  int? get height;
  @override
  int? get width;
  @override
  int? get depth;

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShelfModelImplCopyWith<_$ShelfModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CabinetModel _$CabinetModelFromJson(Map<String, dynamic> json) {
  return _CabinetModel.fromJson(json);
}

/// @nodoc
mixin _$CabinetModel {
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'cabinet_code')
  String? get cabinetCode => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;

  /// Serializes this CabinetModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CabinetModelCopyWith<CabinetModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CabinetModelCopyWith<$Res> {
  factory $CabinetModelCopyWith(
          CabinetModel value, $Res Function(CabinetModel) then) =
      _$CabinetModelCopyWithImpl<$Res, CabinetModel>;
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'cabinet_code') String? cabinetCode,
      String? description,
      String? type});
}

/// @nodoc
class _$CabinetModelCopyWithImpl<$Res, $Val extends CabinetModel>
    implements $CabinetModelCopyWith<$Res> {
  _$CabinetModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cabinetCode = freezed,
    Object? description = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CabinetModelImplCopyWith<$Res>
    implements $CabinetModelCopyWith<$Res> {
  factory _$$CabinetModelImplCopyWith(
          _$CabinetModelImpl value, $Res Function(_$CabinetModelImpl) then) =
      __$$CabinetModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'cabinet_code') String? cabinetCode,
      String? description,
      String? type});
}

/// @nodoc
class __$$CabinetModelImplCopyWithImpl<$Res>
    extends _$CabinetModelCopyWithImpl<$Res, _$CabinetModelImpl>
    implements _$$CabinetModelImplCopyWith<$Res> {
  __$$CabinetModelImplCopyWithImpl(
      _$CabinetModelImpl _value, $Res Function(_$CabinetModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cabinetCode = freezed,
    Object? description = freezed,
    Object? type = freezed,
  }) {
    return _then(_$CabinetModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CabinetModelImpl implements _CabinetModel {
  const _$CabinetModelImpl(
      {this.id,
      @JsonKey(name: 'cabinet_code') this.cabinetCode,
      this.description,
      this.type});

  factory _$CabinetModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CabinetModelImplFromJson(json);

  @override
  final int? id;
  @override
  @JsonKey(name: 'cabinet_code')
  final String? cabinetCode;
  @override
  final String? description;
  @override
  final String? type;

  @override
  String toString() {
    return 'CabinetModel(id: $id, cabinetCode: $cabinetCode, description: $description, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CabinetModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cabinetCode, cabinetCode) ||
                other.cabinetCode == cabinetCode) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, cabinetCode, description, type);

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CabinetModelImplCopyWith<_$CabinetModelImpl> get copyWith =>
      __$$CabinetModelImplCopyWithImpl<_$CabinetModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CabinetModelImplToJson(
      this,
    );
  }
}

abstract class _CabinetModel implements CabinetModel {
  const factory _CabinetModel(
      {final int? id,
      @JsonKey(name: 'cabinet_code') final String? cabinetCode,
      final String? description,
      final String? type}) = _$CabinetModelImpl;

  factory _CabinetModel.fromJson(Map<String, dynamic> json) =
      _$CabinetModelImpl.fromJson;

  @override
  int? get id;
  @override
  @JsonKey(name: 'cabinet_code')
  String? get cabinetCode;
  @override
  String? get description;
  @override
  String? get type;

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CabinetModelImplCopyWith<_$CabinetModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlaceModel _$PlaceModelFromJson(Map<String, dynamic> json) {
  return _PlaceModel.fromJson(json);
}

/// @nodoc
mixin _$PlaceModel {
  int? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  List<String> get images => throw _privateConstructorUsedError;

  /// Serializes this PlaceModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlaceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlaceModelCopyWith<PlaceModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlaceModelCopyWith<$Res> {
  factory $PlaceModelCopyWith(
          PlaceModel value, $Res Function(PlaceModel) then) =
      _$PlaceModelCopyWithImpl<$Res, PlaceModel>;
  @useResult
  $Res call({int? id, String? name, List<String> images});
}

/// @nodoc
class _$PlaceModelCopyWithImpl<$Res, $Val extends PlaceModel>
    implements $PlaceModelCopyWith<$Res> {
  _$PlaceModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlaceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? images = null,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      images: null == images
          ? _value.images
          : images // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PlaceModelImplCopyWith<$Res>
    implements $PlaceModelCopyWith<$Res> {
  factory _$$PlaceModelImplCopyWith(
          _$PlaceModelImpl value, $Res Function(_$PlaceModelImpl) then) =
      __$$PlaceModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? id, String? name, List<String> images});
}

/// @nodoc
class __$$PlaceModelImplCopyWithImpl<$Res>
    extends _$PlaceModelCopyWithImpl<$Res, _$PlaceModelImpl>
    implements _$$PlaceModelImplCopyWith<$Res> {
  __$$PlaceModelImplCopyWithImpl(
      _$PlaceModelImpl _value, $Res Function(_$PlaceModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of PlaceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? images = null,
  }) {
    return _then(_$PlaceModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      images: null == images
          ? _value._images
          : images // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PlaceModelImpl implements _PlaceModel {
  const _$PlaceModelImpl(
      {this.id, this.name, final List<String> images = const []})
      : _images = images;

  factory _$PlaceModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlaceModelImplFromJson(json);

  @override
  final int? id;
  @override
  final String? name;
  final List<String> _images;
  @override
  @JsonKey()
  List<String> get images {
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_images);
  }

  @override
  String toString() {
    return 'PlaceModel(id: $id, name: $name, images: $images)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaceModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._images, _images));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, const DeepCollectionEquality().hash(_images));

  /// Create a copy of PlaceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlaceModelImplCopyWith<_$PlaceModelImpl> get copyWith =>
      __$$PlaceModelImplCopyWithImpl<_$PlaceModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlaceModelImplToJson(
      this,
    );
  }
}

abstract class _PlaceModel implements PlaceModel {
  const factory _PlaceModel(
      {final int? id,
      final String? name,
      final List<String> images}) = _$PlaceModelImpl;

  factory _PlaceModel.fromJson(Map<String, dynamic> json) =
      _$PlaceModelImpl.fromJson;

  @override
  int? get id;
  @override
  String? get name;
  @override
  List<String> get images;

  /// Create a copy of PlaceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlaceModelImplCopyWith<_$PlaceModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
