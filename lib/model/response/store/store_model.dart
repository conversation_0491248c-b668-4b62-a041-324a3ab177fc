import 'package:json_annotation/json_annotation.dart';

part 'store_model.g.dart';

@JsonSerializable()
class StoreModel {
    final int? id;
  final String? name;
  final String? address;
  final String? tel;
  final String? email;
  final String? img;
  final String? responsible;
  @J<PERSON><PERSON>ey(name: 'user_id')
  final int? userId;

  StoreModel({
    this.id,
    this.name,
    this.address,
    this.tel,
    this.email,
    this.img,
    this.responsible,
    this.userId,
  });

  factory StoreModel.fromJson(Map<String, dynamic> json) =>
      _$StoreModelFromJson(json);
  Map<String, dynamic> toJson() => _$StoreModelToJson(this);
}
