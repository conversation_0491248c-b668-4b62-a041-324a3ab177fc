import 'package:json_annotation/json_annotation.dart';

part 'province_model.g.dart';

@JsonSerializable()
class ProvinceModel {
  final String name;

  ProvinceModel({required this.name});

  factory ProvinceModel.fromJson(Map<String, dynamic> json) =>
      _$ProvinceModelFromJson(json);
  Map<String, dynamic> toJson() => _$ProvinceModelToJson(this);
}

@JsonSerializable()
class ProvinceListResponse {
  @JsonKey(name: 'provinces')
  final List<ProvinceModel> provinces;

  ProvinceListResponse({required this.provinces});

  factory ProvinceListResponse.fromJson(Map<String, dynamic> json) =>
      _$ProvinceListResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ProvinceListResponseToJson(this);
}
