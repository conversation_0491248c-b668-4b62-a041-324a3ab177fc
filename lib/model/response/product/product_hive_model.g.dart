// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_hive_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ShelfHiveModelAdapter extends TypeAdapter<ShelfHiveModel> {
  @override
  final typeId = 0;

  @override
  ShelfHiveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ShelfHiveModel(
      id: (fields[0] as num?)?.toInt(),
      shelfCode: fields[1] as String?,
      status: fields[2] as String?,
      description: fields[3] as String?,
      height: (fields[4] as num?)?.toInt(),
      width: (fields[5] as num?)?.toInt(),
      depth: (fields[6] as num?)?.toInt(),
    );
  }

  @override
  void write(BinaryWriter writer, ShelfHiveModel obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.shelfCode)
      ..writeByte(2)
      ..write(obj.status)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.height)
      ..writeByte(5)
      ..write(obj.width)
      ..writeByte(6)
      ..write(obj.depth);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShelfHiveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CabinetHiveModelAdapter extends TypeAdapter<CabinetHiveModel> {
  @override
  final typeId = 1;

  @override
  CabinetHiveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CabinetHiveModel(
      id: (fields[0] as num?)?.toInt(),
      cabinetCode: fields[1] as String?,
      description: fields[2] as String?,
      type: fields[3] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, CabinetHiveModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.cabinetCode)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.type);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CabinetHiveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductImageHiveModelAdapter extends TypeAdapter<ProductImageHiveModel> {
  @override
  final typeId = 2;

  @override
  ProductImageHiveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductImageHiveModel(
      id: (fields[0] as num?)?.toInt(),
      path: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ProductImageHiveModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.path);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductImageHiveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductHiveModelAdapter extends TypeAdapter<ProductHiveModel> {
  @override
  final typeId = 3;

  @override
  ProductHiveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductHiveModel(
      id: (fields[0] as num?)?.toInt(),
      name: fields[1] as String?,
      description: fields[2] as String?,
      comment: fields[3] as String?,
      note: fields[4] as String?,
      quantity: (fields[5] as num?)?.toInt(),
      expirationDate: fields[6] as String?,
      tag: (fields[7] as List?)?.cast<String>(),
      capacity: fields[8] as String?,
      price: fields[9] as String?,
      salePrice: fields[10] as String?,
      image: fields[11] as String?,
      shelf: fields[12] as ShelfHiveModel?,
      cabinet: fields[13] as CabinetHiveModel?,
      images: (fields[14] as List?)?.cast<ProductImageHiveModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, ProductHiveModel obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.comment)
      ..writeByte(4)
      ..write(obj.note)
      ..writeByte(5)
      ..write(obj.quantity)
      ..writeByte(6)
      ..write(obj.expirationDate)
      ..writeByte(7)
      ..write(obj.tag)
      ..writeByte(8)
      ..write(obj.capacity)
      ..writeByte(9)
      ..write(obj.price)
      ..writeByte(10)
      ..write(obj.salePrice)
      ..writeByte(11)
      ..write(obj.image)
      ..writeByte(12)
      ..write(obj.shelf)
      ..writeByte(13)
      ..write(obj.cabinet)
      ..writeByte(14)
      ..write(obj.images);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductHiveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
