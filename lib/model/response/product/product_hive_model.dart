import 'package:hive_ce/hive.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';

part 'product_hive_model.g.dart';

@HiveType(typeId: 0)
class ShelfHiveModel {
  @HiveField(0)
  final int? id;
  @HiveField(1)
  final String? shelfCode;
  @HiveField(2)
  final String? status;
  @HiveField(3)
  final String? description;
  @HiveField(4)
  final int? height;
  @HiveField(5)
  final int? width;
  @HiveField(6)
  final int? depth;

  ShelfHiveModel({
    this.id,
    this.shelfCode,
    this.status,
    this.description,
    this.height,
    this.width,
    this.depth,
  });

  factory ShelfHiveModel.fromShelfModel(ShelfModel model) {
    return ShelfHiveModel(
      id: model.id,
      shelfCode: model.shelfCode,
      status: model.status,
      description: model.description,
      height: model.height,
      width: model.width,
      depth: model.depth,
    );
  }
}

@HiveType(typeId: 1)
class CabinetHiveModel {
  @HiveField(0)
  final int? id;
  @HiveField(1)
  final String? cabinetCode;
  @HiveField(2)
  final String? description;
  @HiveField(3)
  final String? type;

  CabinetHiveModel({
    this.id,
    this.cabinetCode,
    this.description,
    this.type,
  });

  factory CabinetHiveModel.fromCabinetModel(CabinetModel model) {
    return CabinetHiveModel(
      id: model.id,
      cabinetCode: model.cabinetCode,
      description: model.description,
      type: model.type,
    );
  }
}

@HiveType(typeId: 2)
class ProductImageHiveModel {
  @HiveField(0)
  final int? id;
  @HiveField(1)
  final String? path;

  ProductImageHiveModel({
    this.id,
    this.path,
  });

  factory ProductImageHiveModel.fromProductImageModel(ProductImageModel model) {
    return ProductImageHiveModel(
      id: model.id,
      path: model.path,
    );
  }
}

@HiveType(typeId: 3)
class ProductHiveModel {
  @HiveField(0)
  final int? id;
  @HiveField(1)
  final String? name;
  @HiveField(2)
  final String? description;
  @HiveField(3)
  final String? comment;
  @HiveField(4)
  final String? note;
  @HiveField(5)
  final int? quantity;
  @HiveField(6)
  final String? expirationDate;
  @HiveField(7)
  final List<String>? tag;
  @HiveField(8)
  final String? capacity;
  @HiveField(9)
  final String? price;
  @HiveField(10)
  final String? salePrice;
  @HiveField(11)
  final String? image;
  @HiveField(12)
  final ShelfHiveModel? shelf;
  @HiveField(13)
  final CabinetHiveModel? cabinet;
  @HiveField(14)
  final List<ProductImageHiveModel>? images;

  ProductHiveModel({
    this.id,
    this.name,
    this.description,
    this.comment,
    this.note,
    this.quantity,
    this.expirationDate,
    this.tag,
    this.capacity,
    this.price,
    this.salePrice,
    this.image,
    this.shelf,
    this.cabinet,
    this.images,
  });

  factory ProductHiveModel.fromProductModel(ProductModel model) {
    return ProductHiveModel(
      id: model.id,
      name: model.name,
      description: model.description,
      comment: model.comment,
      note: model.note,
      quantity: model.quantity,
      expirationDate: model.expirationDate,
      tag: model.tag,
      capacity: model.capacity,
      price: model.price,
      salePrice: model.salePrice,
      image: model.image,
      shelf: model.shelf != null
          ? ShelfHiveModel.fromShelfModel(model.shelf!)
          : null,
      cabinet: model.cabinet != null
          ? CabinetHiveModel.fromCabinetModel(model.cabinet!)
          : null,
      images: model.images
          ?.map((e) => ProductImageHiveModel.fromProductImageModel(e))
          .toList(),
    );
  }
}
