import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';
 
part 'check_cart_model.freezed.dart';
part 'check_cart_model.g.dart';

@freezed
class CheckCartModel with _$CheckCartModel {
  const factory CheckCartModel({
    int? id,
    @JsonKey(name: 'lock') LockModel? lock,
 
  }) = _CheckCartModel;

  factory CheckCartModel.fromJson(Map<String, dynamic> json) =>
      _$CheckCartModelFromJson(json);
}