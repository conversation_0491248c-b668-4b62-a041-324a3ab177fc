// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'check_cart_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CheckCartModel _$CheckCartModelFromJson(Map<String, dynamic> json) {
  return _CheckCartModel.fromJson(json);
}

/// @nodoc
mixin _$CheckCartModel {
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'lock')
  LockModel? get lock => throw _privateConstructorUsedError;

  /// Serializes this CheckCartModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CheckCartModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckCartModelCopyWith<CheckCartModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckCartModelCopyWith<$Res> {
  factory $CheckCartModelCopyWith(
          CheckCartModel value, $Res Function(CheckCartModel) then) =
      _$CheckCartModelCopyWithImpl<$Res, CheckCartModel>;
  @useResult
  $Res call({int? id, @JsonKey(name: 'lock') LockModel? lock});

  $LockModelCopyWith<$Res>? get lock;
}

/// @nodoc
class _$CheckCartModelCopyWithImpl<$Res, $Val extends CheckCartModel>
    implements $CheckCartModelCopyWith<$Res> {
  _$CheckCartModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckCartModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? lock = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as LockModel?,
    ) as $Val);
  }

  /// Create a copy of CheckCartModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LockModelCopyWith<$Res>? get lock {
    if (_value.lock == null) {
      return null;
    }

    return $LockModelCopyWith<$Res>(_value.lock!, (value) {
      return _then(_value.copyWith(lock: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CheckCartModelImplCopyWith<$Res>
    implements $CheckCartModelCopyWith<$Res> {
  factory _$$CheckCartModelImplCopyWith(_$CheckCartModelImpl value,
          $Res Function(_$CheckCartModelImpl) then) =
      __$$CheckCartModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? id, @JsonKey(name: 'lock') LockModel? lock});

  @override
  $LockModelCopyWith<$Res>? get lock;
}

/// @nodoc
class __$$CheckCartModelImplCopyWithImpl<$Res>
    extends _$CheckCartModelCopyWithImpl<$Res, _$CheckCartModelImpl>
    implements _$$CheckCartModelImplCopyWith<$Res> {
  __$$CheckCartModelImplCopyWithImpl(
      _$CheckCartModelImpl _value, $Res Function(_$CheckCartModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckCartModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? lock = freezed,
  }) {
    return _then(_$CheckCartModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as LockModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckCartModelImpl implements _CheckCartModel {
  const _$CheckCartModelImpl({this.id, @JsonKey(name: 'lock') this.lock});

  factory _$CheckCartModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckCartModelImplFromJson(json);

  @override
  final int? id;
  @override
  @JsonKey(name: 'lock')
  final LockModel? lock;

  @override
  String toString() {
    return 'CheckCartModel(id: $id, lock: $lock)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckCartModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.lock, lock) || other.lock == lock));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, lock);

  /// Create a copy of CheckCartModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckCartModelImplCopyWith<_$CheckCartModelImpl> get copyWith =>
      __$$CheckCartModelImplCopyWithImpl<_$CheckCartModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckCartModelImplToJson(
      this,
    );
  }
}

abstract class _CheckCartModel implements CheckCartModel {
  const factory _CheckCartModel(
      {final int? id,
      @JsonKey(name: 'lock') final LockModel? lock}) = _$CheckCartModelImpl;

  factory _CheckCartModel.fromJson(Map<String, dynamic> json) =
      _$CheckCartModelImpl.fromJson;

  @override
  int? get id;
  @override
  @JsonKey(name: 'lock')
  LockModel? get lock;

  /// Create a copy of CheckCartModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckCartModelImplCopyWith<_$CheckCartModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
