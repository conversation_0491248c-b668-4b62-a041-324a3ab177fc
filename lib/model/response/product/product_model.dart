import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/store/store_detail_model.dart';

part 'product_model.freezed.dart';
part 'product_model.g.dart';

@freezed
class ShelfModel with _$ShelfModel {
  const factory ShelfModel({
    int? id,
    @JsonKey(name: 'shelf_code') String? shelfCode,
    String? status,
    String? description,
    int? height,
    int? width,
    int? depth,
  }) = _ShelfModel;

  factory ShelfModel.fromJson(Map<String, dynamic> json) =>
      _$ShelfModelFromJson(json);
}

@freezed
class CabinetModel with _$CabinetModel {
  const factory CabinetModel({
    int? id,
    @JsonKey(name: 'cabinet_code') String? cabinetCode,
    String? description,
    String? type,
  }) = _CabinetModel;

  factory CabinetModel.fromJson(Map<String, dynamic> json) =>
      _$CabinetModelFromJson(json);
}

@freezed
class ProductImageModel with _$ProductImageModel {
  const factory ProductImageModel({
    int? id,
    String? path,
  }) = _ProductImageModel;

  factory ProductImageModel.fromJson(Map<String, dynamic> json) =>
      _$ProductImageModelFromJson(json);
}

@freezed
class ProductModel with _$ProductModel {
  const factory ProductModel({
    int? id,
    String? name,
    String? description,
    String? comment,
    String? note,
    int? quantity,
    @JsonKey(name: 'expiration_date') String? expirationDate,
    List<String>? tag,
    String? capacity,
    String? price,
    @JsonKey(name: 'sale_price') String? salePrice,
    @JsonKey(name: 'img') String? image,
    ShelfModel? shelf,
    CabinetModel? cabinet,
    List<ProductImageModel>? images,
    StoreDetailModel? place,
  }) = _ProductModel;

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);
}
