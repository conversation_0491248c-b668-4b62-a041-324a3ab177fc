// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ShelfModelImpl _$$ShelfModelImplFromJson(Map<String, dynamic> json) =>
    _$ShelfModelImpl(
      id: (json['id'] as num?)?.toInt(),
      shelfCode: json['shelf_code'] as String?,
      status: json['status'] as String?,
      description: json['description'] as String?,
      height: (json['height'] as num?)?.toInt(),
      width: (json['width'] as num?)?.toInt(),
      depth: (json['depth'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ShelfModelImplToJson(_$ShelfModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'shelf_code': instance.shelfCode,
      'status': instance.status,
      'description': instance.description,
      'height': instance.height,
      'width': instance.width,
      'depth': instance.depth,
    };

_$CabinetModelImpl _$$CabinetModelImplFromJson(Map<String, dynamic> json) =>
    _$CabinetModelImpl(
      id: (json['id'] as num?)?.toInt(),
      cabinetCode: json['cabinet_code'] as String?,
      description: json['description'] as String?,
      type: json['type'] as String?,
    );

Map<String, dynamic> _$$CabinetModelImplToJson(_$CabinetModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cabinet_code': instance.cabinetCode,
      'description': instance.description,
      'type': instance.type,
    };

_$ProductImageModelImpl _$$ProductImageModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductImageModelImpl(
      id: (json['id'] as num?)?.toInt(),
      path: json['path'] as String?,
    );

Map<String, dynamic> _$$ProductImageModelImplToJson(
        _$ProductImageModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'path': instance.path,
    };

_$ProductModelImpl _$$ProductModelImplFromJson(Map<String, dynamic> json) =>
    _$ProductModelImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      description: json['description'] as String?,
      comment: json['comment'] as String?,
      note: json['note'] as String?,
      quantity: (json['quantity'] as num?)?.toInt(),
      expirationDate: json['expiration_date'] as String?,
      tag: (json['tag'] as List<dynamic>?)?.map((e) => e as String).toList(),
      capacity: json['capacity'] as String?,
      price: json['price'] as String?,
      salePrice: json['sale_price'] as String?,
      image: json['img'] as String?,
      shelf: json['shelf'] == null
          ? null
          : ShelfModel.fromJson(json['shelf'] as Map<String, dynamic>),
      cabinet: json['cabinet'] == null
          ? null
          : CabinetModel.fromJson(json['cabinet'] as Map<String, dynamic>),
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => ProductImageModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      place: json['place'] == null
          ? null
          : StoreDetailModel.fromJson(json['place'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProductModelImplToJson(_$ProductModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'comment': instance.comment,
      'note': instance.note,
      'quantity': instance.quantity,
      'expiration_date': instance.expirationDate,
      'tag': instance.tag,
      'capacity': instance.capacity,
      'price': instance.price,
      'sale_price': instance.salePrice,
      'img': instance.image,
      'shelf': instance.shelf,
      'cabinet': instance.cabinet,
      'images': instance.images,
      'place': instance.place,
    };
