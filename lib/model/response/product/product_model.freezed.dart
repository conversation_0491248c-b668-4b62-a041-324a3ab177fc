// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ShelfModel _$ShelfModelFromJson(Map<String, dynamic> json) {
  return _ShelfModel.fromJson(json);
}

/// @nodoc
mixin _$ShelfModel {
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'shelf_code')
  String? get shelfCode => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int? get height => throw _privateConstructorUsedError;
  int? get width => throw _privateConstructorUsedError;
  int? get depth => throw _privateConstructorUsedError;

  /// Serializes this ShelfModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShelfModelCopyWith<ShelfModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShelfModelCopyWith<$Res> {
  factory $ShelfModelCopyWith(
          ShelfModel value, $Res Function(ShelfModel) then) =
      _$ShelfModelCopyWithImpl<$Res, ShelfModel>;
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'shelf_code') String? shelfCode,
      String? status,
      String? description,
      int? height,
      int? width,
      int? depth});
}

/// @nodoc
class _$ShelfModelCopyWithImpl<$Res, $Val extends ShelfModel>
    implements $ShelfModelCopyWith<$Res> {
  _$ShelfModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? shelfCode = freezed,
    Object? status = freezed,
    Object? description = freezed,
    Object? height = freezed,
    Object? width = freezed,
    Object? depth = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      shelfCode: freezed == shelfCode
          ? _value.shelfCode
          : shelfCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      depth: freezed == depth
          ? _value.depth
          : depth // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShelfModelImplCopyWith<$Res>
    implements $ShelfModelCopyWith<$Res> {
  factory _$$ShelfModelImplCopyWith(
          _$ShelfModelImpl value, $Res Function(_$ShelfModelImpl) then) =
      __$$ShelfModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'shelf_code') String? shelfCode,
      String? status,
      String? description,
      int? height,
      int? width,
      int? depth});
}

/// @nodoc
class __$$ShelfModelImplCopyWithImpl<$Res>
    extends _$ShelfModelCopyWithImpl<$Res, _$ShelfModelImpl>
    implements _$$ShelfModelImplCopyWith<$Res> {
  __$$ShelfModelImplCopyWithImpl(
      _$ShelfModelImpl _value, $Res Function(_$ShelfModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? shelfCode = freezed,
    Object? status = freezed,
    Object? description = freezed,
    Object? height = freezed,
    Object? width = freezed,
    Object? depth = freezed,
  }) {
    return _then(_$ShelfModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      shelfCode: freezed == shelfCode
          ? _value.shelfCode
          : shelfCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      depth: freezed == depth
          ? _value.depth
          : depth // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShelfModelImpl implements _ShelfModel {
  const _$ShelfModelImpl(
      {this.id,
      @JsonKey(name: 'shelf_code') this.shelfCode,
      this.status,
      this.description,
      this.height,
      this.width,
      this.depth});

  factory _$ShelfModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShelfModelImplFromJson(json);

  @override
  final int? id;
  @override
  @JsonKey(name: 'shelf_code')
  final String? shelfCode;
  @override
  final String? status;
  @override
  final String? description;
  @override
  final int? height;
  @override
  final int? width;
  @override
  final int? depth;

  @override
  String toString() {
    return 'ShelfModel(id: $id, shelfCode: $shelfCode, status: $status, description: $description, height: $height, width: $width, depth: $depth)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShelfModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.shelfCode, shelfCode) ||
                other.shelfCode == shelfCode) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.depth, depth) || other.depth == depth));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, shelfCode, status, description, height, width, depth);

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShelfModelImplCopyWith<_$ShelfModelImpl> get copyWith =>
      __$$ShelfModelImplCopyWithImpl<_$ShelfModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShelfModelImplToJson(
      this,
    );
  }
}

abstract class _ShelfModel implements ShelfModel {
  const factory _ShelfModel(
      {final int? id,
      @JsonKey(name: 'shelf_code') final String? shelfCode,
      final String? status,
      final String? description,
      final int? height,
      final int? width,
      final int? depth}) = _$ShelfModelImpl;

  factory _ShelfModel.fromJson(Map<String, dynamic> json) =
      _$ShelfModelImpl.fromJson;

  @override
  int? get id;
  @override
  @JsonKey(name: 'shelf_code')
  String? get shelfCode;
  @override
  String? get status;
  @override
  String? get description;
  @override
  int? get height;
  @override
  int? get width;
  @override
  int? get depth;

  /// Create a copy of ShelfModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShelfModelImplCopyWith<_$ShelfModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CabinetModel _$CabinetModelFromJson(Map<String, dynamic> json) {
  return _CabinetModel.fromJson(json);
}

/// @nodoc
mixin _$CabinetModel {
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'cabinet_code')
  String? get cabinetCode => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;

  /// Serializes this CabinetModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CabinetModelCopyWith<CabinetModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CabinetModelCopyWith<$Res> {
  factory $CabinetModelCopyWith(
          CabinetModel value, $Res Function(CabinetModel) then) =
      _$CabinetModelCopyWithImpl<$Res, CabinetModel>;
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'cabinet_code') String? cabinetCode,
      String? description,
      String? type});
}

/// @nodoc
class _$CabinetModelCopyWithImpl<$Res, $Val extends CabinetModel>
    implements $CabinetModelCopyWith<$Res> {
  _$CabinetModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cabinetCode = freezed,
    Object? description = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CabinetModelImplCopyWith<$Res>
    implements $CabinetModelCopyWith<$Res> {
  factory _$$CabinetModelImplCopyWith(
          _$CabinetModelImpl value, $Res Function(_$CabinetModelImpl) then) =
      __$$CabinetModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'cabinet_code') String? cabinetCode,
      String? description,
      String? type});
}

/// @nodoc
class __$$CabinetModelImplCopyWithImpl<$Res>
    extends _$CabinetModelCopyWithImpl<$Res, _$CabinetModelImpl>
    implements _$$CabinetModelImplCopyWith<$Res> {
  __$$CabinetModelImplCopyWithImpl(
      _$CabinetModelImpl _value, $Res Function(_$CabinetModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cabinetCode = freezed,
    Object? description = freezed,
    Object? type = freezed,
  }) {
    return _then(_$CabinetModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cabinetCode: freezed == cabinetCode
          ? _value.cabinetCode
          : cabinetCode // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CabinetModelImpl implements _CabinetModel {
  const _$CabinetModelImpl(
      {this.id,
      @JsonKey(name: 'cabinet_code') this.cabinetCode,
      this.description,
      this.type});

  factory _$CabinetModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CabinetModelImplFromJson(json);

  @override
  final int? id;
  @override
  @JsonKey(name: 'cabinet_code')
  final String? cabinetCode;
  @override
  final String? description;
  @override
  final String? type;

  @override
  String toString() {
    return 'CabinetModel(id: $id, cabinetCode: $cabinetCode, description: $description, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CabinetModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cabinetCode, cabinetCode) ||
                other.cabinetCode == cabinetCode) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, cabinetCode, description, type);

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CabinetModelImplCopyWith<_$CabinetModelImpl> get copyWith =>
      __$$CabinetModelImplCopyWithImpl<_$CabinetModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CabinetModelImplToJson(
      this,
    );
  }
}

abstract class _CabinetModel implements CabinetModel {
  const factory _CabinetModel(
      {final int? id,
      @JsonKey(name: 'cabinet_code') final String? cabinetCode,
      final String? description,
      final String? type}) = _$CabinetModelImpl;

  factory _CabinetModel.fromJson(Map<String, dynamic> json) =
      _$CabinetModelImpl.fromJson;

  @override
  int? get id;
  @override
  @JsonKey(name: 'cabinet_code')
  String? get cabinetCode;
  @override
  String? get description;
  @override
  String? get type;

  /// Create a copy of CabinetModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CabinetModelImplCopyWith<_$CabinetModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductImageModel _$ProductImageModelFromJson(Map<String, dynamic> json) {
  return _ProductImageModel.fromJson(json);
}

/// @nodoc
mixin _$ProductImageModel {
  int? get id => throw _privateConstructorUsedError;
  String? get path => throw _privateConstructorUsedError;

  /// Serializes this ProductImageModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductImageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductImageModelCopyWith<ProductImageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductImageModelCopyWith<$Res> {
  factory $ProductImageModelCopyWith(
          ProductImageModel value, $Res Function(ProductImageModel) then) =
      _$ProductImageModelCopyWithImpl<$Res, ProductImageModel>;
  @useResult
  $Res call({int? id, String? path});
}

/// @nodoc
class _$ProductImageModelCopyWithImpl<$Res, $Val extends ProductImageModel>
    implements $ProductImageModelCopyWith<$Res> {
  _$ProductImageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductImageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? path = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      path: freezed == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductImageModelImplCopyWith<$Res>
    implements $ProductImageModelCopyWith<$Res> {
  factory _$$ProductImageModelImplCopyWith(_$ProductImageModelImpl value,
          $Res Function(_$ProductImageModelImpl) then) =
      __$$ProductImageModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? id, String? path});
}

/// @nodoc
class __$$ProductImageModelImplCopyWithImpl<$Res>
    extends _$ProductImageModelCopyWithImpl<$Res, _$ProductImageModelImpl>
    implements _$$ProductImageModelImplCopyWith<$Res> {
  __$$ProductImageModelImplCopyWithImpl(_$ProductImageModelImpl _value,
      $Res Function(_$ProductImageModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductImageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? path = freezed,
  }) {
    return _then(_$ProductImageModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      path: freezed == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductImageModelImpl implements _ProductImageModel {
  const _$ProductImageModelImpl({this.id, this.path});

  factory _$ProductImageModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductImageModelImplFromJson(json);

  @override
  final int? id;
  @override
  final String? path;

  @override
  String toString() {
    return 'ProductImageModel(id: $id, path: $path)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductImageModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.path, path) || other.path == path));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, path);

  /// Create a copy of ProductImageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductImageModelImplCopyWith<_$ProductImageModelImpl> get copyWith =>
      __$$ProductImageModelImplCopyWithImpl<_$ProductImageModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductImageModelImplToJson(
      this,
    );
  }
}

abstract class _ProductImageModel implements ProductImageModel {
  const factory _ProductImageModel({final int? id, final String? path}) =
      _$ProductImageModelImpl;

  factory _ProductImageModel.fromJson(Map<String, dynamic> json) =
      _$ProductImageModelImpl.fromJson;

  @override
  int? get id;
  @override
  String? get path;

  /// Create a copy of ProductImageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductImageModelImplCopyWith<_$ProductImageModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductModel _$ProductModelFromJson(Map<String, dynamic> json) {
  return _ProductModel.fromJson(json);
}

/// @nodoc
mixin _$ProductModel {
  int? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;
  String? get note => throw _privateConstructorUsedError;
  int? get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'expiration_date')
  String? get expirationDate => throw _privateConstructorUsedError;
  List<String>? get tag => throw _privateConstructorUsedError;
  String? get capacity => throw _privateConstructorUsedError;
  String? get price => throw _privateConstructorUsedError;
  @JsonKey(name: 'sale_price')
  String? get salePrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'img')
  String? get image => throw _privateConstructorUsedError;
  ShelfModel? get shelf => throw _privateConstructorUsedError;
  CabinetModel? get cabinet => throw _privateConstructorUsedError;
  List<ProductImageModel>? get images => throw _privateConstructorUsedError;
  StoreDetailModel? get place => throw _privateConstructorUsedError;

  /// Serializes this ProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductModelCopyWith<ProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductModelCopyWith<$Res> {
  factory $ProductModelCopyWith(
          ProductModel value, $Res Function(ProductModel) then) =
      _$ProductModelCopyWithImpl<$Res, ProductModel>;
  @useResult
  $Res call(
      {int? id,
      String? name,
      String? description,
      String? comment,
      String? note,
      int? quantity,
      @JsonKey(name: 'expiration_date') String? expirationDate,
      List<String>? tag,
      String? capacity,
      String? price,
      @JsonKey(name: 'sale_price') String? salePrice,
      @JsonKey(name: 'img') String? image,
      ShelfModel? shelf,
      CabinetModel? cabinet,
      List<ProductImageModel>? images,
      StoreDetailModel? place});

  $ShelfModelCopyWith<$Res>? get shelf;
  $CabinetModelCopyWith<$Res>? get cabinet;
  $StoreDetailModelCopyWith<$Res>? get place;
}

/// @nodoc
class _$ProductModelCopyWithImpl<$Res, $Val extends ProductModel>
    implements $ProductModelCopyWith<$Res> {
  _$ProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? comment = freezed,
    Object? note = freezed,
    Object? quantity = freezed,
    Object? expirationDate = freezed,
    Object? tag = freezed,
    Object? capacity = freezed,
    Object? price = freezed,
    Object? salePrice = freezed,
    Object? image = freezed,
    Object? shelf = freezed,
    Object? cabinet = freezed,
    Object? images = freezed,
    Object? place = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      capacity: freezed == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      salePrice: freezed == salePrice
          ? _value.salePrice
          : salePrice // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      shelf: freezed == shelf
          ? _value.shelf
          : shelf // ignore: cast_nullable_to_non_nullable
              as ShelfModel?,
      cabinet: freezed == cabinet
          ? _value.cabinet
          : cabinet // ignore: cast_nullable_to_non_nullable
              as CabinetModel?,
      images: freezed == images
          ? _value.images
          : images // ignore: cast_nullable_to_non_nullable
              as List<ProductImageModel>?,
      place: freezed == place
          ? _value.place
          : place // ignore: cast_nullable_to_non_nullable
              as StoreDetailModel?,
    ) as $Val);
  }

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShelfModelCopyWith<$Res>? get shelf {
    if (_value.shelf == null) {
      return null;
    }

    return $ShelfModelCopyWith<$Res>(_value.shelf!, (value) {
      return _then(_value.copyWith(shelf: value) as $Val);
    });
  }

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CabinetModelCopyWith<$Res>? get cabinet {
    if (_value.cabinet == null) {
      return null;
    }

    return $CabinetModelCopyWith<$Res>(_value.cabinet!, (value) {
      return _then(_value.copyWith(cabinet: value) as $Val);
    });
  }

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $StoreDetailModelCopyWith<$Res>? get place {
    if (_value.place == null) {
      return null;
    }

    return $StoreDetailModelCopyWith<$Res>(_value.place!, (value) {
      return _then(_value.copyWith(place: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductModelImplCopyWith<$Res>
    implements $ProductModelCopyWith<$Res> {
  factory _$$ProductModelImplCopyWith(
          _$ProductModelImpl value, $Res Function(_$ProductModelImpl) then) =
      __$$ProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? name,
      String? description,
      String? comment,
      String? note,
      int? quantity,
      @JsonKey(name: 'expiration_date') String? expirationDate,
      List<String>? tag,
      String? capacity,
      String? price,
      @JsonKey(name: 'sale_price') String? salePrice,
      @JsonKey(name: 'img') String? image,
      ShelfModel? shelf,
      CabinetModel? cabinet,
      List<ProductImageModel>? images,
      StoreDetailModel? place});

  @override
  $ShelfModelCopyWith<$Res>? get shelf;
  @override
  $CabinetModelCopyWith<$Res>? get cabinet;
  @override
  $StoreDetailModelCopyWith<$Res>? get place;
}

/// @nodoc
class __$$ProductModelImplCopyWithImpl<$Res>
    extends _$ProductModelCopyWithImpl<$Res, _$ProductModelImpl>
    implements _$$ProductModelImplCopyWith<$Res> {
  __$$ProductModelImplCopyWithImpl(
      _$ProductModelImpl _value, $Res Function(_$ProductModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? comment = freezed,
    Object? note = freezed,
    Object? quantity = freezed,
    Object? expirationDate = freezed,
    Object? tag = freezed,
    Object? capacity = freezed,
    Object? price = freezed,
    Object? salePrice = freezed,
    Object? image = freezed,
    Object? shelf = freezed,
    Object? cabinet = freezed,
    Object? images = freezed,
    Object? place = freezed,
  }) {
    return _then(_$ProductModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value._tag
          : tag // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      capacity: freezed == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      salePrice: freezed == salePrice
          ? _value.salePrice
          : salePrice // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      shelf: freezed == shelf
          ? _value.shelf
          : shelf // ignore: cast_nullable_to_non_nullable
              as ShelfModel?,
      cabinet: freezed == cabinet
          ? _value.cabinet
          : cabinet // ignore: cast_nullable_to_non_nullable
              as CabinetModel?,
      images: freezed == images
          ? _value._images
          : images // ignore: cast_nullable_to_non_nullable
              as List<ProductImageModel>?,
      place: freezed == place
          ? _value.place
          : place // ignore: cast_nullable_to_non_nullable
              as StoreDetailModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductModelImpl implements _ProductModel {
  const _$ProductModelImpl(
      {this.id,
      this.name,
      this.description,
      this.comment,
      this.note,
      this.quantity,
      @JsonKey(name: 'expiration_date') this.expirationDate,
      final List<String>? tag,
      this.capacity,
      this.price,
      @JsonKey(name: 'sale_price') this.salePrice,
      @JsonKey(name: 'img') this.image,
      this.shelf,
      this.cabinet,
      final List<ProductImageModel>? images,
      this.place})
      : _tag = tag,
        _images = images;

  factory _$ProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductModelImplFromJson(json);

  @override
  final int? id;
  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? comment;
  @override
  final String? note;
  @override
  final int? quantity;
  @override
  @JsonKey(name: 'expiration_date')
  final String? expirationDate;
  final List<String>? _tag;
  @override
  List<String>? get tag {
    final value = _tag;
    if (value == null) return null;
    if (_tag is EqualUnmodifiableListView) return _tag;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? capacity;
  @override
  final String? price;
  @override
  @JsonKey(name: 'sale_price')
  final String? salePrice;
  @override
  @JsonKey(name: 'img')
  final String? image;
  @override
  final ShelfModel? shelf;
  @override
  final CabinetModel? cabinet;
  final List<ProductImageModel>? _images;
  @override
  List<ProductImageModel>? get images {
    final value = _images;
    if (value == null) return null;
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final StoreDetailModel? place;

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, description: $description, comment: $comment, note: $note, quantity: $quantity, expirationDate: $expirationDate, tag: $tag, capacity: $capacity, price: $price, salePrice: $salePrice, image: $image, shelf: $shelf, cabinet: $cabinet, images: $images, place: $place)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.expirationDate, expirationDate) ||
                other.expirationDate == expirationDate) &&
            const DeepCollectionEquality().equals(other._tag, _tag) &&
            (identical(other.capacity, capacity) ||
                other.capacity == capacity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.salePrice, salePrice) ||
                other.salePrice == salePrice) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.shelf, shelf) || other.shelf == shelf) &&
            (identical(other.cabinet, cabinet) || other.cabinet == cabinet) &&
            const DeepCollectionEquality().equals(other._images, _images) &&
            (identical(other.place, place) || other.place == place));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      comment,
      note,
      quantity,
      expirationDate,
      const DeepCollectionEquality().hash(_tag),
      capacity,
      price,
      salePrice,
      image,
      shelf,
      cabinet,
      const DeepCollectionEquality().hash(_images),
      place);

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductModelImplCopyWith<_$ProductModelImpl> get copyWith =>
      __$$ProductModelImplCopyWithImpl<_$ProductModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductModelImplToJson(
      this,
    );
  }
}

abstract class _ProductModel implements ProductModel {
  const factory _ProductModel(
      {final int? id,
      final String? name,
      final String? description,
      final String? comment,
      final String? note,
      final int? quantity,
      @JsonKey(name: 'expiration_date') final String? expirationDate,
      final List<String>? tag,
      final String? capacity,
      final String? price,
      @JsonKey(name: 'sale_price') final String? salePrice,
      @JsonKey(name: 'img') final String? image,
      final ShelfModel? shelf,
      final CabinetModel? cabinet,
      final List<ProductImageModel>? images,
      final StoreDetailModel? place}) = _$ProductModelImpl;

  factory _ProductModel.fromJson(Map<String, dynamic> json) =
      _$ProductModelImpl.fromJson;

  @override
  int? get id;
  @override
  String? get name;
  @override
  String? get description;
  @override
  String? get comment;
  @override
  String? get note;
  @override
  int? get quantity;
  @override
  @JsonKey(name: 'expiration_date')
  String? get expirationDate;
  @override
  List<String>? get tag;
  @override
  String? get capacity;
  @override
  String? get price;
  @override
  @JsonKey(name: 'sale_price')
  String? get salePrice;
  @override
  @JsonKey(name: 'img')
  String? get image;
  @override
  ShelfModel? get shelf;
  @override
  CabinetModel? get cabinet;
  @override
  List<ProductImageModel>? get images;
  @override
  StoreDetailModel? get place;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductModelImplCopyWith<_$ProductModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
