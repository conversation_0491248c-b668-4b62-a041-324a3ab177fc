import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/login/provider/auth_notifier.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class ConfirmWithOTP extends ConsumerStatefulWidget {
  const ConfirmWithOTP({super.key, required this.email});
  final String email;

  @override
  ConsumerState<ConfirmWithOTP> createState() => _ConfirmWithOTPState();
}

class _ConfirmWithOTPState extends ConsumerState<ConfirmWithOTP>
    with TickerProviderStateMixin {
  final otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  late AnimationController _controller;
  int levelClock = 60;
  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
        vsync: this, duration: Duration(seconds: levelClock));

    _controller.forward();

    _startCountdown();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   showFirebaseTokenDialog(context);
    // });
  }

  void _startCountdown() {
    setState(() {
      levelClock = 60;
    });

    _controller.reset();
    _controller.forward();
  }

  void _resentOTP(AuthNotifier authNotifier) {
    /// call api
    authNotifier.reSentOtp(widget.email);
    _startCountdown();
    context.showSuccessSnackBar("OTPコード再送信");
  }

  @override
  void dispose() {
    otpController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);
    final authData = authState.maybeWhen(
      data: (data) => data,
      orElse: () => null,
    );
    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: S.current.login,
        onTap: () => Navigator.pop(context),
      ),
      body: SafeArea(
        top: false,
        child: GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Padding(
            padding: EdgeInsets.fromLTRB(16.sp, 0, 16.sp, 12.sp),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.zero,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Center(
                            child: Text(
                              S.current.authCodeSent,
                              textAlign: TextAlign.center,
                              style: AppTextStyles.regular(14.sp,
                                  color: AppColors.textLightSecondary),
                            ),
                          ),
                          SizedBox(height: 32.sp),
                          InputTextField(
                            label: "認証コード",
                            hintText: "認証コード",
                            textController: otpController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              LengthLimitingTextInputFormatter(6),
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "認証コードを入力してください。";
                              }
                              if (value.length != 6) {
                                return "認証コードは6桁で入力してください。";
                              }
                              return null;
                            },
                          ),
                          SizedBox(height: 20.sp),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                S.current.authCodeNotReceived,
                                style: AppTextStyles.bold(14.sp,
                                    color: AppColors.textLightSecondary),
                              ),
                              GestureDetector(
                                onTap: () {
                                  _resentOTP(authNotifier);
                                },
                                child: Countdown(
                                  animation: StepTween(
                                    begin: levelClock,
                                    end: 0,
                                  ).animate(_controller),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 20.sp),
                        ],
                      ),
                    ),
                  ),
                  authData?.isLoading ?? false
                      ? const Center(child: CircularProgressIndicator())
                      : CommonButton(
                          text: "ログイン",
                          onPressed: () async {
                            if (_formKey.currentState?.validate() ?? false) {
                              await authNotifier.verifyOtp(
                                  widget.email, otpController.text);
                              final authState = ref.watch(authNotifierProvider);
                              authState.whenData((state) {
                                if (state.verifyOtpFailure != null) {
                                  context.showErrorSnackBar(
                                      state.verifyOtpFailure ?? "エラーが発生しました");
                                } else if (state.isFirstLogin == false) {
                                  context.go(RouterPaths.home, extra: true);
                                } else if (state.isFirstLogin) {
                                  context.go(RouterPaths.signup,
                                      extra: state.userInfoModel);
                                }
                              });
                            }
                          },
                        ),
                  SizedBox(height: 12.h)
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// ignore: must_be_immutable
class Countdown extends AnimatedWidget {
  Countdown({super.key, required this.animation})
      : super(listenable: animation);
  Animation<int> animation;

  @override
  build(BuildContext context) {
    Duration clockTimer = Duration(seconds: animation.value);

    String timerText = (animation.value == 0)
        ? ""
        : clockTimer.inSeconds.remainder(60).toString().padLeft(2, '0');
    return Text(S.current.resend(timerText),
        style: AppTextStyles.regular(14.sp,
            color: animation.value == 0
                ? AppColors.primary
                : AppColors.textLightSecondary));
  }
}
