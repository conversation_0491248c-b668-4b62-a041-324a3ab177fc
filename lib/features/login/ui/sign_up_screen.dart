import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/custom_choose_avatar/dotted_circle_painter.dart';
import 'package:kitemite_app/core/common/widgets/dropdown/common_dropdown_field.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/image_picker/common_image_picker.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/validator_string.dart';
import 'package:kitemite_app/features/login/provider/auth_notifier.dart';
import 'package:kitemite_app/features/login/provider/provinces/province_notifier.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/features/shopping_cart/ui/widget/shopping_cart_item.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/model/request/auth/user_request.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class SignUpScreen extends ConsumerStatefulWidget {
  const SignUpScreen({super.key, required this.user});
  final UserInfoModel user;

  @override
  _SignUpScreenState createState() => _SignUpScreenState();
}

class _SignUpScreenState extends ConsumerState<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  List<String> _prefectureList = [];

  final TextEditingController _nicknameController = TextEditingController();
  final TextEditingController _birthYearController = TextEditingController();
  final TextEditingController _prefectureController = TextEditingController();
  final TextEditingController _genderController = TextEditingController();
  final TextEditingController _occupationController = TextEditingController();
  final List<String> _genders = ["male", "female", "no_answer"];
  final Map<String, String> _genderMap = {
    "male": "女性",
    "female": "男性",
    "no_answer": "回答しない",
  };

  String _genderJpToBe(String jp) {
    return _genderMap.entries
        .firstWhere(
          (e) => e.value == jp,
          orElse: () => const MapEntry("no_answer", "未回答"),
        )
        .key;
  }

  @override
  void initState() {
    super.initState();
    _nicknameController.text = widget.user.username ?? "";
    _birthYearController.text = widget.user.yob ?? "";
    _prefectureController.text = widget.user.province ?? "";
    final gender = widget.user.gender ?? "";
    _genderController.text =
        _genders.contains(gender) ? gender : _genderJpToBe(gender);
    _occupationController.text = widget.user.job ?? "";

    // Fetch provinces
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(authNotifierProvider.notifier)
          .upDateImageAvatar(widget.user.img ?? "");
      _fetchProvinces();
    });
  }

  Future<void> _fetchProvinces() async {
    await ref.read(provinceNotifierProvider.notifier).getProvinces();
    final provinceState = ref.read(provinceNotifierProvider);
    if (provinceState.provinces != null) {
      setState(() {
        _prefectureList =
            provinceState.provinces!.map((province) => province.name).toList();
      });
    }
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _birthYearController.dispose();
    _prefectureController.dispose();
    _genderController.dispose();
    _occupationController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    final genderBe = _genders.contains(_genderController.text)
        ? _genderController.text
        : _genderJpToBe(_genderController.text);
    if (_formKey.currentState?.validate() ?? false) {
      await ref.read(authNotifierProvider.notifier).updateProfile(
            UserRequest(
              username: _nicknameController.text,
              yob: _birthYearController.text,
              province: _prefectureController.text,
              gender: genderBe,
              job: _occupationController.text,
              email: widget.user.email,
            ),
            widget.user.id,
          );
      final authState = ref.watch(authNotifierProvider);
      authState.whenData((state) {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          if (state.upDateProfileFailure != null) {
            context.showErrorSnackBar(state.upDateProfileFailure ?? "");
          }
          if (state.isUpdateprofileSuccess) {
            await ref.read(profileProviderProvider.notifier).refreshProfile();
            context.push(RouterPaths.registerPayment);
          }
        });
      });
    } else {
      print("Form không hợp lệ.");
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider);
    final authData = authState.maybeWhen(
      data: (data) => data,
      orElse: () => null,
    );
    return BaseScaffold(
      appBar: CustomAppbar(
        title: S.current.userRegistration,
      ),
      body: SafeArea(
        top: false,
        child: GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
            child: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    SizedBox(height: 8.h),
                    InputTextField(
                      label: S.current.nickname,
                      textController: _nicknameController,
                      validator: Validator.validateNickname,
                    ),
                    SizedBox(height: 24.h),
                    InputTextField(
                      label: S.current.birthYear,
                      hintText: "西暦 (例) 1979",
                      textController: _birthYearController,
                      keyboardType: TextInputType.number,
                      validator: Validator.validateBirthYear,
                    ),
                    SizedBox(height: 24.h),
                    CommonDropdownField<String>(
                      label: S.current.prefecture,
                      items: _prefectureList,
                      value:
                          _prefectureList.contains(_prefectureController.text)
                              ? _prefectureController.text
                              : null,
                      itemLabel: (prefecture) => prefecture,
                      onChanged: (String? newValue) {
                        setState(() {
                          _prefectureController.text = newValue ?? "";
                        });
                      },
                      validator: Validator.validatePrefecture,
                    ),
                    SizedBox(height: 24.h),
                    CommonDropdownField<String>(
                      label: S.current.gender,
                      items: _genders,
                      value: _genders.contains(_genderController.text)
                          ? _genderController.text
                          : null,
                      itemLabel: (gender) => _genderMap[gender] ?? gender,
                      onChanged: (String? newValue) {
                        setState(() {
                          _genderController.text = newValue ?? "";
                        });
                      },
                      validator: Validator.validateGender,
                    ),
                    SizedBox(height: 24.h),
                    InputTextField(
                      label: S.current.occupation,
                      textController: _occupationController,
                      validator: Validator.validateOccupation,
                    ),
                    SizedBox(height: 32.h),
                    DottedCircleWithImage(
                      urlImage: widget.user.img,
                    ),
                    SizedBox(height: 24.h),
                    Text(
                      S.current.uploadInstructions,
                      textAlign: TextAlign.center,
                      style: AppTextStyles.regular(12.sp,
                          color: AppColors.textLightSecondary),
                    ),
                    SizedBox(height: 37.h),
                    authData?.isLoading ?? false
                        ? const Center(child: CircularProgressIndicator())
                        : CommonButton(
                            onPressed: () => _submitForm(),
                            text: S.current.next,
                          ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class DottedCircleWithImage extends ConsumerStatefulWidget {
  const DottedCircleWithImage({super.key, this.urlImage});
  final String? urlImage;

  @override
  _DottedCircleWithImageState createState() => _DottedCircleWithImageState();
}

class _DottedCircleWithImageState extends ConsumerState<DottedCircleWithImage> {
  File? _selectedImage;
  final CommonImagePicker _imagePicker = CommonImagePicker(
    maxSizeInBytes: 50 * 1024 * 1024, // Giới hạn 10MB
    allowedFormats: ['jpg', 'jpeg', 'png', 'gif'],
  );

  void _showImagePickerBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (_) => SafeArea(
        child: _buildImagePickerOptions(context),
      ),
    );
  }

  Future<void> _pickImage(BuildContext context, ImageSource source) async {
    context.pop();
    try {
      final XFile? pickedFile =
          await _imagePicker.pickImage(context, source: source);
      if (pickedFile != null) {
        File imageFile = File(pickedFile.path);
        setState(() {
          _selectedImage = imageFile;
        });
        await _uploadImage(imageFile);
      }
    } catch (e) {
      checkCameraPermission(context);
    }
  }

  Future<void> _uploadImage(File image) async {
    String base64Image = await _imagePicker.convertToBase64(image);
    print("Base64 Image: $base64Image");

    await ref.read(authNotifierProvider.notifier).upFileImage(base64Image);
  }

  Widget _buildImagePickerOptions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.camera_alt),
            title: Text(S.current.takePhoto),
            onTap: () => _pickImage(context, ImageSource.camera),
          ),
          ListTile(
            leading: const Icon(Icons.photo_library),
            title: Text(S.current.chooseFromLibrary),
            onTap: () => _pickImage(context, ImageSource.gallery),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double outerCircleSize = 150;
    double padding = 4;
    double innerCircleSize = outerCircleSize - (padding * 2);
    final authState = ref.watch(authNotifierProvider);

    authState.whenData((state) {
      if (state.upFileImageFailure != null) {
        context.showErrorSnackBar(state.upFileImageFailure ?? "");
      }
    });

    // Check if image is uploading
    final isUploading = authState.maybeWhen(
      data: (data) => data.isUploadingImage,
      orElse: () => false,
    );

    return GestureDetector(
      onTap: () => _showImagePickerBottomSheet(context),
      child: Stack(
        alignment: Alignment.center,
        children: [
          CustomPaint(
            size: Size(outerCircleSize, outerCircleSize),
            painter: DottedCirclePainter(),
          ),
          Container(
            width: innerCircleSize,
            height: innerCircleSize,
            padding: EdgeInsets.all(padding),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: ClipOval(
              child: isUploading
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : _selectedImage != null
                      ? Stack(
                          fit: StackFit.expand,
                          children: [
                            Image.file(
                              _selectedImage!,
                              fit: BoxFit.cover,
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.3),
                                shape: BoxShape.circle,
                              ),
                            ),
                          ],
                        )
                      : widget.urlImage != null
                          ? Stack(
                              fit: StackFit.expand,
                              children: [
                                BaseCachedNetworkImage(
                                  imageUrl: widget.urlImage!,
                                  borderRadius: BorderRadius.circular(20.r),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.3),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ],
                            )
                          : Stack(
                              fit: StackFit.expand,
                              children: [
                                Assets.avatarDefault.image(
                                  fit: BoxFit.cover,
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.3),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ],
                            ),
            ),
          ),
          if (!isUploading)
            Column(
              children: [
                Icon(
                  Icons.add_a_photo,
                  color: AppColors.mono0,
                  size: 24.sp,
                ),
                SizedBox(height: 8.h),
                Text(
                  S.current.updatePhoto,
                  style: AppTextStyles.regular(12.sp, color: AppColors.mono0),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
