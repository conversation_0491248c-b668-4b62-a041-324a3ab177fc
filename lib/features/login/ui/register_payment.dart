import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/webview/custom_webview.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/login/provider/payment_notifier.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/model/response/auth/payment_info_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class RegisterPayment extends ConsumerStatefulWidget {
  const RegisterPayment({
    super.key,
    this.isOpenByPayment,
  });
  final bool? isOpenByPayment;

  @override
  ConsumerState<RegisterPayment> createState() => _RegisterPaymentState();
}

class _RegisterPaymentState extends ConsumerState<RegisterPayment> {
  int? selectedCardId;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      var userId = ref.watch(profileProviderProvider).value?.profile?.id ?? 0;

      ref.read(paymentNotifierProvider.notifier).getPaymentInformation(userId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final paymentState = ref.watch(paymentNotifierProvider);

    // Listen to payment information changes and update selectedCardId
    ref.listen(paymentNotifierProvider.select((state) => state.paymentInfos),
        (previous, next) {
      if (next != null && next.isNotEmpty && mounted) {
        final defaultCard = next.firstWhere(
          (card) => card.isDefault,
          orElse: () => next.first,
        );
        setState(() {
          selectedCardId = defaultCard.id;
        });
      }
    });

    // Show error SnackBar when error state changes
    ref.listen(paymentNotifierProvider, (previous, next) {
      if (next.error != null && next.error != previous?.error && mounted) {
        context.showErrorSnackBar(next.error ?? "");
      }
    });

    return BaseScaffold(
        appBar: CustomAppbar.basic(
          title: S.current.paymentMethodRegistration,
          onTap: () {
            Navigator.pop(context);
          },
        ),
        body: SafeArea(
            top: false,
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0),
              child: Column(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: Text(S.current.paymentMethodDescription,
                              textAlign: TextAlign.center,
                              style: AppTextStyles.regular(14.sp,
                                  color: AppColors.textLightSecondary)),
                        ),
                        SizedBox(height: 8.h),
                        Center(
                          child: Text(S.current.canRegisterLater,
                              textAlign: TextAlign.center,
                              style: AppTextStyles.regular(14.sp,
                                  color: AppColors.textLightSecondary)),
                        ),
                        SizedBox(height: 38.h),
                        if (!(paymentState.paymentInfos?.isEmpty ?? true))
                          Text(
                            S.current.creditCard,
                            style: AppTextStyles.bold(12.sp,
                                color: AppColors.textLightSecondary),
                          ),
                        SizedBox(height: 16.h),
                        if (paymentState.isLoading ||
                            paymentState.isDeletingCard)
                          const Center(child: CircularProgressIndicator())
                        else if (paymentState.paymentInfos?.isEmpty ?? true)
                          Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  Assets.paymentProcessed,
                                ),
                                SizedBox(height: 16.h),
                                Center(
                                    child: GestureDetector(
                                  onTap: () => _handleAddCard(),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.add,
                                        color: AppColors.primary,
                                        size: 24.sp,
                                      ),
                                      SizedBox(width: 8.w),
                                      Text(
                                        S.current.addCard,
                                        textAlign: TextAlign.center,
                                        style: AppTextStyles.bold(14.sp,
                                            color: AppColors.primary),
                                      ),
                                    ],
                                  ),
                                ))
                              ],
                            ),
                          )
                        else
                          Expanded(
                              child: Column(
                            children: [
                              Expanded(
                                child: PaymentCardList(
                                  paymentInfos: paymentState.paymentInfos ?? [],
                                  onAddCard: _handleAddCard,
                                  selectedCardId: selectedCardId,
                                  onCardTap: (paymentInfo) {
                                    setState(() {
                                      selectedCardId = paymentInfo.id;
                                    });
                                  },
                                  onCardDelete: _handleDeleteCard,
                                ),
                              ),
                              SizedBox(height: 16.h),
                            ],
                          )),
                      ],
                    ),
                  ),
                  paymentState.isDefaultCardLoading
                      ? const CircularProgressIndicator()
                      : CommonButton(
                          onPressed: () async {
                            if (paymentState.paymentInfos?.length == 0) {
                              context.go(RouterPaths.home, extra: true);
                              return;
                            }

                            bool success = false;
                            if (selectedCardId != null) {
                              final userId = ref
                                  .read(profileProviderProvider)
                                  .value
                                  ?.profile
                                  ?.id;

                              if (userId != null) {
                                success = await ref
                                    .read(paymentNotifierProvider.notifier)
                                    .changeDefaultCard(userId, selectedCardId!);
                              }
                            }
                            if (success) {
                              if (mounted) {
                                (widget.isOpenByPayment ?? false)
                                    ? context.pop()
                                    : context.go(RouterPaths.home, extra: true);
                              }
                            }
                          },
                          text: S.current.skip,
                        ),
                ],
              ),
            )));
  }

  Future<void> _handleAddCard() async {
    var userId = ref.watch(profileProviderProvider).value?.profile?.id;
    final url =
        await ref.read(paymentNotifierProvider.notifier).generateLinkAddCard();
    if (url != null && mounted) {
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => Scaffold(
            body: Stack(
              children: [
                CustomWebView(
                  url: url,
                  title: S.current.addCard,
                  onPageFinished: (url) {},
                ),
                SafeArea(
                  top: true,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        margin: EdgeInsets.all(8.w),
                        padding: EdgeInsets.all(8.w),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 24.w,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ).then((value) {
        if (widget.isOpenByPayment ?? false) {
          context.pop();
        }
        ref
            .read(paymentNotifierProvider.notifier)
            .getPaymentInformation(userId ?? 0);
      });
    }
  }

  Future<void> _handleDeleteCard(PaymentInfoModel card) async {
    final userId = ref.watch(profileProviderProvider).value?.profile?.id;
    if (userId != null) {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.delete,
                    size: 24.w,
                    color: Colors.red,
                  ),
                ),
                SizedBox(height: 12.h),
                Text(
                  'カードを削除しますか？',
                  style:
                      AppTextStyles.bold(16.sp, color: AppColors.textPrimary),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 4.h),
                Text(
                  'この操作は取り消せません。',
                  style: AppTextStyles.regular(12.sp,
                      color: AppColors.textLightSecondary),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16.h),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context, false),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          side: const BorderSide(color: AppColors.border),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                        ),
                        child: Text(
                          'キャンセル',
                          style: AppTextStyles.regular(12.sp,
                              color: AppColors.textLightSecondary),
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context, true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                        ),
                        child: Text(
                          '削除',
                          style: AppTextStyles.bold(12.sp, color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );

      if (confirmed == true) {
        final success = await ref
            .read(paymentNotifierProvider.notifier)
            .deleteCard(userId, card.id);

        if (success && mounted) {
          // If the deleted card was selected, clear the selection
          if (selectedCardId == card.id) {
            setState(() {
              selectedCardId = null;
            });
          }
        }
      }
    }
  }
}

class PaymentCardList extends StatefulWidget {
  final List<PaymentInfoModel> paymentInfos;
  final VoidCallback onAddCard;
  final Function(PaymentInfoModel) onCardTap;
  final Function(PaymentInfoModel) onCardDelete;
  final int? selectedCardId;

  const PaymentCardList({
    super.key,
    required this.paymentInfos,
    required this.onAddCard,
    required this.onCardTap,
    required this.onCardDelete,
    this.selectedCardId,
  });

  @override
  State<PaymentCardList> createState() => _PaymentCardListState();
}

class _PaymentCardListState extends State<PaymentCardList> {
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: widget.paymentInfos.length + 1,
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        if (index == widget.paymentInfos.length) {
          return Padding(
              padding: EdgeInsets.only(top: 16.h),
              child: GestureDetector(
                onTap: widget.onAddCard,
                child: Row(
                  children: [
                    Icon(
                      Icons.add,
                      color: AppColors.primary,
                      size: 24.sp,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      S.current.addCard,
                      textAlign: TextAlign.center,
                      style:
                          AppTextStyles.bold(14.sp, color: AppColors.primary),
                    ),
                  ],
                ),
              ));
        }

        final paymentInfo = widget.paymentInfos[index];
        return Padding(
          padding: EdgeInsets.only(
              bottom: index < widget.paymentInfos.length - 1 ? 16.0 : 0),
          child: PaymentCardItem(
            cardLogo: BaseCachedNetworkImage(
                imageUrl: paymentInfo.brand == 'VISA'
                    ? 'https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png'
                    : 'https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png',
                height: 50,
                width: 50,
                fit: BoxFit.contain),
            cardNumber: paymentInfo.cardNo,
            isSelected: widget.selectedCardId == paymentInfo.id,
            onTap: () => widget.onCardTap(paymentInfo),
            onDelete: () => widget.onCardDelete(paymentInfo),
          ),
        );
      },
    );
  }
}

class PaymentCardItem extends StatelessWidget {
  final Widget cardLogo;
  final String cardNumber;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback onDelete;

  const PaymentCardItem({
    super.key,
    required this.cardLogo,
    required this.cardNumber,
    required this.isSelected,
    required this.onTap,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Slidable(
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        extentRatio: 0.3,
        children: [
          SlidableAction(
            onPressed: (context) => onDelete(),
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: '削除',
            padding: EdgeInsets.zero,
            autoClose: true,
            flex: 1,
          ),
        ],
      ),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.fromLTRB(24, 16, 16, 24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300, width: 1),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  cardLogo,
                  Text(
                    cardNumber,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              _buildSelectionCircle(isSelected),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSelectionCircle(bool isSelected) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: isSelected ? AppColors.primary : Colors.grey.shade600,
          width: 2,
        ),
      ),
      child: isSelected
          ? Center(
              child: Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.primary,
                ),
              ),
            )
          : null,
    );
  }
}
