import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/utils/app_preferences.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/validator_string.dart';
import 'package:kitemite_app/features/login/provider/auth_notifier.dart';
import 'package:kitemite_app/features/login/provider/auth_state.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/ui/profile_screen.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class LoginScreen extends ConsumerWidget {
  LoginScreen({super.key});

  final emailController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);
    final authData = authState.maybeWhen(
      data: (data) => data,
      orElse: () => null,
    );

    ref.listen(authNotifierProvider, (previous, next) {
      next.maybeWhen(
        data: (data) {
          if (data.loginSuccess) {
            context.push(RouterPaths.confirmWithOtp,
                extra: emailController.text);
            authNotifier.reset();
          } else if (data.loginFailure != null) {
            context.showErrorSnackBar(data.loginFailure!);
          }
        },
        orElse: () {},
      );
    });

    return BaseScaffold(
      appBar: CustomAppbar(title: S.current.login),
      body: Form(
        key: _formKey,
        child: GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 8.h),
                InputTextField(
                    label: S.current.enterEmailAddress,
                    hintText: S.current.enterEmailAddress,
                    textController: emailController,
                    validator: Validator.validateEmail),
                SizedBox(height: 20.h),
                _buildPolicyText(context),
                SizedBox(height: 16.h),
                _buildCheckboxes(authNotifier, authData),
                SizedBox(height: 24.h),
                _buildLoginButton(context, authNotifier, authData, ref ),
                SizedBox(height: 8.h),
                Center(
                  child: TextButton(
                    onPressed: () async {
                      ref.read(modeNotifierProvider.notifier).setModeGuest();
                      context.go(RouterPaths.home, extra: false);
                    },
                    child: Text(
                      S.current.viewAsGuest,
                      style: AppTextStyles.bold(16.sp,
                          color: AppColors.textPrimary),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginButton(
      BuildContext context, AuthNotifier authNotifier, AuthState? authData, WidgetRef ref,  ) {
    return authData?.isLoading ?? false
        ? const Center(child: CircularProgressIndicator())
        : Opacity(
            opacity: ((authData?.agree1 ?? false) &&
                    (authData?.agree2 ?? false) &&
                    (authData?.agree3 ?? false))
                ? 1
                : 0.6,
            child: CommonButton(
              onPressed: () {
                if ((authData?.agree1 ?? false) &&
                    (authData?.agree2 ?? false) &&
                    (authData?.agree3 ?? false)) {
                  if (_formKey.currentState?.validate() ?? false) {
                    authNotifier.login(emailController.text);
                     ref.read(modeNotifierProvider.notifier).resetState();
                  }
                }
              },
              text: S.current.sendAuthCode,
            ),
          );
  }

  Widget _buildCheckboxes(AuthNotifier authNotifier, AuthState? authData) {
    Widget customCheckbox({
      required bool value,
      required VoidCallback onChanged,
      required String title,
    }) {
      return GestureDetector(
        onTap: onChanged,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Checkbox(
              value: value,
              onChanged: (_) => onChanged(),
              visualDensity: VisualDensity.compact,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              side: const BorderSide(
                color: AppColors.textLightSecondary,
                width: 1.5,
              ),
            ),
            SizedBox(width: 8.w),
            Flexible(
              child: Text(
                title,
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        customCheckbox(
          value: authData?.agree1 ?? false,
          onChanged: authNotifier.toggleAgree1,
          title: S.current.iUnderstandTerms,
        ),
        SizedBox(height: 8.h),
        customCheckbox(
          value: authData?.agree2 ?? false,
          onChanged: authNotifier.toggleAgree2,
          title: S.current.iUnderstandPrivacy,
        ),
        SizedBox(height: 8.h),
        customCheckbox(
          value: authData?.agree3 ?? false,
          onChanged: authNotifier.toggleAgree3,
          title: S.current.iAmOver20,
        ),
      ],
    );
  }

  Widget _buildPolicyText(BuildContext context) {
    return EasyRichText(
      S.current.termsAndPrivacyPolicy,
      defaultStyle: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
      patternList: [
        EasyRichTextPattern(
          targetString: S.current.termsOfService,
          style: AppTextStyles.bold(14.sp, color: AppColors.blueDark),
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              launchUrlCustom(context, AppConstants.termsUrl);
            },
        ),
        EasyRichTextPattern(
          targetString: S.current.privacyPolicy,
          style: AppTextStyles.bold(14.sp, color: AppColors.blueDark),
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              launchUrlCustom(context, AppConstants.policyUrl);
            },
        ),
      ],
    );
  }
}
