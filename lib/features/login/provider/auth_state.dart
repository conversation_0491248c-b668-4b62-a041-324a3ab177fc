import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';

part 'auth_state.freezed.dart';

@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default(false) bool agree1,
    @Default(false) bool agree2,
    @Default(false) bool agree3,
    @Default(false) bool isLoading,
    @Default(false) bool isUploadingImage,
    @Default(false) bool loginSuccess,
    @Default(false) bool verifySuccess,
    @Default(false) bool isFirstLogin,
    @Default(false) bool isUpdateprofileSuccess,
    @Default(false) bool isDeviceRegistered,
    String? urlImage,
    String? loginFailure,
    String? verifyOtpFailure,
    String? upFileImageFailure,
    String? upDateProfileFailure,
    String? deviceRegistrationFailure,
    UserInfoModel? userInfoModel,
  }) = _AuthState;
}
