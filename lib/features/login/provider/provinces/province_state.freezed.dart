// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'province_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProvinceState {
  bool get isLoading => throw _privateConstructorUsedError;
  List<ProvinceModel>? get provinces => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of ProvinceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvinceStateCopyWith<ProvinceState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvinceStateCopyWith<$Res> {
  factory $ProvinceStateCopyWith(
          ProvinceState value, $Res Function(ProvinceState) then) =
      _$ProvinceStateCopyWithImpl<$Res, ProvinceState>;
  @useResult
  $Res call({bool isLoading, List<ProvinceModel>? provinces, String? error});
}

/// @nodoc
class _$ProvinceStateCopyWithImpl<$Res, $Val extends ProvinceState>
    implements $ProvinceStateCopyWith<$Res> {
  _$ProvinceStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvinceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? provinces = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      provinces: freezed == provinces
          ? _value.provinces
          : provinces // ignore: cast_nullable_to_non_nullable
              as List<ProvinceModel>?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvinceStateImplCopyWith<$Res>
    implements $ProvinceStateCopyWith<$Res> {
  factory _$$ProvinceStateImplCopyWith(
          _$ProvinceStateImpl value, $Res Function(_$ProvinceStateImpl) then) =
      __$$ProvinceStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isLoading, List<ProvinceModel>? provinces, String? error});
}

/// @nodoc
class __$$ProvinceStateImplCopyWithImpl<$Res>
    extends _$ProvinceStateCopyWithImpl<$Res, _$ProvinceStateImpl>
    implements _$$ProvinceStateImplCopyWith<$Res> {
  __$$ProvinceStateImplCopyWithImpl(
      _$ProvinceStateImpl _value, $Res Function(_$ProvinceStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvinceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? provinces = freezed,
    Object? error = freezed,
  }) {
    return _then(_$ProvinceStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      provinces: freezed == provinces
          ? _value._provinces
          : provinces // ignore: cast_nullable_to_non_nullable
              as List<ProvinceModel>?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ProvinceStateImpl implements _ProvinceState {
  const _$ProvinceStateImpl(
      {this.isLoading = false,
      final List<ProvinceModel>? provinces = const [],
      this.error})
      : _provinces = provinces;

  @override
  @JsonKey()
  final bool isLoading;
  final List<ProvinceModel>? _provinces;
  @override
  @JsonKey()
  List<ProvinceModel>? get provinces {
    final value = _provinces;
    if (value == null) return null;
    if (_provinces is EqualUnmodifiableListView) return _provinces;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? error;

  @override
  String toString() {
    return 'ProvinceState(isLoading: $isLoading, provinces: $provinces, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvinceStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            const DeepCollectionEquality()
                .equals(other._provinces, _provinces) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading,
      const DeepCollectionEquality().hash(_provinces), error);

  /// Create a copy of ProvinceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvinceStateImplCopyWith<_$ProvinceStateImpl> get copyWith =>
      __$$ProvinceStateImplCopyWithImpl<_$ProvinceStateImpl>(this, _$identity);
}

abstract class _ProvinceState implements ProvinceState {
  const factory _ProvinceState(
      {final bool isLoading,
      final List<ProvinceModel>? provinces,
      final String? error}) = _$ProvinceStateImpl;

  @override
  bool get isLoading;
  @override
  List<ProvinceModel>? get provinces;
  @override
  String? get error;

  /// Create a copy of ProvinceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvinceStateImplCopyWith<_$ProvinceStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
