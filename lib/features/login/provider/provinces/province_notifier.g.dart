// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'province_notifier.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$provinceNotifierHash() => r'9f5145e3bd6e3bb9f16952b891464ef07a6a62de';

/// See also [ProvinceNotifier].
@ProviderFor(ProvinceNotifier)
final provinceNotifierProvider =
    AutoDisposeNotifierProvider<ProvinceNotifier, ProvinceState>.internal(
  ProvinceNotifier.new,
  name: r'provinceNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$provinceNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProvinceNotifier = AutoDisposeNotifier<ProvinceState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
