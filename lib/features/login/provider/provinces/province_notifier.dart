import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/repository/auth/auth_repository.dart';
import 'province_state.dart';

part 'province_notifier.g.dart';

@riverpod
class ProvinceNotifier extends _$ProvinceNotifier {
  late final AuthRepository _authRepository;

  @override
  ProvinceState build() {
    _authRepository = ref.read(getAuthRepositoryProvider);
    ref.keepAlive();
    return const ProvinceState();
  }

  Future<void> getProvinces() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await _authRepository.getProvinces();
      state = state.copyWith(
        isLoading: false,
        provinces: response.provinces,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }
}
