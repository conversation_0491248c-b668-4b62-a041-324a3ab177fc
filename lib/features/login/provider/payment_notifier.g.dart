// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_notifier.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$paymentNotifierHash() => r'0b04e88f14ae1dec3caf26c672b74d5be99ebda9';

/// See also [PaymentNotifier].
@ProviderFor(PaymentNotifier)
final paymentNotifierProvider =
    AutoDisposeNotifierProvider<PaymentNotifier, PaymentState>.internal(
  PaymentNotifier.new,
  name: r'paymentNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$paymentNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PaymentNotifier = AutoDisposeNotifier<PaymentState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
