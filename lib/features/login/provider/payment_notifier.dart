import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/repository/auth/auth_repository.dart';
import 'payment_state.dart';

part 'payment_notifier.g.dart';

@riverpod
class PaymentNotifier extends _$PaymentNotifier {
  late final AuthRepository _authRepository;

  @override
  PaymentState build() {
    _authRepository = ref.read(getAuthRepositoryProvider);
    return const PaymentState();
  }

  Future<void> getPaymentInformation(int userId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response =
          await _authRepository.getPaymentInformation(userId: userId);
      state = state.copyWith(
        isLoading: false,
        paymentInfos: response.data,
        error: null,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }

  Future<bool> changeDefaultCard(int userId, int cardId) async {
    state = state.copyWith(
        error: null,
        isDefaultCardLoading: true,
        changeDefaultCardSuccess: false);
    try {
      await _authRepository.changeDefaultCard(userId: userId, cardId: cardId);
      state = state.copyWith(
        isDefaultCardLoading: false,
        changeDefaultCardSuccess: true,
      );
      return true;
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isDefaultCardLoading: false,
        changeDefaultCardSuccess: false,
      );
      return false;
    }
  }

  Future<String?> generateLinkAddCard() async {
    state = state.copyWith(error: null);
    try {
      final response = await _authRepository.generateLinkAddCard();
      return response.data?.linkUrl;
    } on AppFailure catch (e) {
      state = state.copyWith(error: e.message);
      return null;
    }
  }

  Future<bool> deleteCard(int userId, int cardId) async {
    state = state.copyWith(error: null, isDeletingCard: true);
    try {
      await _authRepository.deleteCard(userId: userId, cardId: cardId);
      // Refresh payment information after successful deletion
      await getPaymentInformation(userId);
      state = state.copyWith(isDeletingCard: false);
      return true;
    } on AppFailure catch (e) {
      state = state.copyWith(error: e.message, isDeletingCard: false);
      return false;
    }
  }
}
