// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthState {
  bool get agree1 => throw _privateConstructorUsedError;
  bool get agree2 => throw _privateConstructorUsedError;
  bool get agree3 => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isUploadingImage => throw _privateConstructorUsedError;
  bool get loginSuccess => throw _privateConstructorUsedError;
  bool get verifySuccess => throw _privateConstructorUsedError;
  bool get isFirstLogin => throw _privateConstructorUsedError;
  bool get isUpdateprofileSuccess => throw _privateConstructorUsedError;
  bool get isDeviceRegistered => throw _privateConstructorUsedError;
  String? get urlImage => throw _privateConstructorUsedError;
  String? get loginFailure => throw _privateConstructorUsedError;
  String? get verifyOtpFailure => throw _privateConstructorUsedError;
  String? get upFileImageFailure => throw _privateConstructorUsedError;
  String? get upDateProfileFailure => throw _privateConstructorUsedError;
  String? get deviceRegistrationFailure => throw _privateConstructorUsedError;
  UserInfoModel? get userInfoModel => throw _privateConstructorUsedError;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthStateCopyWith<AuthState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) then) =
      _$AuthStateCopyWithImpl<$Res, AuthState>;
  @useResult
  $Res call(
      {bool agree1,
      bool agree2,
      bool agree3,
      bool isLoading,
      bool isUploadingImage,
      bool loginSuccess,
      bool verifySuccess,
      bool isFirstLogin,
      bool isUpdateprofileSuccess,
      bool isDeviceRegistered,
      String? urlImage,
      String? loginFailure,
      String? verifyOtpFailure,
      String? upFileImageFailure,
      String? upDateProfileFailure,
      String? deviceRegistrationFailure,
      UserInfoModel? userInfoModel});
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res, $Val extends AuthState>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? agree1 = null,
    Object? agree2 = null,
    Object? agree3 = null,
    Object? isLoading = null,
    Object? isUploadingImage = null,
    Object? loginSuccess = null,
    Object? verifySuccess = null,
    Object? isFirstLogin = null,
    Object? isUpdateprofileSuccess = null,
    Object? isDeviceRegistered = null,
    Object? urlImage = freezed,
    Object? loginFailure = freezed,
    Object? verifyOtpFailure = freezed,
    Object? upFileImageFailure = freezed,
    Object? upDateProfileFailure = freezed,
    Object? deviceRegistrationFailure = freezed,
    Object? userInfoModel = freezed,
  }) {
    return _then(_value.copyWith(
      agree1: null == agree1
          ? _value.agree1
          : agree1 // ignore: cast_nullable_to_non_nullable
              as bool,
      agree2: null == agree2
          ? _value.agree2
          : agree2 // ignore: cast_nullable_to_non_nullable
              as bool,
      agree3: null == agree3
          ? _value.agree3
          : agree3 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      loginSuccess: null == loginSuccess
          ? _value.loginSuccess
          : loginSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      verifySuccess: null == verifySuccess
          ? _value.verifySuccess
          : verifySuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isFirstLogin: null == isFirstLogin
          ? _value.isFirstLogin
          : isFirstLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdateprofileSuccess: null == isUpdateprofileSuccess
          ? _value.isUpdateprofileSuccess
          : isUpdateprofileSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceRegistered: null == isDeviceRegistered
          ? _value.isDeviceRegistered
          : isDeviceRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      loginFailure: freezed == loginFailure
          ? _value.loginFailure
          : loginFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      verifyOtpFailure: freezed == verifyOtpFailure
          ? _value.verifyOtpFailure
          : verifyOtpFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      upDateProfileFailure: freezed == upDateProfileFailure
          ? _value.upDateProfileFailure
          : upDateProfileFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceRegistrationFailure: freezed == deviceRegistrationFailure
          ? _value.deviceRegistrationFailure
          : deviceRegistrationFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      userInfoModel: freezed == userInfoModel
          ? _value.userInfoModel
          : userInfoModel // ignore: cast_nullable_to_non_nullable
              as UserInfoModel?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthStateImplCopyWith<$Res>
    implements $AuthStateCopyWith<$Res> {
  factory _$$AuthStateImplCopyWith(
          _$AuthStateImpl value, $Res Function(_$AuthStateImpl) then) =
      __$$AuthStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool agree1,
      bool agree2,
      bool agree3,
      bool isLoading,
      bool isUploadingImage,
      bool loginSuccess,
      bool verifySuccess,
      bool isFirstLogin,
      bool isUpdateprofileSuccess,
      bool isDeviceRegistered,
      String? urlImage,
      String? loginFailure,
      String? verifyOtpFailure,
      String? upFileImageFailure,
      String? upDateProfileFailure,
      String? deviceRegistrationFailure,
      UserInfoModel? userInfoModel});
}

/// @nodoc
class __$$AuthStateImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$AuthStateImpl>
    implements _$$AuthStateImplCopyWith<$Res> {
  __$$AuthStateImplCopyWithImpl(
      _$AuthStateImpl _value, $Res Function(_$AuthStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? agree1 = null,
    Object? agree2 = null,
    Object? agree3 = null,
    Object? isLoading = null,
    Object? isUploadingImage = null,
    Object? loginSuccess = null,
    Object? verifySuccess = null,
    Object? isFirstLogin = null,
    Object? isUpdateprofileSuccess = null,
    Object? isDeviceRegistered = null,
    Object? urlImage = freezed,
    Object? loginFailure = freezed,
    Object? verifyOtpFailure = freezed,
    Object? upFileImageFailure = freezed,
    Object? upDateProfileFailure = freezed,
    Object? deviceRegistrationFailure = freezed,
    Object? userInfoModel = freezed,
  }) {
    return _then(_$AuthStateImpl(
      agree1: null == agree1
          ? _value.agree1
          : agree1 // ignore: cast_nullable_to_non_nullable
              as bool,
      agree2: null == agree2
          ? _value.agree2
          : agree2 // ignore: cast_nullable_to_non_nullable
              as bool,
      agree3: null == agree3
          ? _value.agree3
          : agree3 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      loginSuccess: null == loginSuccess
          ? _value.loginSuccess
          : loginSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      verifySuccess: null == verifySuccess
          ? _value.verifySuccess
          : verifySuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isFirstLogin: null == isFirstLogin
          ? _value.isFirstLogin
          : isFirstLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdateprofileSuccess: null == isUpdateprofileSuccess
          ? _value.isUpdateprofileSuccess
          : isUpdateprofileSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceRegistered: null == isDeviceRegistered
          ? _value.isDeviceRegistered
          : isDeviceRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      loginFailure: freezed == loginFailure
          ? _value.loginFailure
          : loginFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      verifyOtpFailure: freezed == verifyOtpFailure
          ? _value.verifyOtpFailure
          : verifyOtpFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      upDateProfileFailure: freezed == upDateProfileFailure
          ? _value.upDateProfileFailure
          : upDateProfileFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceRegistrationFailure: freezed == deviceRegistrationFailure
          ? _value.deviceRegistrationFailure
          : deviceRegistrationFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      userInfoModel: freezed == userInfoModel
          ? _value.userInfoModel
          : userInfoModel // ignore: cast_nullable_to_non_nullable
              as UserInfoModel?,
    ));
  }
}

/// @nodoc

class _$AuthStateImpl implements _AuthState {
  const _$AuthStateImpl(
      {this.agree1 = false,
      this.agree2 = false,
      this.agree3 = false,
      this.isLoading = false,
      this.isUploadingImage = false,
      this.loginSuccess = false,
      this.verifySuccess = false,
      this.isFirstLogin = false,
      this.isUpdateprofileSuccess = false,
      this.isDeviceRegistered = false,
      this.urlImage,
      this.loginFailure,
      this.verifyOtpFailure,
      this.upFileImageFailure,
      this.upDateProfileFailure,
      this.deviceRegistrationFailure,
      this.userInfoModel});

  @override
  @JsonKey()
  final bool agree1;
  @override
  @JsonKey()
  final bool agree2;
  @override
  @JsonKey()
  final bool agree3;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isUploadingImage;
  @override
  @JsonKey()
  final bool loginSuccess;
  @override
  @JsonKey()
  final bool verifySuccess;
  @override
  @JsonKey()
  final bool isFirstLogin;
  @override
  @JsonKey()
  final bool isUpdateprofileSuccess;
  @override
  @JsonKey()
  final bool isDeviceRegistered;
  @override
  final String? urlImage;
  @override
  final String? loginFailure;
  @override
  final String? verifyOtpFailure;
  @override
  final String? upFileImageFailure;
  @override
  final String? upDateProfileFailure;
  @override
  final String? deviceRegistrationFailure;
  @override
  final UserInfoModel? userInfoModel;

  @override
  String toString() {
    return 'AuthState(agree1: $agree1, agree2: $agree2, agree3: $agree3, isLoading: $isLoading, isUploadingImage: $isUploadingImage, loginSuccess: $loginSuccess, verifySuccess: $verifySuccess, isFirstLogin: $isFirstLogin, isUpdateprofileSuccess: $isUpdateprofileSuccess, isDeviceRegistered: $isDeviceRegistered, urlImage: $urlImage, loginFailure: $loginFailure, verifyOtpFailure: $verifyOtpFailure, upFileImageFailure: $upFileImageFailure, upDateProfileFailure: $upDateProfileFailure, deviceRegistrationFailure: $deviceRegistrationFailure, userInfoModel: $userInfoModel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthStateImpl &&
            (identical(other.agree1, agree1) || other.agree1 == agree1) &&
            (identical(other.agree2, agree2) || other.agree2 == agree2) &&
            (identical(other.agree3, agree3) || other.agree3 == agree3) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isUploadingImage, isUploadingImage) ||
                other.isUploadingImage == isUploadingImage) &&
            (identical(other.loginSuccess, loginSuccess) ||
                other.loginSuccess == loginSuccess) &&
            (identical(other.verifySuccess, verifySuccess) ||
                other.verifySuccess == verifySuccess) &&
            (identical(other.isFirstLogin, isFirstLogin) ||
                other.isFirstLogin == isFirstLogin) &&
            (identical(other.isUpdateprofileSuccess, isUpdateprofileSuccess) ||
                other.isUpdateprofileSuccess == isUpdateprofileSuccess) &&
            (identical(other.isDeviceRegistered, isDeviceRegistered) ||
                other.isDeviceRegistered == isDeviceRegistered) &&
            (identical(other.urlImage, urlImage) ||
                other.urlImage == urlImage) &&
            (identical(other.loginFailure, loginFailure) ||
                other.loginFailure == loginFailure) &&
            (identical(other.verifyOtpFailure, verifyOtpFailure) ||
                other.verifyOtpFailure == verifyOtpFailure) &&
            (identical(other.upFileImageFailure, upFileImageFailure) ||
                other.upFileImageFailure == upFileImageFailure) &&
            (identical(other.upDateProfileFailure, upDateProfileFailure) ||
                other.upDateProfileFailure == upDateProfileFailure) &&
            (identical(other.deviceRegistrationFailure,
                    deviceRegistrationFailure) ||
                other.deviceRegistrationFailure == deviceRegistrationFailure) &&
            (identical(other.userInfoModel, userInfoModel) ||
                other.userInfoModel == userInfoModel));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      agree1,
      agree2,
      agree3,
      isLoading,
      isUploadingImage,
      loginSuccess,
      verifySuccess,
      isFirstLogin,
      isUpdateprofileSuccess,
      isDeviceRegistered,
      urlImage,
      loginFailure,
      verifyOtpFailure,
      upFileImageFailure,
      upDateProfileFailure,
      deviceRegistrationFailure,
      userInfoModel);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthStateImplCopyWith<_$AuthStateImpl> get copyWith =>
      __$$AuthStateImplCopyWithImpl<_$AuthStateImpl>(this, _$identity);
}

abstract class _AuthState implements AuthState {
  const factory _AuthState(
      {final bool agree1,
      final bool agree2,
      final bool agree3,
      final bool isLoading,
      final bool isUploadingImage,
      final bool loginSuccess,
      final bool verifySuccess,
      final bool isFirstLogin,
      final bool isUpdateprofileSuccess,
      final bool isDeviceRegistered,
      final String? urlImage,
      final String? loginFailure,
      final String? verifyOtpFailure,
      final String? upFileImageFailure,
      final String? upDateProfileFailure,
      final String? deviceRegistrationFailure,
      final UserInfoModel? userInfoModel}) = _$AuthStateImpl;

  @override
  bool get agree1;
  @override
  bool get agree2;
  @override
  bool get agree3;
  @override
  bool get isLoading;
  @override
  bool get isUploadingImage;
  @override
  bool get loginSuccess;
  @override
  bool get verifySuccess;
  @override
  bool get isFirstLogin;
  @override
  bool get isUpdateprofileSuccess;
  @override
  bool get isDeviceRegistered;
  @override
  String? get urlImage;
  @override
  String? get loginFailure;
  @override
  String? get verifyOtpFailure;
  @override
  String? get upFileImageFailure;
  @override
  String? get upDateProfileFailure;
  @override
  String? get deviceRegistrationFailure;
  @override
  UserInfoModel? get userInfoModel;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthStateImplCopyWith<_$AuthStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
