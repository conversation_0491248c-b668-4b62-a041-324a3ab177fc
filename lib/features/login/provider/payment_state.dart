import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/auth/payment_info_model.dart';

part 'payment_state.freezed.dart';

@freezed
class PaymentState with _$PaymentState {
  const factory PaymentState({
    @Default(false) bool isLoading,
    @Default(false) bool isDefaultCardLoading,
    @Default(false) bool isDeletingCard,
    @Default([]) List<PaymentInfoModel>? paymentInfos,
    @Default(false) bool changeDefaultCardSuccess,
    String? error,
  }) = _PaymentState;
}
