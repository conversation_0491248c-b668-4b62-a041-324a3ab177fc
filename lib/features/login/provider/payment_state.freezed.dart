// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PaymentState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isDefaultCardLoading => throw _privateConstructorUsedError;
  bool get isDeletingCard => throw _privateConstructorUsedError;
  List<PaymentInfoModel>? get paymentInfos =>
      throw _privateConstructorUsedError;
  bool get changeDefaultCardSuccess => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of PaymentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentStateCopyWith<PaymentState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentStateCopyWith<$Res> {
  factory $PaymentStateCopyWith(
          PaymentState value, $Res Function(PaymentState) then) =
      _$PaymentStateCopyWithImpl<$Res, PaymentState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isDefaultCardLoading,
      bool isDeletingCard,
      List<PaymentInfoModel>? paymentInfos,
      bool changeDefaultCardSuccess,
      String? error});
}

/// @nodoc
class _$PaymentStateCopyWithImpl<$Res, $Val extends PaymentState>
    implements $PaymentStateCopyWith<$Res> {
  _$PaymentStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isDefaultCardLoading = null,
    Object? isDeletingCard = null,
    Object? paymentInfos = freezed,
    Object? changeDefaultCardSuccess = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isDefaultCardLoading: null == isDefaultCardLoading
          ? _value.isDefaultCardLoading
          : isDefaultCardLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeletingCard: null == isDeletingCard
          ? _value.isDeletingCard
          : isDeletingCard // ignore: cast_nullable_to_non_nullable
              as bool,
      paymentInfos: freezed == paymentInfos
          ? _value.paymentInfos
          : paymentInfos // ignore: cast_nullable_to_non_nullable
              as List<PaymentInfoModel>?,
      changeDefaultCardSuccess: null == changeDefaultCardSuccess
          ? _value.changeDefaultCardSuccess
          : changeDefaultCardSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaymentStateImplCopyWith<$Res>
    implements $PaymentStateCopyWith<$Res> {
  factory _$$PaymentStateImplCopyWith(
          _$PaymentStateImpl value, $Res Function(_$PaymentStateImpl) then) =
      __$$PaymentStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isDefaultCardLoading,
      bool isDeletingCard,
      List<PaymentInfoModel>? paymentInfos,
      bool changeDefaultCardSuccess,
      String? error});
}

/// @nodoc
class __$$PaymentStateImplCopyWithImpl<$Res>
    extends _$PaymentStateCopyWithImpl<$Res, _$PaymentStateImpl>
    implements _$$PaymentStateImplCopyWith<$Res> {
  __$$PaymentStateImplCopyWithImpl(
      _$PaymentStateImpl _value, $Res Function(_$PaymentStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isDefaultCardLoading = null,
    Object? isDeletingCard = null,
    Object? paymentInfos = freezed,
    Object? changeDefaultCardSuccess = null,
    Object? error = freezed,
  }) {
    return _then(_$PaymentStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isDefaultCardLoading: null == isDefaultCardLoading
          ? _value.isDefaultCardLoading
          : isDefaultCardLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeletingCard: null == isDeletingCard
          ? _value.isDeletingCard
          : isDeletingCard // ignore: cast_nullable_to_non_nullable
              as bool,
      paymentInfos: freezed == paymentInfos
          ? _value._paymentInfos
          : paymentInfos // ignore: cast_nullable_to_non_nullable
              as List<PaymentInfoModel>?,
      changeDefaultCardSuccess: null == changeDefaultCardSuccess
          ? _value.changeDefaultCardSuccess
          : changeDefaultCardSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$PaymentStateImpl implements _PaymentState {
  const _$PaymentStateImpl(
      {this.isLoading = false,
      this.isDefaultCardLoading = false,
      this.isDeletingCard = false,
      final List<PaymentInfoModel>? paymentInfos = const [],
      this.changeDefaultCardSuccess = false,
      this.error})
      : _paymentInfos = paymentInfos;

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isDefaultCardLoading;
  @override
  @JsonKey()
  final bool isDeletingCard;
  final List<PaymentInfoModel>? _paymentInfos;
  @override
  @JsonKey()
  List<PaymentInfoModel>? get paymentInfos {
    final value = _paymentInfos;
    if (value == null) return null;
    if (_paymentInfos is EqualUnmodifiableListView) return _paymentInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final bool changeDefaultCardSuccess;
  @override
  final String? error;

  @override
  String toString() {
    return 'PaymentState(isLoading: $isLoading, isDefaultCardLoading: $isDefaultCardLoading, isDeletingCard: $isDeletingCard, paymentInfos: $paymentInfos, changeDefaultCardSuccess: $changeDefaultCardSuccess, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isDefaultCardLoading, isDefaultCardLoading) ||
                other.isDefaultCardLoading == isDefaultCardLoading) &&
            (identical(other.isDeletingCard, isDeletingCard) ||
                other.isDeletingCard == isDeletingCard) &&
            const DeepCollectionEquality()
                .equals(other._paymentInfos, _paymentInfos) &&
            (identical(
                    other.changeDefaultCardSuccess, changeDefaultCardSuccess) ||
                other.changeDefaultCardSuccess == changeDefaultCardSuccess) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isDefaultCardLoading,
      isDeletingCard,
      const DeepCollectionEquality().hash(_paymentInfos),
      changeDefaultCardSuccess,
      error);

  /// Create a copy of PaymentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentStateImplCopyWith<_$PaymentStateImpl> get copyWith =>
      __$$PaymentStateImplCopyWithImpl<_$PaymentStateImpl>(this, _$identity);
}

abstract class _PaymentState implements PaymentState {
  const factory _PaymentState(
      {final bool isLoading,
      final bool isDefaultCardLoading,
      final bool isDeletingCard,
      final List<PaymentInfoModel>? paymentInfos,
      final bool changeDefaultCardSuccess,
      final String? error}) = _$PaymentStateImpl;

  @override
  bool get isLoading;
  @override
  bool get isDefaultCardLoading;
  @override
  bool get isDeletingCard;
  @override
  List<PaymentInfoModel>? get paymentInfos;
  @override
  bool get changeDefaultCardSuccess;
  @override
  String? get error;

  /// Create a copy of PaymentState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentStateImplCopyWith<_$PaymentStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
