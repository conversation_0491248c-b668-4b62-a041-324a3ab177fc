// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'choose_seller_product_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$chooseSellerProductProviderHash() =>
    r'f0bb3546550f78c873e1071ebc771927f520c30e';

/// See also [ChooseSellerProductProvider].
@ProviderFor(ChooseSellerProductProvider)
final chooseSellerProductProviderProvider = AsyncNotifierProvider<
    ChooseSellerProductProvider, ChooseSellerProductState>.internal(
  ChooseSellerProductProvider.new,
  name: r'chooseSellerProductProviderProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$chooseSellerProductProviderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ChooseSellerProductProvider = AsyncNotifier<ChooseSellerProductState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
