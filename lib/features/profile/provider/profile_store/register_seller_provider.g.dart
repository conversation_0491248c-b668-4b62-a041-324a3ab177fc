// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_seller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$registerSellerHash() => r'3ae369b89935e62a3c394eab9292dc1897f8d771';

/// See also [RegisterSeller].
@ProviderFor(RegisterSeller)
final registerSellerProvider =
    NotifierProvider<RegisterSeller, AsyncValue<RegisterSellerState>>.internal(
  RegisterSeller.new,
  name: r'registerSellerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$registerSellerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RegisterSeller = Notifier<AsyncValue<RegisterSellerState>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
