import 'package:freezed_annotation/freezed_annotation.dart';
 
part 'register_seller_state.freezed.dart';

@freezed
class RegisterSellerState with _$RegisterSellerState {
  const factory RegisterSellerState({
    @Default(false) bool isLoading,
    @Default(false) bool isRegisterSellerStateSuccess,
    String? urlImage,
    String? registerSellerFailure,
    @Default(false) bool isUploadingImage,
  }) = _RegisterSellerState;
}
