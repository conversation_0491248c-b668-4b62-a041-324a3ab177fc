import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/auth/auth_repository.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/model/request/store/register_seller_request.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'register_seller_state.dart';

part 'register_seller_provider.g.dart';

@Riverpod(keepAlive: true)
class RegisterSeller extends _$RegisterSeller {
  late final AuthRepository authRepo;
  late final StoreRepository storeRepo;

  @override
  AsyncValue<RegisterSellerState> build() {
    authRepo = ref.read(getAuthRepositoryProvider);
    storeRepo = ref.read(getStoreRepositoryProvider);
    return const AsyncValue.data(RegisterSellerState());
  }

  Future<void> registerSeller(RegisterSellerRequest request, int userID) async {
    state = state.whenData((prevState) => prevState.copyWith(
          isLoading: true,
          isRegisterSellerStateSuccess: false,
          registerSellerFailure: null,
        ));
    try {
      final urlImage = state.value?.urlImage;
      // const urlImage =
      //     "https://kitemite-api.mystg-env.com/storage/tmp/67da58c14a135.jpg";
      request = request.copyWith(img: urlImage);
      await storeRepo.userRegisterSeller(userID, request);
      // await storeRepo.updateStore(userID, 3, request);
      state = state.whenData((prevState) => prevState.copyWith(
          isLoading: false, isRegisterSellerStateSuccess: true));
    } on AppFailure catch (e) {
      state = state.whenData((prevState) => prevState.copyWith(
            isLoading: false,
            registerSellerFailure: e.message,
          ));
    }
  }

  Future<void> updateSeller(
      RegisterSellerRequest request, int userID, int storeId) async {
    state = state.whenData((prevState) => prevState.copyWith(
          isLoading: true,
          isRegisterSellerStateSuccess: false,
          registerSellerFailure: null,
        ));
    try {
      final urlImage = state.value?.urlImage;
      request = request.copyWith(img: urlImage);
      await storeRepo.updateStore(userID, storeId, request);
      state = state.whenData((prevState) => prevState.copyWith(
          isLoading: false, isRegisterSellerStateSuccess: true));
    } on AppFailure catch (e) {
      state = state.whenData((prevState) => prevState.copyWith(
            isLoading: false,
            registerSellerFailure: e.message,
          ));
    }
  }

  Future<void> upFileImage(String imageBase64) async {
    state = state.whenData((prevState) => prevState.copyWith(
          isUploadingImage: true,
          registerSellerFailure: null,
        ));
    try {
      final result = await authRepo.upLoadFile(base64: imageBase64);
      state = state.whenData((prevState) => prevState.copyWith(
            urlImage: result.links?.first ?? "",
            isLoading: false,
            isUploadingImage: false,
          ));
    } on AppFailure catch (e) {
      state = state.whenData((prevState) => prevState.copyWith(
            isLoading: false,
            registerSellerFailure: e.message,
          ));
    }
  }

  Future<void> upDateImageAvatar(String imageURL) async {
    state = state.whenData((prevState) => prevState.copyWith(
          urlImage: imageURL,
        ));
  }

  void reset() {
    state = const AsyncValue.data(RegisterSellerState());
  }
}
