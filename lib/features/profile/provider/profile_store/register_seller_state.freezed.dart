// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_seller_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RegisterSellerState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isRegisterSellerStateSuccess => throw _privateConstructorUsedError;
  String? get urlImage => throw _privateConstructorUsedError;
  String? get registerSellerFailure => throw _privateConstructorUsedError;
  bool get isUploadingImage => throw _privateConstructorUsedError;

  /// Create a copy of RegisterSellerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RegisterSellerStateCopyWith<RegisterSellerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterSellerStateCopyWith<$Res> {
  factory $RegisterSellerStateCopyWith(
          RegisterSellerState value, $Res Function(RegisterSellerState) then) =
      _$RegisterSellerStateCopyWithImpl<$Res, RegisterSellerState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isRegisterSellerStateSuccess,
      String? urlImage,
      String? registerSellerFailure,
      bool isUploadingImage});
}

/// @nodoc
class _$RegisterSellerStateCopyWithImpl<$Res, $Val extends RegisterSellerState>
    implements $RegisterSellerStateCopyWith<$Res> {
  _$RegisterSellerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RegisterSellerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isRegisterSellerStateSuccess = null,
    Object? urlImage = freezed,
    Object? registerSellerFailure = freezed,
    Object? isUploadingImage = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRegisterSellerStateSuccess: null == isRegisterSellerStateSuccess
          ? _value.isRegisterSellerStateSuccess
          : isRegisterSellerStateSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      registerSellerFailure: freezed == registerSellerFailure
          ? _value.registerSellerFailure
          : registerSellerFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegisterSellerStateImplCopyWith<$Res>
    implements $RegisterSellerStateCopyWith<$Res> {
  factory _$$RegisterSellerStateImplCopyWith(_$RegisterSellerStateImpl value,
          $Res Function(_$RegisterSellerStateImpl) then) =
      __$$RegisterSellerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isRegisterSellerStateSuccess,
      String? urlImage,
      String? registerSellerFailure,
      bool isUploadingImage});
}

/// @nodoc
class __$$RegisterSellerStateImplCopyWithImpl<$Res>
    extends _$RegisterSellerStateCopyWithImpl<$Res, _$RegisterSellerStateImpl>
    implements _$$RegisterSellerStateImplCopyWith<$Res> {
  __$$RegisterSellerStateImplCopyWithImpl(_$RegisterSellerStateImpl _value,
      $Res Function(_$RegisterSellerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegisterSellerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isRegisterSellerStateSuccess = null,
    Object? urlImage = freezed,
    Object? registerSellerFailure = freezed,
    Object? isUploadingImage = null,
  }) {
    return _then(_$RegisterSellerStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRegisterSellerStateSuccess: null == isRegisterSellerStateSuccess
          ? _value.isRegisterSellerStateSuccess
          : isRegisterSellerStateSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      registerSellerFailure: freezed == registerSellerFailure
          ? _value.registerSellerFailure
          : registerSellerFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$RegisterSellerStateImpl implements _RegisterSellerState {
  const _$RegisterSellerStateImpl(
      {this.isLoading = false,
      this.isRegisterSellerStateSuccess = false,
      this.urlImage,
      this.registerSellerFailure,
      this.isUploadingImage = false});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isRegisterSellerStateSuccess;
  @override
  final String? urlImage;
  @override
  final String? registerSellerFailure;
  @override
  @JsonKey()
  final bool isUploadingImage;

  @override
  String toString() {
    return 'RegisterSellerState(isLoading: $isLoading, isRegisterSellerStateSuccess: $isRegisterSellerStateSuccess, urlImage: $urlImage, registerSellerFailure: $registerSellerFailure, isUploadingImage: $isUploadingImage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterSellerStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isRegisterSellerStateSuccess,
                    isRegisterSellerStateSuccess) ||
                other.isRegisterSellerStateSuccess ==
                    isRegisterSellerStateSuccess) &&
            (identical(other.urlImage, urlImage) ||
                other.urlImage == urlImage) &&
            (identical(other.registerSellerFailure, registerSellerFailure) ||
                other.registerSellerFailure == registerSellerFailure) &&
            (identical(other.isUploadingImage, isUploadingImage) ||
                other.isUploadingImage == isUploadingImage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isRegisterSellerStateSuccess,
      urlImage,
      registerSellerFailure,
      isUploadingImage);

  /// Create a copy of RegisterSellerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterSellerStateImplCopyWith<_$RegisterSellerStateImpl> get copyWith =>
      __$$RegisterSellerStateImplCopyWithImpl<_$RegisterSellerStateImpl>(
          this, _$identity);
}

abstract class _RegisterSellerState implements RegisterSellerState {
  const factory _RegisterSellerState(
      {final bool isLoading,
      final bool isRegisterSellerStateSuccess,
      final String? urlImage,
      final String? registerSellerFailure,
      final bool isUploadingImage}) = _$RegisterSellerStateImpl;

  @override
  bool get isLoading;
  @override
  bool get isRegisterSellerStateSuccess;
  @override
  String? get urlImage;
  @override
  String? get registerSellerFailure;
  @override
  bool get isUploadingImage;

  /// Create a copy of RegisterSellerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterSellerStateImplCopyWith<_$RegisterSellerStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
