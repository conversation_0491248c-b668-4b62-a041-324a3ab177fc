import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/auth/auth_repository.dart';
import 'package:kitemite_app/core/utils/app_preferences.dart';
import 'package:kitemite_app/core/utils/app_version_service.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'profile_provider.g.dart';

@Riverpod(keepAlive: true)
class ProfileProvider extends _$ProfileProvider {
  @override
  FutureOr<ProfileState> build() async {
    return const ProfileState(profile: null);
  }

  Future<void> loadProfile() async {
    state = const AsyncValue.loading();
    try {
      final result = await ref.read(getAuthRepositoryProvider).getMe();
      state = AsyncData(ProfileState(profile: result.data));
    } on AppFailure catch (e) {
      if (e.code != 401) {
        state = AsyncError(e.message, StackTrace.current);
      }
    }
  }

  Future<void> signOut() async {
    state = const AsyncValue.loading();
    try {
      String? deviceId = await getDeviceId();
      await ref
          .read(getAuthRepositoryProvider)
          .logout(deviceID: deviceId ?? "");

      await AppPreferences.remove(AppConstants.tokenKey);
      await AppPreferences.remove(AppConstants.accountModel);

      state = const AsyncData(ProfileState(profile: null));
    } catch (error) {
      state = AsyncError(error, StackTrace.current);
    }
  }

  Future<void> destroyUser(int id) async {
    state = const AsyncValue.loading();
    try {
      await ref.read(getAuthRepositoryProvider).destroyUser(id: id);
      await AppPreferences.remove(AppConstants.tokenKey);
      await AppPreferences.remove(AppConstants.accountModel);
      state = const AsyncData(ProfileState(profile: null));
    } catch (error) {
      state = AsyncError(error, StackTrace.current);
    }
  }

  Future<void> refreshProfile() async {
    state = const AsyncValue.loading();
    try {
      final profile = await ref.read(getAuthRepositoryProvider).getMe();
      state = AsyncData(ProfileState(profile: profile.data));
    } catch (error) {
      state = AsyncError(error, StackTrace.current);
    }
  }
}
