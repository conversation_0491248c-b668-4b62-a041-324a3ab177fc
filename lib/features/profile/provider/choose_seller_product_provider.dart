import 'package:equatable/equatable.dart';
import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/model/request/store/register_seller_request.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'choose_seller_product_provider.g.dart';

class ChooseSellerProductState extends Equatable {
  const ChooseSellerProductState(this.eodStatus, {this.closeManual = false});

  final bool eodStatus;

  final bool closeManual;

  ChooseSellerProductState copyWith({bool? eodStatus, bool? closeManual}) {
    return ChooseSellerProductState(
      eodStatus ?? this.eodStatus,
      closeManual: closeManual ?? this.closeManual,
    );
  }

  @override
  List<Object?> get props => [eodStatus, closeManual];
}

@Riverpod(keepAlive: true)
class ChooseSellerProductProvider extends _$ChooseSellerProductProvider {
  late final StoreRepository repo;

  @override
  FutureOr<ChooseSellerProductState> build() async {
    repo = ref.read(getStoreRepositoryProvider);
    return const ChooseSellerProductState(true);
  }

  Future<void> bulkUpdate(WarehousesBulkUpdateRequest request) async {
    state = const AsyncValue.loading();
    try {
     final _ = await repo.bulkUpdate(request);
      state =
          const AsyncData(ChooseSellerProductState(true, closeManual: true));
    } on AppFailure catch (e) {
      if (e.code != 401) {
        state = AsyncError(e.message, StackTrace.current);
      }
    }
  }

  void resetCloseManual() {
    state = const AsyncData(ChooseSellerProductState(true, closeManual: false));
  }
}
