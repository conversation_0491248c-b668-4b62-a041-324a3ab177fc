// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mode_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$modeNotifierHash() => r'3847d861200d742b66b6ca3912569017189d65bc';

/// See also [ModeNotifier].
@ProviderFor(ModeNotifier)
final modeNotifierProvider = NotifierProvider<ModeNotifier, ModeState>.internal(
  ModeNotifier.new,
  name: r'modeNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$modeNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ModeNotifier = Notifier<ModeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
