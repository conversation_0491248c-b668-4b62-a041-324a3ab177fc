// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mode_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ModeState {
  ModeAccount get mode => throw _privateConstructorUsedError;

  /// Create a copy of ModeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ModeStateCopyWith<ModeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ModeStateCopyWith<$Res> {
  factory $ModeStateCopyWith(ModeState value, $Res Function(ModeState) then) =
      _$ModeStateCopyWithImpl<$Res, ModeState>;
  @useResult
  $Res call({ModeAccount mode});
}

/// @nodoc
class _$ModeStateCopyWithImpl<$Res, $Val extends ModeState>
    implements $ModeStateCopyWith<$Res> {
  _$ModeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ModeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mode = null,
  }) {
    return _then(_value.copyWith(
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as ModeAccount,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ModeStateImplCopyWith<$Res>
    implements $ModeStateCopyWith<$Res> {
  factory _$$ModeStateImplCopyWith(
          _$ModeStateImpl value, $Res Function(_$ModeStateImpl) then) =
      __$$ModeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ModeAccount mode});
}

/// @nodoc
class __$$ModeStateImplCopyWithImpl<$Res>
    extends _$ModeStateCopyWithImpl<$Res, _$ModeStateImpl>
    implements _$$ModeStateImplCopyWith<$Res> {
  __$$ModeStateImplCopyWithImpl(
      _$ModeStateImpl _value, $Res Function(_$ModeStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ModeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mode = null,
  }) {
    return _then(_$ModeStateImpl(
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as ModeAccount,
    ));
  }
}

/// @nodoc

class _$ModeStateImpl implements _ModeState {
  const _$ModeStateImpl({this.mode = ModeAccount.personal});

  @override
  @JsonKey()
  final ModeAccount mode;

  @override
  String toString() {
    return 'ModeState(mode: $mode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ModeStateImpl &&
            (identical(other.mode, mode) || other.mode == mode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mode);

  /// Create a copy of ModeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ModeStateImplCopyWith<_$ModeStateImpl> get copyWith =>
      __$$ModeStateImplCopyWithImpl<_$ModeStateImpl>(this, _$identity);
}

abstract class _ModeState implements ModeState {
  const factory _ModeState({final ModeAccount mode}) = _$ModeStateImpl;

  @override
  ModeAccount get mode;

  /// Create a copy of ModeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ModeStateImplCopyWith<_$ModeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
