import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/utils/app_preferences.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'mode_state.dart';

part 'mode_provider.g.dart';

@Riverpod(keepAlive: true)
class ModeNotifier extends _$ModeNotifier {
  @override
  ModeState build() {
    return ModeState.initial();
  }

  Future<void> loadModeFromPrefs() async {
    if (state.mode == ModeAccount.guest) {
      return;
    }
    final savedMode = AppPreferences.getString(AppConstants.accountModel);
    state = state.copyWith(
      mode: savedMode == "seller" ? ModeAccount.seller : ModeAccount.personal,
    );
  }

  Future<void> setMode(ModeAccount newMode) async {
    await AppPreferences.saveString(AppConstants.accountModel,
        newMode == ModeAccount.seller ? "seller" : "personal");
    state = state.copyWith(mode: newMode);
  }

  void setModeGuest() {
    state = state.copyWith(mode: ModeAccount.guest);
  }

  void resetState() {
    state = ModeState.initial();
  }
}
