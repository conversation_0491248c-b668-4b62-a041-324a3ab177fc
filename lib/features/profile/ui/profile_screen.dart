import 'package:flutter/material.dart';
import 'package:flutter_custom_tabs/flutter_custom_tabs.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/networking/http_provicer.dart';
import 'package:kitemite_app/core/utils/app_version_service.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/flavors.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:talker_flutter/talker_flutter.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({
    super.key,
  });

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  bool userIsSeller = false;
  String _versionApp = "";
  int _tapCount = 0;

  void _getVersionApp() {
    _versionApp = AppVersionService().currentVersion;
  }

  @override
  void initState() {
    _getVersionApp();
    super.initState();
  }

  Widget _buildMenuItem(IconData icon, String title, {Function()? onTap}) {
    return ListTile(
      visualDensity: const VisualDensity(vertical: -4),
      contentPadding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
      leading: Icon(icon, color: Colors.grey[700], size: 22),
      title: Text(title,
          style: AppTextStyles.regular(16.sp, color: AppColors.textProfile)),
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    final profile = ref.watch(profileProviderProvider).value?.profile;
    userIsSeller = ref.watch(modeNotifierProvider).mode == ModeAccount.seller;
    return BaseScaffold(
      appBar: CustomAppbar.basic(
        widgetTitle: Row(
          children: [
            BaseCachedNetworkImage(
              imageUrl: userIsSeller
                  ? (profile?.store?.img ?? '')
                  : (profile?.img ?? ''),
              width: 40.r,
              height: 40.r,
              borderRadius: BorderRadius.circular(20.r),
              placeholder: CircleAvatar(
                radius: 20.r,
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(20.r),
                    child: Assets.avatarDefault.image()),
              ),
              errorWidget: CircleAvatar(
                radius: 20.r,
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(20.r),
                    child: Assets.avatarDefault.image()),
              ),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    userIsSeller
                        ? profile?.store?.name ?? ""
                        : profile?.username ?? "_",
                    style: AppTextStyles.bold(16.sp,
                        color: AppColors.textPrimary)),
                Text(
                    userIsSeller
                        ? profile?.store?.address ?? ""
                        : profile?.email ?? "_",
                    style: AppTextStyles.medium(12.sp,
                        color: AppColors.textLightSecondary)),
              ],
            ),
          ],
        ),
        centerTitle: false,
        onTap: () {
          context.pop();
        },
      ),
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: EdgeInsets.only(left: 16.w, right: 16.w),
                children: [
                  if (profile?.accountHasRoleSeller == true)
                    UserModeSwitchWidget(
                      profile: profile,
                    ),
                  _buildMenuItem(Icons.person, 'アカウント情報',
                      onTap: () =>
                          context.push(RouterPaths.signup, extra: profile)),
                  _buildMenuItem(Icons.notifications, '通知履歴', onTap: () {
                    context.push(RouterPaths.listNotification);
                  }),
                  _buildMenuItem(Icons.published_with_changes, '売上金受取り',
                      onTap: () {
                    launchUrlCustom(context, AppConstants.publishedWithChanges);
                  }),
                  _buildMenuItem(Icons.support_agent, '事務局へ問い合わせ', onTap: () {
                    launchUrlCustom(context, AppConstants.supportAgent);
                  }),
                  if (profile?.accountHasRoleSeller == false)
                    _buildMenuItem(Icons.storefront, '出展登録', onTap: () {
                      context.push(RouterPaths.registerSeller);
                    }),
                  if (userIsSeller)
                    _buildMenuItem(Icons.storefront, '店舗情報更新', onTap: () {
                      context.push(RouterPaths.registerSeller,
                          extra: profile?.store);
                    }),
                  const Divider(),
                  _buildMenuItem(Icons.verified_user_sharp, '利用規約', onTap: () {
                    launchUrlCustom(context, AppConstants.termsUrl);
                  }),
                  _buildMenuItem(Icons.lock, 'プライバシーポリシー', onTap: () {
                    launchUrlCustom(context, AppConstants.policyUrl);
                  }),
                  const Divider(),
                  _buildMenuItem(Icons.logout_rounded, 'ログアウト',
                      onTap: () => _logout(ref, context)),
                  ListTile(
                    visualDensity: const VisualDensity(vertical: -4),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
                    leading: const Icon(Icons.delete_forever_outlined,
                        color: AppColors.error, size: 22),
                    title: Text('アカウントを削除',
                        style: AppTextStyles.regular(16.sp,
                            color: AppColors.error)),
                    onTap: () => _destroyUser(ref, context),
                  )
                ],
              ),
            ),
            Text("ID: ${profile?.id}",
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary)),
            InkWell(
              onTap: () {
                setState(() {
                  _tapCount++;
                  if (_tapCount >= 5) {
                    _tapCount = 0;
                    // if (F.appFlavor == Flavor.dev) {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => TalkerScreen(talker: talkerApp),
                      ));
                    // }
                  }
                });
              },
              child: Text("App ver: $_versionApp",
                  style: AppTextStyles.regular(14.sp,
                      color: AppColors.textLightSecondary)),
            )
          ],
        ),
      ),
    );
  }

  Future<void> _logout(WidgetRef ref, BuildContext context) async {
    try {
      await ref.read(profileProviderProvider.notifier).signOut();
    } finally {
      if (context.mounted) {
        context.go(RouterPaths.login);
      }
    }
  }

  Future<void> _destroyUser(WidgetRef ref, BuildContext context) async {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'アカウントを削除',
                style: AppTextStyles.bold(18.sp, color: AppColors.textPrimary),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text(
                'アカウント削除すると登録商品も削除されます',
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        side: const BorderSide(color: AppColors.border),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'キャンセル',
                        style: AppTextStyles.bold(14.sp,
                            color: AppColors.textPrimary),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        try {
                          final profile =
                              ref.watch(profileProviderProvider).value?.profile;
                          await ref
                              .read(profileProviderProvider.notifier)
                              .destroyUser(profile?.id ?? 0);
                          if (!context.mounted) return;
                          context.go(RouterPaths.login);
                        } catch (e) {
                          if (!context.mounted) return;
                          context.showErrorSnackBar(e.toString());
                        } finally {
                          context.go(RouterPaths.login);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.error,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        '削除',
                        style: AppTextStyles.bold(14.sp, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class UserModeSwitchWidget extends ConsumerWidget {
  final UserInfoModel? profile;

  const UserModeSwitchWidget({
    super.key,
    required this.profile,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final modeState = ref.watch(modeNotifierProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFBE9).withOpacity(0.7),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          BaseCachedNetworkImage(
            imageUrl: modeState.mode == ModeAccount.seller
                ? profile?.img ?? ""
                : profile?.store?.img ?? "",
            width: 32,
            height: 32,
            borderRadius: BorderRadius.circular(16),
            placeholder: CircleAvatar(
              radius: 16,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Assets.avatarDefault.image(),
              ),
            ),
            errorWidget: CircleAvatar(
              radius: 16,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Assets.avatarDefault.image(),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              modeState.mode == ModeAccount.seller
                  ? profile?.username ?? ""
                  : profile?.store?.name ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: AppTextStyles.bold(14, color: AppColors.textProfile),
            ),
          ),
          GestureDetector(
            onTap: () {
              final newMode = modeState.mode == ModeAccount.personal
                  ? ModeAccount.seller
                  : ModeAccount.personal;

              ref.read(modeNotifierProvider.notifier).setMode(newMode);
              context.showSuccessSnackBar(newMode == ModeAccount.personal
                  ? "購入者メニューに変更しました。"
                  : "販売者メニューに変更しました。");
            },
            child: Row(
              children: [
                Text(
                  modeState.mode == ModeAccount.personal
                      ? "販売者メニュー"
                      : "購入者メニュー",
                  style:
                      AppTextStyles.regular(14, color: AppColors.cempedak100),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.arrow_forward_ios,
                    size: 12, color: AppColors.cempedak100),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

Future<void> _launchUrlInDefaultBrowserOnAndroid(
    BuildContext context, String url) async {
  final theme = Theme.of(context);
  try {
    await launchUrl(
      Uri.parse(url),
      customTabsOptions: CustomTabsOptions(
        colorSchemes: CustomTabsColorSchemes.defaults(
          toolbarColor: theme.colorScheme.surface,
          navigationBarColor: theme.colorScheme.surface,
        ),
        urlBarHidingEnabled: true,
        showTitle: true,
        browser: const CustomTabsBrowserConfiguration(
          prefersDefaultBrowser: true,
        ),
      ),
    );
  } catch (e) {
    debugPrint(e.toString());
  }
}

void launchUrlCustom(BuildContext context, String url) async {
  final theme = Theme.of(context);
  try {
    await launchUrl(
      Uri.parse(url),
      customTabsOptions: CustomTabsOptions(
        colorSchemes: CustomTabsColorSchemes.defaults(
          toolbarColor: theme.colorScheme.surface,
        ),
        shareState: CustomTabsShareState.on,
        urlBarHidingEnabled: true,
        showTitle: true,
        closeButton: CustomTabsCloseButton(
          icon: CustomTabsCloseButtonIcons.back,
        ),
      ),
      safariVCOptions: SafariViewControllerOptions(
        preferredBarTintColor: theme.colorScheme.surface,
        preferredControlTintColor: theme.colorScheme.onSurface,
        barCollapsingEnabled: true,
        dismissButtonStyle: SafariViewControllerDismissButtonStyle.close,
      ),
    );
  } catch (e) {
    // If the URL launch fails, an exception will be thrown. (For example, if no browser app is installed on the Android device.)
    debugPrint(e.toString());
  }
}
