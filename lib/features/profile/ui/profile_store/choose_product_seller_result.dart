import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/features/home/<USER>/home_seller/seller_home_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class ChooseProductSellerResult extends ConsumerWidget {
  const ChooseProductSellerResult({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Container(
        color: AppColors.white,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 38.w),
          child: Column(
            children: [
              SizedBox(height: MediaQuery.of(context).size.height * 0.15),
              Assets.kitemite1920x10802Png.image(width: 137.w, height: 116.h),
              SizedBox(height: 32.h),
              Text("販売を開始しました！",
                  textAlign: TextAlign.center,
                  style:
                      AppTextStyles.bold(18.sp, color: AppColors.textProfile)),
              SizedBox(height: 8.h),
             
              SizedBox(height: 32.h),
             
              
              CommonButton(
                onPressed: () async {
                  await ref
                      .read(profileProviderProvider.notifier)
                      .refreshProfile();
                  ref
                      .read(sellerHomeNotifierProvider.notifier)
                      .loadWarehouses();
                  context.go(RouterPaths.home, extra: true);
                },
                customTitle: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text("終わり",
                        style: AppTextStyles.bold(14.sp,
                            color: AppColors.textPrimary)),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
