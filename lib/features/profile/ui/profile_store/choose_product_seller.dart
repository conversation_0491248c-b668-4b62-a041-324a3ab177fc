import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/home/<USER>/detail_store_seller_provider.dart';
import 'package:kitemite_app/features/home/<USER>/detail_store_seller.dart';
import 'package:kitemite_app/features/profile/provider/choose_seller_product_provider.dart';
import 'package:kitemite_app/model/request/store/register_seller_request.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

final _itemsProvider =
    StateProvider.autoDispose<List<WarehouseProductModel>>((ref) => []);

class ChooseProductSeller extends ConsumerStatefulWidget {
  final CabinetModel cabinet;
  const ChooseProductSeller({super.key, required this.cabinet});

  @override
  ConsumerState<ChooseProductSeller> createState() =>
      _ChooseProductSellerState();
}

class _ChooseProductSellerState extends ConsumerState<ChooseProductSeller> {
  final Set<int> _selectedItems = {};

  @override
  void initState() {
    super.initState();
    // Initialize selected items based on shelf state
    for (var (product as WarehouseProductModel)
        in widget.cabinet.products ?? []) {
      if (product.shelf?.isShelfActive ?? false) {
        _selectedItems.add(product.id!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final warehouse = ref.watch(detailStoreSellerNotifierProvider).warehouse;

    ref.listen(chooseSellerProductProviderProvider, (previous, current) {
      if (current.hasValue && current.value != previous?.value) {
        if (current.value?.closeManual == true) {
          context.push(RouterPaths.chooseProductSellerResult);
          ref
              .read(chooseSellerProductProviderProvider.notifier)
              .resetCloseManual();
        }
      }
      if (current is AsyncError) {
        context.showErrorSnackBar(current.error.toString());
      }
    });

    return BaseScaffold(
      resizeToAvoidBottomInset: true,
      appBar: CustomAppbar(
        automaticallyImplyLeading: false,
        title: "冷凍庫-${widget.cabinet.cabinetCode ?? ""}",
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
            children: (widget.cabinet.products ?? []).map((product) {
          final isSelected = _selectedItems.contains(product.id);
          return _buildSlidableItem(
              isSelected: isSelected,
              key: ValueKey(product.id),
              image: product.img ?? "",
              title: product.name ?? "",
              price: "${product.price?.priceString()}円",
              quantity: product.quantity?.toString() ?? "0",
              expiryDate: product.expirationDate ?? "",
              sheftCode: product.shelf?.shelfCode ?? "",
              onPressed: () {
                setState(() {
                  if (_selectedItems.contains(product.id)) {
                    _selectedItems.remove(product.id);
                    ref.read(_itemsProvider.notifier).state = ref
                        .read(_itemsProvider)
                        .where((item) => item.id != product.id)
                        .toList();
                  } else {
                    _selectedItems.add(product.id!);
                    ref.read(_itemsProvider.notifier).state = [
                      ...ref.read(_itemsProvider),
                      product
                    ];
                  }
                });
              });
        }).toList()),
      ),
      bottom: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Opacity(
                opacity:
                    _selectedItems.length == widget.cabinet.products!.length
                        ? 1
                        : 0.6,
                child: CommonButton(
                  text: "納品完了",
                  onPressed: () {
                    if (_selectedItems.length ==
                        widget.cabinet.products!.length) {
                      final request = WarehousesBulkUpdateRequest(
                          ids: _selectedItems.toList());
                      print("----------------${request.ids}");
                      ref
                          .read(chooseSellerProductProviderProvider.notifier)
                          .bulkUpdate(request);
                    }
                  },
                ),
              ),
            ),
            Center(
              child: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    "キャンセル",
                    style: AppTextStyles.bold(15.sp,
                        color: AppColors.textLightSecondary),
                  )),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  Widget _buildSlidableItem(
      {Key? key,
      required bool isSelected,
      required String image,
      required String title,
      required String price,
      required String quantity,
      required String expiryDate,
      required String sheftCode,
      required VoidCallback onPressed}) {
    return GestureDetector(
      onTap: onPressed,
      child: Row(
        children: [
          Expanded(
            child: ShoppingCartItem(
              image: image,
              title: title,
              price: price,
              quantity: quantity,
              expiryDate: expiryDate,
              sheftCode: sheftCode,
              isShowStatus: true,
              isActive: isSelected,
            ),
          ),
        ],
      ),
    );
  }
}
