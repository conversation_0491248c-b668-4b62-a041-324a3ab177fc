import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/custom_choose_avatar/dotted_circle_painter.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/image_picker/common_image_picker.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/validator_string.dart';
import 'package:kitemite_app/features/profile/provider/profile_store/register_seller_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/features/shopping_cart/ui/widget/shopping_cart_item.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/request/store/register_seller_request.dart';
import 'package:kitemite_app/model/response/store/store_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class ProfileStoreScreen extends ConsumerStatefulWidget {
  const ProfileStoreScreen({super.key, this.storeInfo});
  final StoreModel? storeInfo;

  @override
  _ProfileStoreScreenState createState() => _ProfileStoreScreenState();
}

class _ProfileStoreScreenState extends ConsumerState<ProfileStoreScreen> {
  final _formKey = GlobalKey<FormState>();
  final _companyNameController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _responsiblePersonController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.storeInfo != null) {
      _companyNameController.text = widget.storeInfo?.name ?? '';
      _addressController.text = widget.storeInfo?.address ?? '';
      _phoneController.text = widget.storeInfo?.tel ?? '';
      _emailController.text = widget.storeInfo?.email ?? '';
      _responsiblePersonController.text = widget.storeInfo?.responsible ?? '';

      // Set the image URL in the provider state
      if (widget.storeInfo?.img != null) {
        ref
            .read(registerSellerProvider.notifier)
            .upDateImageAvatar(widget.storeInfo!.img!);
      }
    }
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _responsiblePersonController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      final profileUser = ref.read(profileProviderProvider).value?.profile;
      final request = RegisterSellerRequest(
        name: _companyNameController.text,
        email: _emailController.text,
        tel: _phoneController.text,
        address: _addressController.text,
        responsible: _responsiblePersonController.text,
      );

      if (widget.storeInfo != null) {
        await ref.read(registerSellerProvider.notifier).updateSeller(
              request,
              profileUser?.id ?? 0,
              widget.storeInfo?.id ?? 0,
            );
      } else {
        await ref.read(registerSellerProvider.notifier).registerSeller(
              request,
              profileUser?.id ?? 0,
            );
      }

      final registerSellerState = ref.watch(registerSellerProvider);
      registerSellerState.whenData((state) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (state.registerSellerFailure != null) {
            context.showErrorSnackBar(state.registerSellerFailure ?? "");
          }
          if (state.isRegisterSellerStateSuccess) {
            if (widget.storeInfo != null) {
              context.showSuccessSnackBar("更新しました");
              context.pop();
            } else {
              context.push(RouterPaths.registerSellerResult);
            }
          }
        });
      });
    } else {
      print("フォームが無効です。");
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(registerSellerProvider);
    final authData = authState.maybeWhen(
      data: (data) => data,
      orElse: () => null,
    );
    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: widget.storeInfo != null ? "店舗情報編集" : "出店責任者",
        onTap: () {
          context.pop();
        },
      ),
      body: GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 12),
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  SizedBox(height: 8.h),
                  InputTextField(
                    label: "屋号（会社名）",
                    textController: _companyNameController,
                    validator: (value) =>
                        Validator.validateRequiredField(value, "屋号（会社名）"),
                  ),
                  SizedBox(height: 24.h),
                  InputTextField(
                    label: "住所",
                    textController: _addressController,
                    validator: (value) =>
                        Validator.validateRequiredField(value, "住所"),
                  ),
                  SizedBox(height: 24.h),
                  InputTextField(
                    label: "電話番号",
                    textController: _phoneController,
                    validator: Validator.validatePhone,
                    keyboardType: TextInputType.phone,
                  ),
                  SizedBox(height: 24.h),
                  InputTextField(
                    label: "メールアドレス",
                    textController: _emailController,
                    validator: Validator.validateEmail,
                    keyboardType: TextInputType.emailAddress,
                  ),
                  SizedBox(height: 24.h),
                  InputTextField(
                    label: "責任者",
                    textController: _responsiblePersonController,
                    validator: (value) =>
                        Validator.validateRequiredField(value, "責任者"),
                  ),
                  SizedBox(height: 32.h),
                  DottedCircleWithImage(urlImage: widget.storeInfo?.img),
                  SizedBox(height: 24.h),
                  Text(
                    '最大50MBで、\n*.jpeg, *.jpg, *.png, *.gif\nファイルだけアップロードしてください。',
                    textAlign: TextAlign.center,
                    style: AppTextStyles.regular(12.sp,
                        color: AppColors.textLightSecondary),
                  ),
                  SizedBox(height: 37.h),
                  authData?.isLoading ?? false
                      ? const Center(child: CircularProgressIndicator())
                      : CommonButton(
                          onPressed: () => _submitForm(),
                          text: widget.storeInfo != null ? "更新" : "次へ",
                        ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class DottedCircleWithImage extends ConsumerStatefulWidget {
  const DottedCircleWithImage({super.key, this.urlImage});
  final String? urlImage;

  @override
  _DottedCircleWithImageState createState() => _DottedCircleWithImageState();
}

class _DottedCircleWithImageState extends ConsumerState<DottedCircleWithImage> {
  File? _selectedImage;
  final CommonImagePicker _imagePicker = CommonImagePicker(
    maxSizeInBytes: 50 * 1024 * 1024, // Giới hạn 10MB
    allowedFormats: ['jpg', 'jpeg', 'png', 'gif'],
  );

  void _showImagePickerBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (_) => SafeArea(child: _buildImagePickerOptions(context)),
    );
  }

  Future<void> _pickImage(BuildContext context, ImageSource source) async {
    Navigator.pop(context);
    try {
      final XFile? pickedFile =
          await _imagePicker.pickImage(context, source: source);
      if (pickedFile != null) {
        File imageFile = File(pickedFile.path);
        setState(() {
          _selectedImage = imageFile;
        });

        await _uploadImage(imageFile);
      }
    } catch (e) {
      checkCameraPermission(context);
    }
  }

  Future<void> _uploadImage(File image) async {
    String base64Image = await _imagePicker.convertToBase64(image);
    print("Base64 Image: $base64Image");

    await ref.read(registerSellerProvider.notifier).upFileImage(base64Image);
  }

  Widget _buildImagePickerOptions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.camera_alt),
            title: const Text("写真を撮る"),
            onTap: () => _pickImage(context, ImageSource.camera),
          ),
          ListTile(
            leading: const Icon(Icons.photo_library),
            title: const Text("ライブラリから選択"),
            onTap: () => _pickImage(context, ImageSource.gallery),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double outerCircleSize = 150;
    double padding = 4;
    double innerCircleSize = outerCircleSize - (padding * 2);
    final registerSellerState = ref.watch(registerSellerProvider);

    registerSellerState.whenData((state) {
      if (state.registerSellerFailure != null) {
        context.showErrorSnackBar(state.registerSellerFailure ?? "");
      }
    });

    return GestureDetector(
      onTap: () => _showImagePickerBottomSheet(context),
      child: Stack(
        alignment: Alignment.center,
        children: [
          CustomPaint(
            size: Size(outerCircleSize, outerCircleSize),
            painter: DottedCirclePainter(),
          ),
          Container(
            width: innerCircleSize,
            height: innerCircleSize,
            padding: EdgeInsets.all(padding),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: ClipOval(
              child: registerSellerState.value?.isUploadingImage ?? false
                  ? const Center(child: CircularProgressIndicator())
                  : _selectedImage != null
                      ? Stack(
                          fit: StackFit.expand,
                          children: [
                            Image.file(
                              _selectedImage!,
                              fit: BoxFit.cover,
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.3),
                                shape: BoxShape.circle,
                              ),
                            ),
                          ],
                        )
                      : widget.urlImage != null
                          ? Stack(
                              fit: StackFit.expand,
                              children: [
                                BaseCachedNetworkImage(
                                  imageUrl: widget.urlImage!,
                                  borderRadius: BorderRadius.circular(20.r),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.3),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ],
                            )
                          : Stack(
                              fit: StackFit.expand,
                              children: [
                                Assets.avatarDefault.image(
                                  fit: BoxFit.cover,
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.3),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ],
                            ),
            ),
          ),
          Column(
            children: [
              Icon(
                Icons.add_a_photo,
                color: AppColors.mono0,
                size: 24.sp,
              ),
              SizedBox(height: 8.h),
              Text(
                "ロゴを更新",
                style: AppTextStyles.regular(12.sp, color: AppColors.mono0),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
