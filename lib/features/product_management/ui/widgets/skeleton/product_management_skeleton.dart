import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProductManagementSkeleton extends StatelessWidget {
  const ProductManagementSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
  }

  static Widget buildProductsSkeleton() {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: 3,
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWarehouseHeaderSkeleton(),
            _buildProductsListSkeleton(),
            SizedBox(height: 16.h),
          ],
        );
      },
    );
  }

  static Widget _buildWarehouseHeaderSkeleton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 20,
            width: 200,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 4),
          Container(
            height: 16,
            width: 300,
            color: Colors.grey[300],
          ),
        ],
      ),
    );
  }

  static Widget _buildProductsListSkeleton() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 2,
      padding: EdgeInsets.zero,
      itemBuilder: (context, productIndex) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProductImageSkeleton(),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildProductNameSkeleton(),
                    SizedBox(height: 8.h),
                    _buildProductDatesSkeleton(),
                    const SizedBox(height: 8),
                    _buildProductChipsSkeleton(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Widget _buildProductImageSkeleton() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  static Widget _buildProductNameSkeleton() {
    return Container(
      height: 16,
      width: double.infinity,
      color: Colors.grey[300],
    );
  }

  static Widget _buildProductDatesSkeleton() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 14,
            width: 150,
            color: Colors.grey[300],
          ),
        ),
        Container(
          height: 14,
          width: 100,
          color: Colors.grey[300],
        ),
      ],
    );
  }

  static Widget _buildProductChipsSkeleton() {
    return Row(
      children: [
        Container(
          height: 24,
          width: 100,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        const SizedBox(width: 8),
        Container(
          height: 24,
          width: 80,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(20),
          ),
        ),
      ],
    );
  }

  static Widget buildTemplatesSkeleton() {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: 5,
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildTemplateImageSkeleton(),
              const SizedBox(width: 12),
              _buildTemplateNameSkeleton(),
            ],
          ),
        );
      },
    );
  }

  static Widget _buildTemplateImageSkeleton() {
    return Container(
      width: 84,
      height: 84,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  static Widget _buildTemplateNameSkeleton() {
    return Expanded(
      child: Container(
        height: 20,
        color: Colors.grey[300],
      ),
    );
  }

  static Widget buildHistorySkeleton() {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: 3,
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Container(
                height: 16,
                width: 100,
                color: Colors.grey[300],
              ),
            ),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 2,
              padding: EdgeInsets.zero,
              itemBuilder: (context, productIndex) {
                return Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildProductImageSkeleton(),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildProductNameSkeleton(),
                            SizedBox(height: 8.h),
                            Row(
                              children: [
                                Container(
                                  height: 14,
                                  width: 60,
                                  color: Colors.grey[300],
                                ),
                                const SizedBox(width: 16),
                                Container(
                                  height: 14,
                                  width: 80,
                                  color: Colors.grey[300],
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Container(
                              height: 14,
                              width: 120,
                              color: Colors.grey[300],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            SizedBox(height: 16.h),
          ],
        );
      },
    );
  }
}
