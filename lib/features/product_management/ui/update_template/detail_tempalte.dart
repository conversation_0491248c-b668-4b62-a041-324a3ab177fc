import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/product_management/provider/detail_template/detail_template_provider.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class DetailTemplate extends ConsumerStatefulWidget {
  const DetailTemplate({super.key, required this.templateId});
  final int templateId;

  @override
  ConsumerState<DetailTemplate> createState() => _DetailTemplateState();
}

class _DetailTemplateState extends ConsumerState<DetailTemplate> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(detailTemplateNotifierProvider.notifier)
          .getDetailTemplate(widget.templateId);
    });
  }

  Widget _buildTemplateInfo() {
    final template = ref.watch(detailTemplateNotifierProvider).template;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16.h),
        Row(children: [
          Text(
            '販売価格',
            style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
          ),
          const Spacer(),
          Text(
            '${template?.basePrice?.priceString()}円',
            style: AppTextStyles.regular(
              14.sp,
              color: AppColors.textPrimary,
            ),
          ),
        ]),
        SizedBox(height: 8.h),
        Text(
          'アピールポイント',
          style: AppTextStyles.bold(16.sp, color: AppColors.textPrimary),
        ),
        SizedBox(height: 8.h),
        Text(
          template?.description ?? '',
          style:
              AppTextStyles.regular(14.sp, color: AppColors.textLightSecondary),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(detailTemplateNotifierProvider);

    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: state.template?.name ?? "",
        onTap: () => context.pop(),
      ),
      bottom: SafeArea(
          child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: CommonButton(
          text: 'テンプレート更新',
          onPressed: () {
            context
                .push(RouterPaths.updateTemplate, extra: state.template)
                .then((value) {
              if (value == true) {
                ref
                    .read(detailTemplateNotifierProvider.notifier)
                    .getDetailTemplate(widget.templateId);
              }
            });
          },
        ),
      )),
      body: state.isLoading
          ? const Center(child: CircularProgressIndicator())
          : state.errorMessage != null
              ? Center(
                  child: ErrorCommonWidget(
                  error: state.errorMessage!,
                  onPressed: () {
                    ref
                        .read(detailTemplateNotifierProvider.notifier)
                        .getDetailTemplate(widget.templateId);
                  },
                ))
              : SafeArea(
                  top: false,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 16.h),
                        BaseCachedNetworkImage(
                          imageUrl: state.template?.baseImg ?? '',
                          width: double.infinity,
                          height: 375.h,
                          fit: BoxFit.cover,
                          borderRadius: BorderRadius.circular(0),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: _buildTemplateInfo(),
                        ),
                        SizedBox(height: 16.h),
                        // Add more template details here
                      ],
                    ),
                  ),
                ),
    );
  }
}
