import 'dart:io';

import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/custom_choose_avatar/dotted_circle_painter.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/image_picker/common_image_picker.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/product_management/provider/detail_template/detail_template_provider.dart';
import 'package:kitemite_app/features/shopping_cart/ui/widget/shopping_cart_item.dart';
import 'package:kitemite_app/model/request/store/update_template_request.dart';
import 'package:kitemite_app/model/response/store/template_model.dart';

class UpdateTemplate extends ConsumerStatefulWidget {
  const UpdateTemplate({super.key, required this.template});
  final TemplateModel? template;

  @override
  ConsumerState<UpdateTemplate> createState() => _UpdateTemplateState();
}

class _UpdateTemplateState extends ConsumerState<UpdateTemplate> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  bool _priceEdited = false; // Flag to track if price has been edited
  final CommonImagePicker _imagePicker = CommonImagePicker(
    maxSizeInBytes: 50 * 1024 * 1024,
    allowedFormats: ['jpg', 'jpeg', 'png', 'gif'],
  );
  final CurrencyTextInputFormatter _formatter =
      CurrencyTextInputFormatter.currency(
          locale: 'ja', symbol: '円', customPattern: '#,##0\u00a4');

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      _initializeForm();
    });
  }

  void _initializeForm() {
    if (widget.template != null) {
      // Initialize controllers with template data
      _nameController.text = widget.template!.name ?? '';
      _descriptionController.text = widget.template!.description ?? '';
      _priceController.text = widget.template!.basePrice != null
          ? '${widget.template!.basePrice.toString().priceString()}円'
          : '0円';

      // Initialize the request in the provider with all required fields
      ref.read(detailTemplateNotifierProvider.notifier).updateRequest(
            UpdateTemplateRequest(
              name: widget.template!.name ?? '',
              description: widget.template!.description ?? '',
              baseImg: widget.template!.baseImg ?? '',
              basePrice: double.tryParse(
                      widget.template!.basePrice?.toString() ?? '0') ??
                  0.0,
            ),
          );
    } else {
      // Initialize with empty values if no template is provided
      _nameController.text = '';
      _descriptionController.text = '';
      _priceController.text = '0円';

      ref.read(detailTemplateNotifierProvider.notifier).updateRequest(
            const UpdateTemplateRequest(
              name: '',
              description: '',
              baseImg: '',
              basePrice: 0.0,
            ),
          );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _showImagePickerBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (_) => SafeArea(child: _buildImagePickerOptions(context)),
    );
  }

  Future<void> _pickImage(BuildContext context, ImageSource source) async {
    context.pop();
    try {
      final XFile? pickedFile =
          await _imagePicker.pickImage(context, source: source);
      if (pickedFile != null) {
        final base64Image =
            await _imagePicker.convertToBase64(File(pickedFile.path));
        await ref
            .read(detailTemplateNotifierProvider.notifier)
            .upFileImage(base64Image);

        // Show error if upload failed
        final state = ref.read(detailTemplateNotifierProvider);
        if (state.upFileImageFailure != null) {
          context.showErrorSnackBar(state.upFileImageFailure!);
        }
      }
    } catch (e) {
      checkCameraPermission(context);
      // context.showErrorSnackBar('Failed to pick image: $e');
    }
  }

  Widget _buildImagePickerOptions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.camera_alt),
            title: const Text('画像撮影'),
            onTap: () => _pickImage(context, ImageSource.camera),
          ),
          ListTile(
            leading: const Icon(Icons.photo_library),
            title: const Text('アルバムからアップロード'),
            onTap: () => _pickImage(context, ImageSource.gallery),
          ),
        ],
      ),
    );
  }

  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return '商品名を入力してください。';
    }
    if (value.length > 30) {
      return '30文字以内で入力してください。';
    }
    return null;
  }

  String? _validateDescription(String? value) {
    if (value == null || value.isEmpty) {
      return 'アピール文章を入力してください。';
    }
    if (value.length > 1000) {
      return '1000文字以内で入力してください';
    }
    return null;
  }

  String? _validatePrice(String? value) {
    if (value == null || value.isEmpty) {
      return '定価を入力してください。';
    }
    final price = _formatter.getUnformattedValue().toDouble();

    // Only validate the minimum price if the user has edited the price
    if (_priceEdited && price < 1) {
      return '定価は1円以上で入力してください。';
    }
    return null;
  }

  Future<void> _submitForm() async {
    if (!(_formKey.currentState?.validate() ?? false)) {
      return; // Stop if validation fails
    }

    final templateId = widget.template?.id;
    if (templateId == null) {
      context.showErrorSnackBar('Template ID is missing');
      return;
    }

    // Get unformatted price value and convert to double
    final price = _priceEdited
        ? _formatter.getUnformattedValue().toDouble()
        : double.tryParse(widget.template!.basePrice?.toString() ?? '0') ?? 0.0;

    // Update the request with current form values
    ref.read(detailTemplateNotifierProvider.notifier).updateRequest(
          UpdateTemplateRequest(
            name: _nameController.text,
            description: _descriptionController.text,
            baseImg: widget.template?.baseImg ?? '',
            basePrice: price,
          ),
        );

    await ref
        .read(detailTemplateNotifierProvider.notifier)
        .updateTemplate(templateId);

    final state = ref.read(detailTemplateNotifierProvider);
    if (state.errorMessage != null) {
      context.showErrorSnackBar(state.errorMessage!);
      return; // Stop if there's an error
    }

    if (state.isSuccess) {
      context.showSuccessSnackBar('テンプレートが更新されました。');
      context.pop(true);
      context.pop(true);
    }
  }

  Future<void> _retryUpdate() async {
    final templateId = widget.template?.id;
    await ref
        .read(detailTemplateNotifierProvider.notifier)
        .updateTemplate(templateId ?? 0);
    final state = ref.read(detailTemplateNotifierProvider);
    if (state.errorMessage != null) {
      context.showErrorSnackBar(state.errorMessage!);
      return; // Stop if there's an error
    }

    if (state.isSuccess) {
      context.showSuccessSnackBar('テンプレートが更新されました。');
      context.pop(true);
      context.pop(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(detailTemplateNotifierProvider);

    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: 'テンプレート更新',
        onTap: () => context.pop(),
      ),
      bottom: SafeArea(
        child: state.isLoading
            ? const Center(child: CircularProgressIndicator())
            : state.errorMessage != null
                ? const SizedBox.shrink()
                : Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    child: CommonButton(
                      text: '更新',
                      onPressed: _submitForm,
                    ),
                  ),
      ),
      body: state.errorMessage != null
          ? Center(
              child: ErrorCommonWidget(
                error: state.errorMessage!,
                onPressed: () {
                  _retryUpdate();
                },
              ),
            )
          : GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        GestureDetector(
                          onTap: () => _showImagePickerBottomSheet(context),
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              CustomPaint(
                                size: Size(double.infinity, 200.h),
                                painter: DottedRoundedRectPainter(),
                              ),
                              Opacity(
                                  opacity: 0.5,
                                  child: Container(
                                    width: double.infinity,
                                    height: 200.w,
                                    margin: const EdgeInsets.only(
                                        bottom: 16, left: 8, right: 8, top: 16),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: state.isUploadingImage
                                        ? const Center(
                                            child: CircularProgressIndicator(),
                                          )
                                        : state.urlImage != null
                                            ? BaseCachedNetworkImage(
                                                imageUrl: state.urlImage!,
                                                fit: BoxFit.cover,
                                                width: double.infinity,
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                height: 200.h,
                                              )
                                            : widget.template?.baseImg != null
                                                ? BaseCachedNetworkImage(
                                                    imageUrl: widget
                                                        .template!.baseImg!,
                                                    fit: BoxFit.cover,
                                                    width: double.infinity,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                    height: 200.h,
                                                  )
                                                : Image.asset(
                                                    'assets/images/avatar_default.png',
                                                    fit: BoxFit.cover,
                                                  ),
                                  )),
                              if (!state.isUploadingImage)
                                Column(
                                  children: [
                                    Icon(
                                      Icons.add_a_photo,
                                      color: AppColors.textLightSecondary,
                                      size: 24.sp,
                                    ),
                                    SizedBox(height: 8.h),
                                    Text(
                                      '画像更新',
                                      style: AppTextStyles.regular(12.sp,
                                          color: AppColors.textLightSecondary),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                        SizedBox(height: 32.h),
                        InputTextField(
                          label: '商品名',
                          textController: _nameController,
                          validator: _validateName,
                        ),
                        SizedBox(height: 24.h),
                        InputTextField(
                          label: '商品詳細',
                          textController: _descriptionController,
                          validator: _validateDescription,
                        ),
                        SizedBox(height: 24.h),
                        InputTextField(
                          label: '定価',
                          textController: _priceController,
                          keyboardType: TextInputType.number,
                          inputFormatters: [_formatter],
                          validator: _validatePrice,
                          onChanged: (value) {
                            setState(() {
                              _priceEdited = true;
                            });
                          },
                        ),
                        SizedBox(height: 32.h),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }
}
