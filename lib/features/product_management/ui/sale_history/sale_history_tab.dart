import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/product_management/provider/product_management_provider.dart';
import 'package:kitemite_app/features/product_management/ui/widgets/skeleton/product_management_skeleton.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/store/sales_history_model.dart';

class SaleHistoryTab extends ConsumerStatefulWidget {
  const SaleHistoryTab({super.key});

  @override
  ConsumerState<SaleHistoryTab> createState() => _SaleHistoryTabState();
}

class _SaleHistoryTabState extends ConsumerState<SaleHistoryTab> {
  final ScrollController _historyScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _historyScrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _historyScrollController.removeListener(_onScroll);
    _historyScrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_historyScrollController.position.pixels >=
        _historyScrollController.position.maxScrollExtent - 200) {
      // Trigger load more when user is 200px from bottom
      final state = ref.read(productManagementNotifierProvider);
      if (state.hasMoreSalesHistory && !state.isLoadingMoreSalesHistory) {
        ref
            .read(productManagementNotifierProvider.notifier)
            .loadMoreSalesHistory();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(productManagementNotifierProvider);
    final filteredSalesHistory = ref
        .read(productManagementNotifierProvider.notifier)
        .getFilteredSalesHistory();

    // Auto-scroll to highlighted product after data is loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state.highlightProductId != null &&
          filteredSalesHistory.isNotEmpty &&
          _historyScrollController.hasClients) {
        _scrollToHighlightedProduct(filteredSalesHistory);
      }
    });

    if (state.isLoadingSalesHistory) {
      return ProductManagementSkeleton.buildHistorySkeleton();
    }

    if (state.salesHistoryError != null) {
      return RefreshIndicator(
        onRefresh: () => ref
            .read(productManagementNotifierProvider.notifier)
            .fetchSalesHistory(),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red,
              ),
              SizedBox(height: 16.h),
              Text(
                state.salesHistoryError!,
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (filteredSalesHistory.isEmpty) {
      return RefreshIndicator(
        onRefresh: () => ref
            .read(productManagementNotifierProvider.notifier)
            .fetchSalesHistory(),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Assets.iconEmptyProduct.image(),
              SizedBox(height: 16.h),
              Text(
                "販売履歴がありません",
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref
          .read(productManagementNotifierProvider.notifier)
          .fetchSalesHistory(),
      child: ListView.builder(
        controller: _historyScrollController,
        shrinkWrap: true,
        itemCount:
            filteredSalesHistory.length + (state.hasMoreSalesHistory ? 1 : 0),
        padding: const EdgeInsets.only(top: 16),
        itemBuilder: (context, index) {
          // Show loading indicator at the bottom
          if (index == filteredSalesHistory.length) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: state.isLoadingMoreSalesHistory
                    ? const CircularProgressIndicator()
                    : const SizedBox.shrink(),
              ),
            );
          }
          return _buildSalesHistoryItem(filteredSalesHistory[index]);
        },
      ),
    );
  }

  String _formatDateForHistory(String dateString) {
    if (dateString.isEmpty) return '';

    try {
      final date = DateTime.parse(dateString);
      return "${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}";
    } catch (e) {
      return dateString;
    }
  }

  void _scrollToHighlightedProduct(List<SalesHistoryModel> salesHistory) {
    final state = ref.read(productManagementNotifierProvider);
    if (state.highlightProductId == null) return;

    // Find the index of the sales history that contains the highlighted product
    int foundIndex = -1;
    for (int i = 0; i < salesHistory.length; i++) {
      final products = salesHistory[i].products;
      for (var product in products) {
        if (product.orderId.toString() == state.highlightOrderId &&
            product.id.toString() == state.highlightProductId) {
          foundIndex = i;
          break;
        }
      }
      if (foundIndex != -1) break;
    }

    if (foundIndex != -1) {
      // Calculate approximate position (each item has approximate height)
      const double itemHeight =
          150.0; // Approximate height per sales history item
      final double targetPosition = foundIndex * itemHeight;

      Future.delayed(const Duration(milliseconds: 500), () {
        if (_historyScrollController.hasClients) {
          _historyScrollController.animateTo(
            targetPosition,
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeInOut,
          );
        }
      });

      // Clear highlight after some time
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          print('=== TRIGGERING CLEAR HIGHLIGHT FROM SALE HISTORY ===');
          ref.read(productManagementNotifierProvider.notifier).clearHighlight();
          // Force rebuild to ensure UI updates
          if (mounted) {
            setState(() {});
          }
        }
      });
    }
  }

  Widget _buildSalesHistoryItem(SalesHistoryModel salesHistory) {
    final state = ref.watch(productManagementNotifierProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          child: Text(
            _formatDateForHistory(salesHistory.date ?? ''),
            style: AppTextStyles.medium(14.sp, color: AppColors.textPrimary)
                .copyWith(fontWeight: FontWeight.w600),
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: salesHistory.products.length,
          padding: EdgeInsets.zero,
          itemBuilder: (context, index) {
            final product = salesHistory.products[index];

            bool isHighlighted = false;
            if (state.highlightProductId != null &&
                product.orderId.toString() == state.highlightOrderId) {
              // If no dateNoti provided, highlight any matching product ID
              if (state.highlightProductId != null &&
                  product.id.toString() == state.highlightProductId) {
                isHighlighted = true;
              }
            }

            return AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                // padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isHighlighted
                      ? AppColors.primary.withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: isHighlighted
                      ? Border.all(color: AppColors.primary, width: 2)
                      : null,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: BaseCachedNetworkImage(
                        imageUrl: product.img ?? '',
                        width: 84,
                        height: 90,
                        fit: BoxFit.cover,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            product.name ?? '',
                            style: AppTextStyles.bold(14.sp,
                                color: isHighlighted
                                    ? AppColors.primary
                                    : AppColors.textPrimary),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            "数量: ${product.saleQuantity ?? 0}　合計金額: ${product.totalAmountPrice?.toString().priceString() ?? '0'} 円",
                            style: AppTextStyles.regular(12.sp,
                                color: AppColors.textLightSecondary),
                          ),
                          SizedBox(height: 4.h),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                Chip(
                                  labelPadding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  label: Text(
                                    product.place?.name ?? '',
                                    style: AppTextStyles.medium(12.sp,
                                        color: AppColors.textLightSecondary),
                                  ),
                                  backgroundColor: AppColors.grey200,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                      side: const BorderSide(
                                          color: AppColors.grey200, width: 1)),
                                  padding: EdgeInsets.zero,
                                ),
                                const SizedBox(
                                  width: 8,
                                ),
                                if (product.cabinet != null)
                                  Chip(
                                    labelPadding: const EdgeInsets.symmetric(
                                        horizontal: 8),
                                    label: Text(
                                      product.cabinet!.cabinetCode ?? '',
                                      style: AppTextStyles.medium(12.sp,
                                          color: AppColors.textLightSecondary),
                                    ),
                                    backgroundColor: AppColors.grey200,
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                        side: const BorderSide(
                                            color: AppColors.grey200,
                                            width: 1)),
                                    padding: EdgeInsets.zero,
                                  ),
                                if (product.cabinet != null &&
                                    product.shelf != null)
                                  const SizedBox(width: 8),
                                if (product.shelf != null)
                                  Chip(
                                    labelPadding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 0),
                                    label: Text(
                                      "No.${product.shelf!.shelfCode ?? ''}",
                                      style: AppTextStyles.medium(12.sp,
                                          color: AppColors.textLightSecondary),
                                    ),
                                    backgroundColor: AppColors.grey200,
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                        side: const BorderSide(
                                            color: AppColors.grey200,
                                            width: 1)),
                                    padding: EdgeInsets.zero,
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ));
          },
        ),
      ],
    );
  }
}
