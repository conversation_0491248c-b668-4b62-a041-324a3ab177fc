// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_management_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productManagementNotifierHash() =>
    r'1f285cebff44d9e88f5d19d82faceb4343e40959';

/// See also [ProductManagementNotifier].
@ProviderFor(ProductManagementNotifier)
final productManagementNotifierProvider = AutoDisposeNotifierProvider<
    ProductManagementNotifier, ProductManagementState>.internal(
  ProductManagementNotifier.new,
  name: r'productManagementNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$productManagementNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProductManagementNotifier
    = AutoDisposeNotifier<ProductManagementState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
