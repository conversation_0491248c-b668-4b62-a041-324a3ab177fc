// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'detail_template_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DetailTemplateState _$DetailTemplateStateFromJson(Map<String, dynamic> json) {
  return _DetailTemplateState.fromJson(json);
}

/// @nodoc
mixin _$DetailTemplateState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isSuccess => throw _privateConstructorUsedError;
  TemplateModel? get template => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  String? get urlImage => throw _privateConstructorUsedError;
  bool get isUploadingImage => throw _privateConstructorUsedError;
  String? get upFileImageFailure => throw _privateConstructorUsedError;
  bool get showImageSelectionDialog => throw _privateConstructorUsedError;
  UpdateTemplateRequest? get request => throw _privateConstructorUsedError;

  /// Serializes this DetailTemplateState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DetailTemplateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DetailTemplateStateCopyWith<DetailTemplateState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DetailTemplateStateCopyWith<$Res> {
  factory $DetailTemplateStateCopyWith(
          DetailTemplateState value, $Res Function(DetailTemplateState) then) =
      _$DetailTemplateStateCopyWithImpl<$Res, DetailTemplateState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      TemplateModel? template,
      String? errorMessage,
      String? urlImage,
      bool isUploadingImage,
      String? upFileImageFailure,
      bool showImageSelectionDialog,
      UpdateTemplateRequest? request});

  $UpdateTemplateRequestCopyWith<$Res>? get request;
}

/// @nodoc
class _$DetailTemplateStateCopyWithImpl<$Res, $Val extends DetailTemplateState>
    implements $DetailTemplateStateCopyWith<$Res> {
  _$DetailTemplateStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DetailTemplateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? template = freezed,
    Object? errorMessage = freezed,
    Object? urlImage = freezed,
    Object? isUploadingImage = null,
    Object? upFileImageFailure = freezed,
    Object? showImageSelectionDialog = null,
    Object? request = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      template: freezed == template
          ? _value.template
          : template // ignore: cast_nullable_to_non_nullable
              as TemplateModel?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      showImageSelectionDialog: null == showImageSelectionDialog
          ? _value.showImageSelectionDialog
          : showImageSelectionDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      request: freezed == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as UpdateTemplateRequest?,
    ) as $Val);
  }

  /// Create a copy of DetailTemplateState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UpdateTemplateRequestCopyWith<$Res>? get request {
    if (_value.request == null) {
      return null;
    }

    return $UpdateTemplateRequestCopyWith<$Res>(_value.request!, (value) {
      return _then(_value.copyWith(request: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DetailTemplateStateImplCopyWith<$Res>
    implements $DetailTemplateStateCopyWith<$Res> {
  factory _$$DetailTemplateStateImplCopyWith(_$DetailTemplateStateImpl value,
          $Res Function(_$DetailTemplateStateImpl) then) =
      __$$DetailTemplateStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      TemplateModel? template,
      String? errorMessage,
      String? urlImage,
      bool isUploadingImage,
      String? upFileImageFailure,
      bool showImageSelectionDialog,
      UpdateTemplateRequest? request});

  @override
  $UpdateTemplateRequestCopyWith<$Res>? get request;
}

/// @nodoc
class __$$DetailTemplateStateImplCopyWithImpl<$Res>
    extends _$DetailTemplateStateCopyWithImpl<$Res, _$DetailTemplateStateImpl>
    implements _$$DetailTemplateStateImplCopyWith<$Res> {
  __$$DetailTemplateStateImplCopyWithImpl(_$DetailTemplateStateImpl _value,
      $Res Function(_$DetailTemplateStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DetailTemplateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? template = freezed,
    Object? errorMessage = freezed,
    Object? urlImage = freezed,
    Object? isUploadingImage = null,
    Object? upFileImageFailure = freezed,
    Object? showImageSelectionDialog = null,
    Object? request = freezed,
  }) {
    return _then(_$DetailTemplateStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      template: freezed == template
          ? _value.template
          : template // ignore: cast_nullable_to_non_nullable
              as TemplateModel?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      showImageSelectionDialog: null == showImageSelectionDialog
          ? _value.showImageSelectionDialog
          : showImageSelectionDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      request: freezed == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as UpdateTemplateRequest?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DetailTemplateStateImpl implements _DetailTemplateState {
  const _$DetailTemplateStateImpl(
      {this.isLoading = false,
      this.isSuccess = false,
      this.template,
      this.errorMessage,
      this.urlImage,
      this.isUploadingImage = false,
      this.upFileImageFailure,
      this.showImageSelectionDialog = false,
      this.request});

  factory _$DetailTemplateStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$DetailTemplateStateImplFromJson(json);

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSuccess;
  @override
  final TemplateModel? template;
  @override
  final String? errorMessage;
  @override
  final String? urlImage;
  @override
  @JsonKey()
  final bool isUploadingImage;
  @override
  final String? upFileImageFailure;
  @override
  @JsonKey()
  final bool showImageSelectionDialog;
  @override
  final UpdateTemplateRequest? request;

  @override
  String toString() {
    return 'DetailTemplateState(isLoading: $isLoading, isSuccess: $isSuccess, template: $template, errorMessage: $errorMessage, urlImage: $urlImage, isUploadingImage: $isUploadingImage, upFileImageFailure: $upFileImageFailure, showImageSelectionDialog: $showImageSelectionDialog, request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DetailTemplateStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSuccess, isSuccess) ||
                other.isSuccess == isSuccess) &&
            (identical(other.template, template) ||
                other.template == template) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.urlImage, urlImage) ||
                other.urlImage == urlImage) &&
            (identical(other.isUploadingImage, isUploadingImage) ||
                other.isUploadingImage == isUploadingImage) &&
            (identical(other.upFileImageFailure, upFileImageFailure) ||
                other.upFileImageFailure == upFileImageFailure) &&
            (identical(
                    other.showImageSelectionDialog, showImageSelectionDialog) ||
                other.showImageSelectionDialog == showImageSelectionDialog) &&
            (identical(other.request, request) || other.request == request));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isSuccess,
      template,
      errorMessage,
      urlImage,
      isUploadingImage,
      upFileImageFailure,
      showImageSelectionDialog,
      request);

  /// Create a copy of DetailTemplateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DetailTemplateStateImplCopyWith<_$DetailTemplateStateImpl> get copyWith =>
      __$$DetailTemplateStateImplCopyWithImpl<_$DetailTemplateStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DetailTemplateStateImplToJson(
      this,
    );
  }
}

abstract class _DetailTemplateState implements DetailTemplateState {
  const factory _DetailTemplateState(
      {final bool isLoading,
      final bool isSuccess,
      final TemplateModel? template,
      final String? errorMessage,
      final String? urlImage,
      final bool isUploadingImage,
      final String? upFileImageFailure,
      final bool showImageSelectionDialog,
      final UpdateTemplateRequest? request}) = _$DetailTemplateStateImpl;

  factory _DetailTemplateState.fromJson(Map<String, dynamic> json) =
      _$DetailTemplateStateImpl.fromJson;

  @override
  bool get isLoading;
  @override
  bool get isSuccess;
  @override
  TemplateModel? get template;
  @override
  String? get errorMessage;
  @override
  String? get urlImage;
  @override
  bool get isUploadingImage;
  @override
  String? get upFileImageFailure;
  @override
  bool get showImageSelectionDialog;
  @override
  UpdateTemplateRequest? get request;

  /// Create a copy of DetailTemplateState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DetailTemplateStateImplCopyWith<_$DetailTemplateStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
