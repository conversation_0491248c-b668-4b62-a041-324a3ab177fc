// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_template_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DetailTemplateStateImpl _$$DetailTemplateStateImplFromJson(
        Map<String, dynamic> json) =>
    _$DetailTemplateStateImpl(
      isLoading: json['isLoading'] as bool? ?? false,
      isSuccess: json['isSuccess'] as bool? ?? false,
      template: json['template'] == null
          ? null
          : TemplateModel.fromJson(json['template'] as Map<String, dynamic>),
      errorMessage: json['errorMessage'] as String?,
      urlImage: json['urlImage'] as String?,
      isUploadingImage: json['isUploadingImage'] as bool? ?? false,
      upFileImageFailure: json['upFileImageFailure'] as String?,
      showImageSelectionDialog:
          json['showImageSelectionDialog'] as bool? ?? false,
      request: json['request'] == null
          ? null
          : UpdateTemplateRequest.fromJson(
              json['request'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DetailTemplateStateImplToJson(
        _$DetailTemplateStateImpl instance) =>
    <String, dynamic>{
      'isLoading': instance.isLoading,
      'isSuccess': instance.isSuccess,
      'template': instance.template,
      'errorMessage': instance.errorMessage,
      'urlImage': instance.urlImage,
      'isUploadingImage': instance.isUploadingImage,
      'upFileImageFailure': instance.upFileImageFailure,
      'showImageSelectionDialog': instance.showImageSelectionDialog,
      'request': instance.request,
    };
