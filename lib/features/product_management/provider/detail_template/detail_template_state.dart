import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/request/store/update_template_request.dart';
import 'package:kitemite_app/model/response/store/template_model.dart';

part 'detail_template_state.freezed.dart';
part 'detail_template_state.g.dart';

@freezed
class DetailTemplateState with _$DetailTemplateState {
  const factory DetailTemplateState({
    @Default(false) bool isLoading,
    @Default(false) bool isSuccess,
    TemplateModel? template,
    String? errorMessage,
    String? urlImage,
    @Default(false) bool isUploadingImage,
    String? upFileImageFailure,
    @Default(false) bool showImageSelectionDialog,
    UpdateTemplateRequest? request,
  }) = _DetailTemplateState;

  factory DetailTemplateState.fromJson(Map<String, dynamic> json) =>
      _$DetailTemplateStateFromJson(json);
}
