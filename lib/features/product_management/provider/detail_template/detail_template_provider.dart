import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/auth/auth_repository.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/features/product_management/provider/detail_template/detail_template_state.dart';
import 'package:kitemite_app/model/request/store/update_template_request.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'detail_template_provider.g.dart';

@riverpod
class DetailTemplateNotifier extends _$DetailTemplateNotifier {
  @override
  DetailTemplateState build() => const DetailTemplateState();

  Future<void> getDetailTemplate(int id) async {
    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
    );

    try {
      final response = await ref
          .read(getStoreRepositoryProvider)
          .getDetailTemplateWarehouse(id);
      state = state.copyWith(
        template: response.data,
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.message,
      );
    }
  }

  Future<void> updateTemplate(int id) async {
    if (state.request == null) return;

    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
    );
    var requestNew = state.request!.copyWith(
      baseImg: state.urlImage ?? state.request!.baseImg,
    );
    try {
      await ref
          .read(getStoreRepositoryProvider)
          .updateTemplateWarehouse(id, requestNew);
      state = state.copyWith(
        isSuccess: true,
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.message,
      );
    }
  }

  Future<void> upFileImage(String imageBase64) async {
    state = state.copyWith(
      upFileImageFailure: null,
      isUploadingImage: true,
    );

    try {
      final authRepo = ref.read(getAuthRepositoryProvider);
      final result = await authRepo.upLoadFile(base64: imageBase64);

      state = state.copyWith(
        urlImage: result.links?.first ?? "",
        isLoading: false,
        isUploadingImage: false,
        showImageSelectionDialog: false,
      );

      // Update the request with the new image URL
      if (state.request != null) {
        state = state.copyWith(
          request: state.request!.copyWith(baseImg: result.links?.first ?? ""),
        );
      }
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        isUploadingImage: false,
        upFileImageFailure: e.message,
      );
    }
  }

  void updateRequest(UpdateTemplateRequest request) {
    state = state.copyWith(request: request);
  }

  void resetState() {
    state = const DetailTemplateState();
  }

  void resetImageSelectionDialog() {
    state = state.copyWith(showImageSelectionDialog: false);
  }
}
