// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_template_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$detailTemplateNotifierHash() =>
    r'488434b7947f5e4bd32fd8e214874f7123107dd0';

/// See also [DetailTemplateNotifier].
@ProviderFor(DetailTemplateNotifier)
final detailTemplateNotifierProvider = AutoDisposeNotifierProvider<
    DetailTemplateNotifier, DetailTemplateState>.internal(
  DetailTemplateNotifier.new,
  name: r'detailTemplateNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$detailTemplateNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DetailTemplateNotifier = AutoDisposeNotifier<DetailTemplateState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
