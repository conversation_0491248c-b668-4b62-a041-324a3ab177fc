// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_management_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProductManagementState {
  List<WarehouseModel> get warehouseModel => throw _privateConstructorUsedError;
  List<TemplateModel> get templates => throw _privateConstructorUsedError;
  List<SalesHistoryModel> get salesHistory =>
      throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isLoadingSalesHistory => throw _privateConstructorUsedError;
  bool get isLoadingMoreSalesHistory => throw _privateConstructorUsedError;
  int get salesHistoryCurrentPage => throw _privateConstructorUsedError;
  bool get hasMoreSalesHistory => throw _privateConstructorUsedError;
  String get searchQuery => throw _privateConstructorUsedError;
  int get selectedTabIndex => throw _privateConstructorUsedError;
  String? get deleteTemplateError => throw _privateConstructorUsedError;
  String? get deleteTemplateSuccess => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  String? get salesHistoryError => throw _privateConstructorUsedError;
  String? get highlightProductId => throw _privateConstructorUsedError;
  String? get highlightOrderId => throw _privateConstructorUsedError;

  /// Create a copy of ProductManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductManagementStateCopyWith<ProductManagementState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductManagementStateCopyWith<$Res> {
  factory $ProductManagementStateCopyWith(ProductManagementState value,
          $Res Function(ProductManagementState) then) =
      _$ProductManagementStateCopyWithImpl<$Res, ProductManagementState>;
  @useResult
  $Res call(
      {List<WarehouseModel> warehouseModel,
      List<TemplateModel> templates,
      List<SalesHistoryModel> salesHistory,
      bool isLoading,
      bool isLoadingSalesHistory,
      bool isLoadingMoreSalesHistory,
      int salesHistoryCurrentPage,
      bool hasMoreSalesHistory,
      String searchQuery,
      int selectedTabIndex,
      String? deleteTemplateError,
      String? deleteTemplateSuccess,
      String? errorMessage,
      String? salesHistoryError,
      String? highlightProductId,
      String? highlightOrderId});
}

/// @nodoc
class _$ProductManagementStateCopyWithImpl<$Res,
        $Val extends ProductManagementState>
    implements $ProductManagementStateCopyWith<$Res> {
  _$ProductManagementStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseModel = null,
    Object? templates = null,
    Object? salesHistory = null,
    Object? isLoading = null,
    Object? isLoadingSalesHistory = null,
    Object? isLoadingMoreSalesHistory = null,
    Object? salesHistoryCurrentPage = null,
    Object? hasMoreSalesHistory = null,
    Object? searchQuery = null,
    Object? selectedTabIndex = null,
    Object? deleteTemplateError = freezed,
    Object? deleteTemplateSuccess = freezed,
    Object? errorMessage = freezed,
    Object? salesHistoryError = freezed,
    Object? highlightProductId = freezed,
    Object? highlightOrderId = freezed,
  }) {
    return _then(_value.copyWith(
      warehouseModel: null == warehouseModel
          ? _value.warehouseModel
          : warehouseModel // ignore: cast_nullable_to_non_nullable
              as List<WarehouseModel>,
      templates: null == templates
          ? _value.templates
          : templates // ignore: cast_nullable_to_non_nullable
              as List<TemplateModel>,
      salesHistory: null == salesHistory
          ? _value.salesHistory
          : salesHistory // ignore: cast_nullable_to_non_nullable
              as List<SalesHistoryModel>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingSalesHistory: null == isLoadingSalesHistory
          ? _value.isLoadingSalesHistory
          : isLoadingSalesHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMoreSalesHistory: null == isLoadingMoreSalesHistory
          ? _value.isLoadingMoreSalesHistory
          : isLoadingMoreSalesHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      salesHistoryCurrentPage: null == salesHistoryCurrentPage
          ? _value.salesHistoryCurrentPage
          : salesHistoryCurrentPage // ignore: cast_nullable_to_non_nullable
              as int,
      hasMoreSalesHistory: null == hasMoreSalesHistory
          ? _value.hasMoreSalesHistory
          : hasMoreSalesHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      selectedTabIndex: null == selectedTabIndex
          ? _value.selectedTabIndex
          : selectedTabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      deleteTemplateError: freezed == deleteTemplateError
          ? _value.deleteTemplateError
          : deleteTemplateError // ignore: cast_nullable_to_non_nullable
              as String?,
      deleteTemplateSuccess: freezed == deleteTemplateSuccess
          ? _value.deleteTemplateSuccess
          : deleteTemplateSuccess // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      salesHistoryError: freezed == salesHistoryError
          ? _value.salesHistoryError
          : salesHistoryError // ignore: cast_nullable_to_non_nullable
              as String?,
      highlightProductId: freezed == highlightProductId
          ? _value.highlightProductId
          : highlightProductId // ignore: cast_nullable_to_non_nullable
              as String?,
      highlightOrderId: freezed == highlightOrderId
          ? _value.highlightOrderId
          : highlightOrderId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductManagementStateImplCopyWith<$Res>
    implements $ProductManagementStateCopyWith<$Res> {
  factory _$$ProductManagementStateImplCopyWith(
          _$ProductManagementStateImpl value,
          $Res Function(_$ProductManagementStateImpl) then) =
      __$$ProductManagementStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<WarehouseModel> warehouseModel,
      List<TemplateModel> templates,
      List<SalesHistoryModel> salesHistory,
      bool isLoading,
      bool isLoadingSalesHistory,
      bool isLoadingMoreSalesHistory,
      int salesHistoryCurrentPage,
      bool hasMoreSalesHistory,
      String searchQuery,
      int selectedTabIndex,
      String? deleteTemplateError,
      String? deleteTemplateSuccess,
      String? errorMessage,
      String? salesHistoryError,
      String? highlightProductId,
      String? highlightOrderId});
}

/// @nodoc
class __$$ProductManagementStateImplCopyWithImpl<$Res>
    extends _$ProductManagementStateCopyWithImpl<$Res,
        _$ProductManagementStateImpl>
    implements _$$ProductManagementStateImplCopyWith<$Res> {
  __$$ProductManagementStateImplCopyWithImpl(
      _$ProductManagementStateImpl _value,
      $Res Function(_$ProductManagementStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseModel = null,
    Object? templates = null,
    Object? salesHistory = null,
    Object? isLoading = null,
    Object? isLoadingSalesHistory = null,
    Object? isLoadingMoreSalesHistory = null,
    Object? salesHistoryCurrentPage = null,
    Object? hasMoreSalesHistory = null,
    Object? searchQuery = null,
    Object? selectedTabIndex = null,
    Object? deleteTemplateError = freezed,
    Object? deleteTemplateSuccess = freezed,
    Object? errorMessage = freezed,
    Object? salesHistoryError = freezed,
    Object? highlightProductId = freezed,
    Object? highlightOrderId = freezed,
  }) {
    return _then(_$ProductManagementStateImpl(
      warehouseModel: null == warehouseModel
          ? _value._warehouseModel
          : warehouseModel // ignore: cast_nullable_to_non_nullable
              as List<WarehouseModel>,
      templates: null == templates
          ? _value._templates
          : templates // ignore: cast_nullable_to_non_nullable
              as List<TemplateModel>,
      salesHistory: null == salesHistory
          ? _value._salesHistory
          : salesHistory // ignore: cast_nullable_to_non_nullable
              as List<SalesHistoryModel>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingSalesHistory: null == isLoadingSalesHistory
          ? _value.isLoadingSalesHistory
          : isLoadingSalesHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMoreSalesHistory: null == isLoadingMoreSalesHistory
          ? _value.isLoadingMoreSalesHistory
          : isLoadingMoreSalesHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      salesHistoryCurrentPage: null == salesHistoryCurrentPage
          ? _value.salesHistoryCurrentPage
          : salesHistoryCurrentPage // ignore: cast_nullable_to_non_nullable
              as int,
      hasMoreSalesHistory: null == hasMoreSalesHistory
          ? _value.hasMoreSalesHistory
          : hasMoreSalesHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      selectedTabIndex: null == selectedTabIndex
          ? _value.selectedTabIndex
          : selectedTabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      deleteTemplateError: freezed == deleteTemplateError
          ? _value.deleteTemplateError
          : deleteTemplateError // ignore: cast_nullable_to_non_nullable
              as String?,
      deleteTemplateSuccess: freezed == deleteTemplateSuccess
          ? _value.deleteTemplateSuccess
          : deleteTemplateSuccess // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      salesHistoryError: freezed == salesHistoryError
          ? _value.salesHistoryError
          : salesHistoryError // ignore: cast_nullable_to_non_nullable
              as String?,
      highlightProductId: freezed == highlightProductId
          ? _value.highlightProductId
          : highlightProductId // ignore: cast_nullable_to_non_nullable
              as String?,
      highlightOrderId: freezed == highlightOrderId
          ? _value.highlightOrderId
          : highlightOrderId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ProductManagementStateImpl implements _ProductManagementState {
  const _$ProductManagementStateImpl(
      {final List<WarehouseModel> warehouseModel = const [],
      final List<TemplateModel> templates = const [],
      final List<SalesHistoryModel> salesHistory = const [],
      this.isLoading = false,
      this.isLoadingSalesHistory = false,
      this.isLoadingMoreSalesHistory = false,
      this.salesHistoryCurrentPage = 1,
      this.hasMoreSalesHistory = true,
      this.searchQuery = '',
      this.selectedTabIndex = 0,
      this.deleteTemplateError,
      this.deleteTemplateSuccess,
      this.errorMessage,
      this.salesHistoryError,
      this.highlightProductId,
      this.highlightOrderId})
      : _warehouseModel = warehouseModel,
        _templates = templates,
        _salesHistory = salesHistory;

  final List<WarehouseModel> _warehouseModel;
  @override
  @JsonKey()
  List<WarehouseModel> get warehouseModel {
    if (_warehouseModel is EqualUnmodifiableListView) return _warehouseModel;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_warehouseModel);
  }

  final List<TemplateModel> _templates;
  @override
  @JsonKey()
  List<TemplateModel> get templates {
    if (_templates is EqualUnmodifiableListView) return _templates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_templates);
  }

  final List<SalesHistoryModel> _salesHistory;
  @override
  @JsonKey()
  List<SalesHistoryModel> get salesHistory {
    if (_salesHistory is EqualUnmodifiableListView) return _salesHistory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_salesHistory);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isLoadingSalesHistory;
  @override
  @JsonKey()
  final bool isLoadingMoreSalesHistory;
  @override
  @JsonKey()
  final int salesHistoryCurrentPage;
  @override
  @JsonKey()
  final bool hasMoreSalesHistory;
  @override
  @JsonKey()
  final String searchQuery;
  @override
  @JsonKey()
  final int selectedTabIndex;
  @override
  final String? deleteTemplateError;
  @override
  final String? deleteTemplateSuccess;
  @override
  final String? errorMessage;
  @override
  final String? salesHistoryError;
  @override
  final String? highlightProductId;
  @override
  final String? highlightOrderId;

  @override
  String toString() {
    return 'ProductManagementState(warehouseModel: $warehouseModel, templates: $templates, salesHistory: $salesHistory, isLoading: $isLoading, isLoadingSalesHistory: $isLoadingSalesHistory, isLoadingMoreSalesHistory: $isLoadingMoreSalesHistory, salesHistoryCurrentPage: $salesHistoryCurrentPage, hasMoreSalesHistory: $hasMoreSalesHistory, searchQuery: $searchQuery, selectedTabIndex: $selectedTabIndex, deleteTemplateError: $deleteTemplateError, deleteTemplateSuccess: $deleteTemplateSuccess, errorMessage: $errorMessage, salesHistoryError: $salesHistoryError, highlightProductId: $highlightProductId, highlightOrderId: $highlightOrderId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductManagementStateImpl &&
            const DeepCollectionEquality()
                .equals(other._warehouseModel, _warehouseModel) &&
            const DeepCollectionEquality()
                .equals(other._templates, _templates) &&
            const DeepCollectionEquality()
                .equals(other._salesHistory, _salesHistory) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoadingSalesHistory, isLoadingSalesHistory) ||
                other.isLoadingSalesHistory == isLoadingSalesHistory) &&
            (identical(other.isLoadingMoreSalesHistory,
                    isLoadingMoreSalesHistory) ||
                other.isLoadingMoreSalesHistory == isLoadingMoreSalesHistory) &&
            (identical(
                    other.salesHistoryCurrentPage, salesHistoryCurrentPage) ||
                other.salesHistoryCurrentPage == salesHistoryCurrentPage) &&
            (identical(other.hasMoreSalesHistory, hasMoreSalesHistory) ||
                other.hasMoreSalesHistory == hasMoreSalesHistory) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.selectedTabIndex, selectedTabIndex) ||
                other.selectedTabIndex == selectedTabIndex) &&
            (identical(other.deleteTemplateError, deleteTemplateError) ||
                other.deleteTemplateError == deleteTemplateError) &&
            (identical(other.deleteTemplateSuccess, deleteTemplateSuccess) ||
                other.deleteTemplateSuccess == deleteTemplateSuccess) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.salesHistoryError, salesHistoryError) ||
                other.salesHistoryError == salesHistoryError) &&
            (identical(other.highlightProductId, highlightProductId) ||
                other.highlightProductId == highlightProductId) &&
            (identical(other.highlightOrderId, highlightOrderId) ||
                other.highlightOrderId == highlightOrderId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_warehouseModel),
      const DeepCollectionEquality().hash(_templates),
      const DeepCollectionEquality().hash(_salesHistory),
      isLoading,
      isLoadingSalesHistory,
      isLoadingMoreSalesHistory,
      salesHistoryCurrentPage,
      hasMoreSalesHistory,
      searchQuery,
      selectedTabIndex,
      deleteTemplateError,
      deleteTemplateSuccess,
      errorMessage,
      salesHistoryError,
      highlightProductId,
      highlightOrderId);

  /// Create a copy of ProductManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductManagementStateImplCopyWith<_$ProductManagementStateImpl>
      get copyWith => __$$ProductManagementStateImplCopyWithImpl<
          _$ProductManagementStateImpl>(this, _$identity);
}

abstract class _ProductManagementState implements ProductManagementState {
  const factory _ProductManagementState(
      {final List<WarehouseModel> warehouseModel,
      final List<TemplateModel> templates,
      final List<SalesHistoryModel> salesHistory,
      final bool isLoading,
      final bool isLoadingSalesHistory,
      final bool isLoadingMoreSalesHistory,
      final int salesHistoryCurrentPage,
      final bool hasMoreSalesHistory,
      final String searchQuery,
      final int selectedTabIndex,
      final String? deleteTemplateError,
      final String? deleteTemplateSuccess,
      final String? errorMessage,
      final String? salesHistoryError,
      final String? highlightProductId,
      final String? highlightOrderId}) = _$ProductManagementStateImpl;

  @override
  List<WarehouseModel> get warehouseModel;
  @override
  List<TemplateModel> get templates;
  @override
  List<SalesHistoryModel> get salesHistory;
  @override
  bool get isLoading;
  @override
  bool get isLoadingSalesHistory;
  @override
  bool get isLoadingMoreSalesHistory;
  @override
  int get salesHistoryCurrentPage;
  @override
  bool get hasMoreSalesHistory;
  @override
  String get searchQuery;
  @override
  int get selectedTabIndex;
  @override
  String? get deleteTemplateError;
  @override
  String? get deleteTemplateSuccess;
  @override
  String? get errorMessage;
  @override
  String? get salesHistoryError;
  @override
  String? get highlightProductId;
  @override
  String? get highlightOrderId;

  /// Create a copy of ProductManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductManagementStateImplCopyWith<_$ProductManagementStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
