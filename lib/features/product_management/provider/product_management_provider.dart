import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/features/product_management/provider/product_management_state.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/response/store/sales_history_model.dart';
import 'package:kitemite_app/model/response/store/template_model.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'product_management_provider.g.dart';

@riverpod
class ProductManagementNotifier extends _$ProductManagementNotifier {
  @override
  ProductManagementState build() {
    return const ProductManagementState();
  }

  Future<void> fetchInitialData() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      final results = await Future.wait<BaseListResponse>([
        ref.read(getStoreRepositoryProvider).getListProductWarehouse(),
        ref.read(getStoreRepositoryProvider).getListTemplateWarehouse(),
      ]);

      final products = results[0] as BaseListResponse<WarehouseModel>;
      final templates = results[1] as BaseListResponse<TemplateModel>;

      state = state.copyWith(
        warehouseModel: products.data ?? [],
        templates: templates.data ?? [],
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.message);
    }
  }

  Future<void> fetchSalesHistory({bool isLoadMore = false}) async {
    try {
      if (isLoadMore) {
        // Don't load more if already loading or no more data
        if (state.isLoadingMoreSalesHistory || !state.hasMoreSalesHistory) {
          return;
        }
        state = state.copyWith(isLoadingMoreSalesHistory: true);
      } else {
        // Reset pagination for initial load
        state = state.copyWith(
          isLoadingSalesHistory: true,
          salesHistoryError: null,
          salesHistoryCurrentPage: 1,
          hasMoreSalesHistory: true,
        );
      }

      final page = isLoadMore ? state.salesHistoryCurrentPage + 1 : 1;
      final response = await ref
          .read(getStoreRepositoryProvider)
          .getSellerOrdersHistories(page);

      final newData = response.data ?? [];
      final hasMore =
          newData.isNotEmpty; // Adjust this logic based on your API response

      if (isLoadMore) {
        // Append new data to existing list
        state = state.copyWith(
          salesHistory: [...state.salesHistory, ...newData],
          isLoadingMoreSalesHistory: false,
          salesHistoryCurrentPage: page,
          hasMoreSalesHistory: hasMore,
        );
      } else {
        // Replace existing data
        state = state.copyWith(
          salesHistory: newData,
          isLoadingSalesHistory: false,
          salesHistoryCurrentPage: page,
          hasMoreSalesHistory: hasMore,
        );
      }
    } on AppFailure catch (e) {
      if (isLoadMore) {
        state = state.copyWith(
          isLoadingMoreSalesHistory: false,
          salesHistoryError: e.message,
        );
      } else {
        state = state.copyWith(
          isLoadingSalesHistory: false,
          salesHistoryError: e.message,
        );
      }
    }
  }

  Future<void> loadMoreSalesHistory() async {
    await fetchSalesHistory(isLoadMore: true);
  }

  Future<void> refresh() async {
    state = state.copyWith(
      isLoading: true,
      deleteTemplateError: null,
      deleteTemplateSuccess: null,
    );
    await fetchInitialData();
    // Fetch sales history if the history tab is currently selected
    if (state.selectedTabIndex == 2) {
      await fetchSalesHistory();
    }
  }

  void setSelectedTabIndex(int index) {
    state = state.copyWith(selectedTabIndex: index);
    // Fetch sales history when History tab is selected for the first time
    if (index == 2 &&
        state.salesHistory.isEmpty &&
        !state.isLoadingSalesHistory) {
      fetchSalesHistory();
    }
  }

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void setHighlightProductId(String? productId) {
    state = state.copyWith(highlightProductId: productId);
  }

  void setHighlightProductIdWithDate(
    String? productId,
    String? orderId,
  ) {
    print('setHighlightProductIdWithDate: $productId');
    state = state.copyWith(
      highlightProductId: productId,
      highlightOrderId: orderId,
    );
  }

  void clearHighlight() {
    state = state.copyWith(
      highlightProductId: null,
      highlightOrderId: null,
    );
  }

  List<WarehouseModel> getFilteredWarehouses() {
    if (state.searchQuery.isEmpty) return state.warehouseModel;

    return state.warehouseModel
        .map((warehouse) {
          // Filter cabinets that have matching products
          final filteredCabinets = warehouse.cabinets
              ?.map((cabinet) {
                final filteredProducts = cabinet.products
                    ?.where((product) =>
                        product.name
                            ?.toLowerCase()
                            .contains(state.searchQuery.toLowerCase()) ??
                        false)
                    .toList();

                return cabinet.copyWith(products: filteredProducts);
              })
              .where((cabinet) => cabinet.products?.isNotEmpty ?? false)
              .toList();

          // Return a new warehouse with only cabinets containing matching products
          return warehouse.copyWith(cabinets: filteredCabinets);
        })
        .where((warehouse) => warehouse.cabinets?.isNotEmpty ?? false)
        .toList();
  }

  List<TemplateModel> getFilteredTemplates() {
    if (state.searchQuery.isEmpty) return state.templates;
    return state.templates
        .where((template) =>
            template.name
                ?.toLowerCase()
                .contains(state.searchQuery.toLowerCase()) ??
            false)
        .toList();
  }

  List<SalesHistoryModel> getFilteredSalesHistory() {
    if (state.searchQuery.isEmpty) return state.salesHistory;
    return state.salesHistory
        .map((salesHistory) {
          final filteredProducts = salesHistory.products
              .where((product) =>
                  product.name
                      ?.toLowerCase()
                      .contains(state.searchQuery.toLowerCase()) ??
                  false)
              .toList();

          // Only return sales history entries that have matching products
          if (filteredProducts.isEmpty) return null;

          return salesHistory.copyWith(products: filteredProducts);
        })
        .where((salesHistory) => salesHistory != null)
        .cast<SalesHistoryModel>()
        .toList();
  }

  Future<void> deleteTemplate(int templateId) async {
    try {
      state = state.copyWith(
        deleteTemplateError: null,
        deleteTemplateSuccess: null,
      );
      await ref
          .read(getStoreRepositoryProvider)
          .deleteTemplateWarehouse(templateId);
      state = state.copyWith(
        templates: state.templates
            .where((template) => template.id != templateId)
            .toList(),
        deleteTemplateSuccess: 'Template deleted successfully',
      );
    } catch (e) {
      state = state.copyWith(
        deleteTemplateError: e.toString(),
        deleteTemplateSuccess: null,
      );
    }
  }
}
