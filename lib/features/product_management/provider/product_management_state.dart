import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/store/sales_history_model.dart';
import 'package:kitemite_app/model/response/store/template_model.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';

part 'product_management_state.freezed.dart';

@freezed
class ProductManagementState with _$ProductManagementState {
  const factory ProductManagementState({
    @Default([]) List<WarehouseModel> warehouseModel,
    @Default([]) List<TemplateModel> templates,
    @Default([]) List<SalesHistoryModel> salesHistory,
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingSalesHistory,
    @Default(false) bool isLoadingMoreSalesHistory,
    @Default(1) int salesHistoryCurrentPage,
    @Default(true) bool hasMoreSalesHistory,
    @Default('') String searchQuery,
    @Default(0) int selectedTabIndex,
    String? deleteTemplateError,
    String? deleteTemplateSuccess,
    String? errorMessage,
    String? salesHistoryError,
    String? highlightProductId,
    String? highlightOrderId,
  }) = _ProductManagementState;
}
