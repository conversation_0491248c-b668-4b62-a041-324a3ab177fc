import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/features/map_store/ui/detail_store/detail_store_screen.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';
import 'package:kitemite_app/model/response/store/nearby_store_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:url_launcher/url_launcher_string.dart';

class StoreCard extends ConsumerWidget {
  final NearbyStoreModel store;
  final LatLng userPosition;
  StoreCard({
    super.key,
    required this.store,
    required this.userPosition,
  });
  final List<Color> tagColors = [
    Colors.blue,
    Colors.cyan,
    Colors.green,
    Colors.orange,
    Colors.red
  ];

  Color getRandomColor(String tag) {
    final hash = tag.hashCode;
    return tagColors[hash.abs() % tagColors.length];
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tags = store.tags;
    return InkWell(
      onTap: () {
        context.push(RouterPaths.detailStore,
            extra: DetailStoreScreenArg(
              storeId: store.id ?? 0,
              userPosition: userPosition,
            ));
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (store.listImage.isNotEmpty)
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: store.listImage.map((image) {
                  // Lấy product tương ứng với image
                  final ProductModel? product = store.cabinets
                      ?.expand(
                          (cabinet) => cabinet.products ?? <ProductModel>[])
                      .firstWhereOrNull((product) => product.image == image);
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: GestureDetector(
                      onTap: product != null
                          ? () {
                              final mode = ref.watch(modeNotifierProvider).mode;
                              var isLoginWithAccount =
                                  (mode != ModeAccount.guest);

                              context.push(
                                RouterPaths.productDetail,
                                extra: ProductDetailScreenArg(
                                  productId: product.id!,
                                  isLoginWithAccount: isLoginWithAccount,
                                  isUpdateProduct: false,
                                ),
                              );
                            }
                          : null,
                      child: BaseCachedNetworkImage(
                        imageUrl: image,
                        width: 84,
                        height: 84,
                        fit: BoxFit.cover,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      store.name ?? "",
                      style: AppTextStyles.bold(16.sp,
                          color: AppColors.textPrimary),
                    ),
                    Text(
                      "${store.province ?? ""}, ${store.city ?? ""}, ${store.street ?? ""}",
                      style: AppTextStyles.regular(12.sp,
                          color: AppColors.textLightSecondary),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              IconButton(
                  onPressed: () {
                    _onDirections(
                        LatLng(double.parse(store.latitude ?? "0"),
                            double.parse(store.longitude ?? "0")),
                        adressStore: store.addressStoreFormatGGMap);
                  },
                  icon: const Icon(Icons.fork_right,
                      size: 24, color: AppColors.actionLightActive)),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              // Tags
              ...List.generate(tags.length, (index) {
                return Container(
                  margin: const EdgeInsets.only(right: 6),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: getRandomColor(tags[index]),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    tags[index],
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                  ),
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  void _onDirections(LatLng locationStation, {String? adressStore}) async {
    String url = "";
    if (Platform.isIOS) {
      url = AppConstants.urlGoogleMap2IOS(
          userPosition.latitude, userPosition.longitude, adressStore);
    } else {
      url = AppConstants.urlGoogleMap(
          userPosition.latitude,
          userPosition.longitude,
          locationStation.latitude,
          locationStation.longitude);
    }

    await canLaunchUrlString(url)
        ? await launchUrlString(url, mode: LaunchMode.externalApplication)
        : throw 'Could not launch $url';
  }
}
