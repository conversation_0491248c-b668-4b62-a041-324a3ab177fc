import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/service/location_service.dart';
import 'package:kitemite_app/features/map_store/ui/detail_shop_seller/detail_shop_seller.dart';
import 'package:kitemite_app/model/response/store/nearby_store_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class StoreCardSeller extends StatefulWidget {
  const StoreCardSeller(
      {super.key, required this.store, required this.userPosition});
  final NearbyStoreModel store;
  final LatLng userPosition;

  @override
  State<StoreCardSeller> createState() => _StoreCardSellerState();
}

class _StoreCardSellerState extends State<StoreCardSeller> {
  LatLng? currentLocation;
  NearbyStoreModel get store => widget.store;
  LatLng get userPosition => widget.userPosition;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getCurrentLocation();
    });
  }

  Future<void> _getCurrentLocation() async {
    currentLocation = await LocationService.getCurrentLocation();
  }

  String _calculateDistance() {
    if (store.latitude == null || store.longitude == null) return 'N/A';

    final distance = Geolocator.distanceBetween(
          userPosition.latitude,
          userPosition.longitude,
          double.parse(store.latitude!),
          double.parse(store.longitude!),
        ) /
        1000; // Convert to kilometers

    return '${distance.toStringAsFixed(1)} KM';
  }

  @override
  Widget build(BuildContext context) {
    return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(store.name ?? "",
                  style:
                      AppTextStyles.bold(16.sp, color: AppColors.textPrimary)),
              SizedBox(height: 8.h),
              Text(
                  "${store.province ?? ""}, ${store.city ?? ""}, ${store.street ?? ""}",
                  style: AppTextStyles.regular(12.sp,
                      color: AppColors.textLightSecondary)),
              SizedBox(height: 8.h),
              Text(
                  "毎日 ${store.open != null && store.open!.length >= 5 ? store.open!.substring(0, 5) : store.open ?? ""} 時から ${store.close != null && store.close!.length >= 5 ? store.close!.substring(0, 5) : store.close ?? ""} 時まで営業",
                  style: AppTextStyles.regular(12.sp,
                      color: AppColors.textLightSecondary)),
              SizedBox(height: 8.h),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.textLightSecondary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    child: Text(
                      _calculateDistance(),
                      style: AppTextStyles.regular(12.sp,
                          color: AppColors.textLightSecondary),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF5BE584),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    child: Text(
                      '空きあり ${store.availableShelvesCount ?? 0}',
                      style:
                          AppTextStyles.regular(12.sp, color: AppColors.white),
                    ),
                  )
                ],
              )
            ]),
          ),
          SizedBox(width: 16.w),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              minimumSize: const Size(0, 32),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () {
              context.push(RouterPaths.detailShopSeller,
                  extra: DetailShopSellerArg(
                    storeId: store.id ?? 0,
                    userPosition: userPosition,
                  ));
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text("ここで販売",
                    style: AppTextStyles.bold(14.sp,
                        color: AppColors.textPrimary)),
                SizedBox(width: 8.w),
                const Icon(Icons.arrow_forward_ios,
                    size: 12, color: AppColors.textPrimary)
              ],
            ),
          ),
        ]);
  }
}
