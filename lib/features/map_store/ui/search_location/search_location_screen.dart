import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/dropdown/common_dropdown_field.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/login/provider/provinces/province_notifier.dart';
import 'package:kitemite_app/features/map_store/provider/map_store/map_store_provider.dart';

class SearchLocationArg {
  final String? storeName;
  final String? openTime;
  final String? closeTime;
  final String? province;

  SearchLocationArg({
    this.storeName,
    this.openTime,
    this.closeTime,
    this.province,
  });
}

class SearchLocationScreen extends ConsumerStatefulWidget {
  const SearchLocationScreen({super.key, required this.args});
  final SearchLocationArg args;
  @override
  ConsumerState<SearchLocationScreen> createState() =>
      _SearchLocationScreenState();
}

class _SearchLocationScreenState extends ConsumerState<SearchLocationScreen> {
  final TextEditingController _storeNameController = TextEditingController();
  final TextEditingController _openTimeController = TextEditingController();
  final TextEditingController _closeTimeController = TextEditingController();

  String _selectedCity = '';
  List<String> _cities = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchProvinces();
      _storeNameController.text = widget.args.storeName ?? '';

      // Handle open time format
      if (widget.args.openTime != null && widget.args.openTime!.isNotEmpty) {
        final openTimeParts = widget.args.openTime!.split(':');
        if (openTimeParts.length == 3) {
          _openTimeController.text = '${openTimeParts[0]}:${openTimeParts[1]}';
        } else {
          _openTimeController.text = widget.args.openTime!;
        }
      }

      // Handle close time format
      if (widget.args.closeTime != null && widget.args.closeTime!.isNotEmpty) {
        final closeTimeParts = widget.args.closeTime!.split(':');
        if (closeTimeParts.length == 3) {
          _closeTimeController.text =
              '${closeTimeParts[0]}:${closeTimeParts[1]}';
        } else {
          _closeTimeController.text = widget.args.closeTime!;
        }
      }

      _selectedCity = widget.args.province ?? '';
    });
  }

  Future<void> _fetchProvinces() async {
    await ref.read(provinceNotifierProvider.notifier).getProvinces();
    final provinceState = ref.read(provinceNotifierProvider);
    if (provinceState.provinces != null) {
      setState(() {
        _cities =
            provinceState.provinces!.map((province) => province.name).toList();
      });
    }
  }

  void _resetFields() {
    final mapState = ref.watch(mapStoreNotifierProvider);
    setState(() {
      _storeNameController.clear();
      _openTimeController.clear();
      _closeTimeController.clear();
      _selectedCity = '';
    });

    ref.read(mapStoreNotifierProvider.notifier).resetSearchLocation();
    ref.read(mapStoreNotifierProvider.notifier).loadNearbyStoresSeller(
        role: "seller", isSearch: false, location: mapState.currentLocation);
  }

  Future<void> _selectTime(BuildContext context, bool isOpenTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final displayTime =
          '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      final updateTime = '$displayTime:00';
      setState(() {
        if (isOpenTime) {
          _openTimeController.text = displayTime;
        } else {
          _closeTimeController.text = displayTime;
        }
      });
    }
  }

  Widget _buildTimeField(
      {required String label,
      required TextEditingController controller,
      required VoidCallback onTap}) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: AbsorbPointer(
          child: InputTextField(
            textController: controller,
            hintText: label,
            label: label,
            maxLine: 1,
          ),
        ),
      ),
    );
  }

  bool _isValidTime(String openTime, String closeTime) {
    if (openTime.isEmpty || closeTime.isEmpty) return true;

    final openParts = openTime.split(':');
    final closeParts = closeTime.split(':');

    if (openParts.length != 3 || closeParts.length != 3) return true;

    final openHour = int.parse(openParts[0]);
    final openMinute = int.parse(openParts[1]);
    final closeHour = int.parse(closeParts[0]);
    final closeMinute = int.parse(closeParts[1]);

    if (openHour < closeHour) return true;
    if (openHour == closeHour && openMinute < closeMinute) return true;

    return false;
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
        resizeToAvoidBottomInset: false,
        appBar: CustomAppbar.basic(
          title: "販売所を検索",
          onTap: () {
            Navigator.pop(context);
          },
        ),
        body: SafeArea(
          top: false,
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InputTextField(
                    textController: _storeNameController,
                    hintText: "店舗名",
                    label: "店舗名",
                    maxLine: 1,
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      _buildTimeField(
                        label: "開始",
                        controller: _openTimeController,
                        onTap: () => _selectTime(context, true),
                      ),
                      const SizedBox(width: 24),
                      _buildTimeField(
                        label: "完了",
                        controller: _closeTimeController,
                        onTap: () => _selectTime(context, false),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  CommonDropdownField<String>(
                    label: "都道府県",
                    items: _cities,
                    value: _selectedCity.isEmpty ? null : _selectedCity,
                    itemLabel: (item) => item,
                    onChanged: (String? newValue) {
                      setState(() {
                        _selectedCity = newValue ?? "";
                      });
                    },
                  ),
                  const Spacer(),
                  CommonButton(
                    onPressed: () {
                      final openTime = _openTimeController.text.isEmpty
                          ? ''
                          : _openTimeController.text.length == 5
                              ? '${_openTimeController.text}:00'
                              : _openTimeController.text;
                      final closeTime = _closeTimeController.text.isEmpty
                          ? ''
                          : _closeTimeController.text.length == 5
                              ? '${_closeTimeController.text}:00'
                              : _closeTimeController.text;

                      if (!_isValidTime(openTime, closeTime)) {
                        context.showErrorSnackBar(
                            '開始時刻は終了時刻より前でなければなりません。');
                        return;
                      }

                      ref
                          .read(mapStoreNotifierProvider.notifier)
                          .updateSearchLocation(
                            _storeNameController.text,
                            openTime,
                            closeTime,
                            _selectedCity,
                          );
                      context.pop(true);
                    },
                    text: "検索",
                  ),
                  Center(
                    child: TextButton(
                      onPressed: _resetFields,
                      child: const Text("リセット",
                          style: TextStyle(color: Colors.black54)),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
