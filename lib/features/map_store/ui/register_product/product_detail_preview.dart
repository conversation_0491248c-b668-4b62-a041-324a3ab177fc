import 'package:flutter/material.dart';
import 'package:flutter_emoji/flutter_emoji.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/custom_coupon/coupon_painter.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/date_time_formatter.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/home/<USER>/home_seller/seller_home_provider.dart';
import 'package:kitemite_app/features/map_store/provider/handle_navigator_provider/handle_navigator_provider.dart';
import 'package:kitemite_app/features/map_store/provider/register_product/register_product_provider.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/register_product_screen.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/take_photo_product_preview.dart';
import 'package:kitemite_app/features/product_management/provider/product_management_provider.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class ProductDetailPreviewArg {
  final bool isUpdateProduct;
  final bool? isProductActive;
  ProductDetailPreviewArg(
      {required this.isUpdateProduct, this.isProductActive});
}

class ProductDetailPreview extends ConsumerStatefulWidget {
  const ProductDetailPreview({
    super.key,
    required this.arg,
  });
  final ProductDetailPreviewArg arg;

  @override
  ConsumerState<ProductDetailPreview> createState() =>
      _ProductDetailPreviewState();
}

class _ProductDetailPreviewState extends ConsumerState<ProductDetailPreview> {
  final List<Color> categoryColors = [
    const Color(0xFFD6E4FF),
    const Color(0xFFD8FBDE),
    const Color(0xFFCAFDF5),
    const Color(0xFFFFF5CC),
    const Color(0xFFFFE9D5),
  ];

  ProductModel get product =>
      ref.watch(registerProductNotifierProvider).previewProductModel ??
      const ProductModel();

  int? selectedImageIndex;
  int quantity = 1;

  @override
  void initState() {
    super.initState();
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          product.place?.name ?? "_", // tên cửa hàng
          style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
        ),
        Text(
          "${product.place?.province ?? ""}, ${product.place?.city ?? ""}, ${product.place?.street ?? ""}", // địa chỉ
          style:
              AppTextStyles.regular(14.sp, color: AppColors.textLightSecondary),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildMainImage() {
    return BaseCachedNetworkImage(
      imageUrl: selectedImageIndex != null &&
              product.images != null &&
              selectedImageIndex! < product.images!.length
          ? product.images![selectedImageIndex!].path ?? ''
          : product.image ?? '',
      width: double.infinity,
      height: 375.h,
      fit: BoxFit.cover,
      borderRadius: BorderRadius.circular(16),
    );
  }

  Widget _buildImageGallery() {
    return SizedBox(
      height: 55.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: product.images?.length ?? 0,
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) => Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: GestureDetector(
            onTap: () {
              setState(() {
                selectedImageIndex = index;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                border: selectedImageIndex == index
                    ? Border.all(color: AppColors.primary, width: 2)
                    : null,
                borderRadius: BorderRadius.circular(8),
              ),
              child: BaseCachedNetworkImage(
                imageUrl: product.images?[index].path ?? '',
                width: 55.w,
                height: 55.h,
                fit: BoxFit.cover,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDiscountBanner() {
    if (product.comment == null || product.comment == '')
      return const SizedBox.shrink();
    return CustomPaint(
      size: const Size(double.infinity, 40),
      painter: BannerPainterCustom(),
      child: SizedBox(
        width: double.infinity,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                product.comment ?? '_',
                style: AppTextStyles.bold(15.sp, color: AppColors.white),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceSection() {
    return Row(
      children: [
        Text(
          '販売価格',
          style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
        ),
        const Spacer(),
        Text(
          '${product.price?.priceString()}円',
          style: AppTextStyles.regular(14.sp,
              color: AppColors.textPrimary,
              decoration: TextDecoration.lineThrough),
        ),
        const SizedBox(width: 8),
        Text(
          '${product.salePrice?.priceString()}円',
          style: AppTextStyles.bold(20.sp, color: AppColors.primary),
        ),
      ],
    );
  }

  Widget _buildProductInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '冷凍庫-${product.cabinet?.cabinetCode ?? ''}: No.${product.shelf?.shelfCode ?? ''}',
              style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
            ),
            const Spacer(),
            Text(' 残り: ${product.quantity}',
                style: AppTextStyles.regular(
                  14.sp,
                  color: AppColors.textPrimary,
                )),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Text(
              '賞味期限 ',
              style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
            ),
            const Spacer(),
            Text(
                DateTimeFormatter.formatDate(product.expirationDate != null
                    ? DateTime.parse(product.expirationDate!)
                    : DateTime.now()),
                style: AppTextStyles.regular(
                  14.sp,
                  color: AppColors.textPrimary,
                )),
          ],
        ),
      ],
    );
  }

  Widget _buildStockWarning() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.mono20,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        product.note ?? '_',
        style:
            AppTextStyles.regular(12.sp, color: AppColors.textLightSecondary),
      ),
    );
  }

  Widget _buildProductDescription() {
    final parser = EmojiParser();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          'アピールポイント',
          style: AppTextStyles.bold(16.sp, color: AppColors.textPrimary),
        ),
        const SizedBox(height: 4),
        Text(parser.emojify(product.description ?? ''),
            style:
                AppTextStyles.regular(14.sp, color: const Color(0xFF5F5F5F))),
      ],
    );
  }

  Widget _listTag() {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      alignment: WrapAlignment.start,
      crossAxisAlignment: WrapCrossAlignment.start,
      children: List.generate(
        product.tag?.length ?? 0,
        (index) {
          final colorIndex = index % categoryColors.length;
          final color = categoryColors[colorIndex];

          return Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              product.tag?[index] ?? '',
              style: AppTextStyles.regular(12.sp, color: AppColors.textPrimary),
            ),
          );
        },
      ),
    );
  }

  _buildUpdateButton() {
    final registerProductState = ref.watch(registerProductNotifierProvider);
    final popToRouteUpdate = ref.watch(handleNavigatorNotifierProvider);
    print("000000000000000000 $popToRouteUpdate");
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(children: [
        registerProductState.isLoading
            ? const Center(child: CircularProgressIndicator())
            : CommonButton(
                text: widget.arg.isUpdateProduct
                    ? S.current.productDetailPreviewUpdateProduct
                    : S.current.productDetailPreviewPublishProduct,
                onPressed: () async {
                  final notifier =
                      ref.read(registerProductNotifierProvider.notifier);

                  if (widget.arg.isUpdateProduct) {
                    await notifier.updateProduct(product.id ?? 0);
                  } else {
                    await notifier.registerProduct();
                  }

                  if (!mounted) return;

                  final state = ref.read(registerProductNotifierProvider);

                  if (state.isSuccess ?? false) {
                    context.showSuccessSnackBar(widget.arg.isUpdateProduct
                        ? S.current.takePhotoProductUpdateSuccess
                        : S.current.takePhotoProductRegisterSuccess);

                    await Future.delayed(const Duration(milliseconds: 1000));
                    if (!mounted) return;

                    if (widget.arg.isUpdateProduct) {
                      notifier.resetState();

                      if (popToRouteUpdate == RouterPaths.detailStoreSeller) {
                        Navigator.of(context).popUntil((route) {
                          final name = route.settings.name;
                          return name == RouterPaths.detailStoreSeller;
                        });
                      } else {
                        ref
                            .read(productManagementNotifierProvider.notifier)
                            .fetchInitialData();
                        context.go(RouterPaths.business);
                      }
                    } else {
                      context.go(RouterPaths.home, extra: true);
                      ref
                          .read(sellerHomeNotifierProvider.notifier)
                          .loadWarehouses();
                      notifier.resetState();
                    }
                  } else if (state.errorMessage != null) {
                    context.showErrorSnackBar(state.errorMessage!);
                  }
                },
              ),
        const SizedBox(
          height: 16,
        ),
        GestureDetector(
            onTap: () {
              context.push(RouterPaths.registerProductScreen,
                  extra: RegisterProductArg(
                      shelfId: product.shelf?.id ?? 0,
                      shelfCode: product.shelf?.shelfCode ?? '',
                      cabinetCode: product.cabinet?.cabinetCode ?? '',
                      product: product,
                      backFromPreview: true,
                      sizeBox: registerProductState.request?.capacity ?? "",
                      isProductActive: widget.arg.isProductActive));
            },
            child: Text(
              S.current.productDetailPreviewBackToEdit,
              style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
            )),
        const SizedBox(
          height: 16,
        ),
        GestureDetector(
            onTap: () {
              context.push(RouterPaths.takePhotoProductPreview,
                  extra: TakePhotoProductPreviewArg(
                      product: product, isUpdateMode: false));
            },
            child: Text(
              S.current.productDetailPreviewRetakePhotos,
              style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
            )),
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
        appBar: CustomAppbar.basic(
          title: product.name ?? '',
          onTap: () => context.pop(),
        ),
        body: SafeArea(
            top: false,
            child: Column(children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Padding(
                      //   padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      //   child: _buildHeader(),
                      // ),
                      SizedBox(height: 12.h),
                      _buildMainImage(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(height: 12.h),
                            _buildImageGallery(),
                            SizedBox(height: 12.h),
                            _buildDiscountBanner(),
                            SizedBox(height: 12.h),
                            _buildPriceSection(),
                            SizedBox(height: 12.h),
                            _buildProductInfo(),
                            if (product.note?.isNotEmpty == true) ...[
                              SizedBox(height: 16.h),
                              _buildStockWarning(),
                            ],
                            SizedBox(height: 12.h),
                            _buildProductDescription(),
                            SizedBox(height: 12.h),
                            const Divider(
                              color: AppColors.mono20,
                              thickness: 1,
                            ),
                            SizedBox(height: 12.h),
                            _listTag(),
                            SizedBox(height: 12.h),
                            const Divider(
                              color: AppColors.mono20,
                              thickness: 1,
                            ),
                            SizedBox(height: 8.h),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              _buildUpdateButton(),
              SizedBox(height: 8.h),
            ])));
  }
}
