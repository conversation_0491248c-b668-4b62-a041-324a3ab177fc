import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/tags/string_tag.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/date_time_formatter.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/map_store/provider/register_product/register_product_provider.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/product_detail_preview.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/take_photo_product.dart';
import 'package:kitemite_app/features/product_management/provider/product_management_provider.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/model/request/product/register_product_request.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';
import 'package:kitemite_app/model/response/store/template_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class RegisterProductArg {
  final int shelfId;
  final String shelfCode;
  final String cabinetCode;
  final ProductModel? product;
  final bool backFromPreview;
  final bool? isProductActive;
  final String? sizeBox;
  RegisterProductArg(
      {required this.shelfId,
      required this.shelfCode,
      required this.cabinetCode,
      this.product,
      this.backFromPreview = false,
      this.sizeBox,
      this.isProductActive});
}

class RegisterProductScreen extends ConsumerStatefulWidget {
  const RegisterProductScreen({super.key, required this.args});
  final RegisterProductArg args;

  @override
  ConsumerState<RegisterProductScreen> createState() =>
      _RegisterProductScreenState();
}

class _RegisterProductScreenState extends ConsumerState<RegisterProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController dateController = TextEditingController();
  final TextEditingController productNameController = TextEditingController();
  final TextEditingController salesPriceController = TextEditingController();
  final TextEditingController listPriceController = TextEditingController();
  final TextEditingController sizeController = TextEditingController();
  final TextEditingController inventoryController = TextEditingController();
  final TextEditingController commentController = TextEditingController();
  final TextEditingController noteController = TextEditingController();
  final TextEditingController productDetailsController =
      TextEditingController();
  final CurrencyTextInputFormatter _formatter =
      CurrencyTextInputFormatter.currency(
          locale: 'ja', symbol: '円', customPattern: '#,##0\u00a4');
  final _tagsController = TextEditingController();
  List<String> _tags = [];
  List<String> _templateNames = [];
  List<TemplateModel> _templates = [];
  String? selectedDropdownValue;
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTemplates();
    });
    // If product is not null, this is an update operation
    if (widget.args.product != null) {
      final product = widget.args.product!;

      // Populate controllers with existing product data
      productNameController.text = product.name ?? '';

      // Format price values with the currency formatter
      if (product.salePrice != null) {
        salesPriceController.text =
            '${product.salePrice.toString().priceString()}円';
      }
      if (product.price != null) {
        listPriceController.text = '${product.price.toString().priceString()}円';
      }

      sizeController.text =
          product.capacity != null ? '${product.capacity} mm' : '';
      inventoryController.text = product.quantity?.toString() ?? '';
      dateController.text = product.expirationDate != null
          ? DateTimeFormatter.formatDate(
              DateTime.parse(product.expirationDate!))
          : '';
      commentController.text = product.comment ?? '';
      noteController.text = product.note ?? '';
      productDetailsController.text = product.description ?? '';

      // Set tags if available
      if (product.tag != null && product.tag!.isNotEmpty) {
        _tags = List<String>.from(product.tag!);
        _tagsController.text = _tags.join(',');
      }
    } else {
      // // Set default size from sizeBox if available
      // if (widget.args.sizeBox != null) {
      //   sizeController.text = '${widget.args.sizeBox} mm';
      // }
    }
  }

  Future<void> _loadTemplates() async {
    final templates = ref.read(productManagementNotifierProvider).templates;
    if (templates.isEmpty) {
      await ref
          .read(productManagementNotifierProvider.notifier)
          .fetchInitialData();
      final updatedTemplates =
          ref.read(productManagementNotifierProvider).templates;
      setState(() {
        _templateNames =
            updatedTemplates.map((template) => template.name ?? '').toList();
        _templates = updatedTemplates;
      });
    } else {
      setState(() {
        _templateNames =
            templates.map((template) => template.name ?? '').toList();
        _templates = templates;
      });
    }
  }

  @override
  void dispose() {
    productNameController.dispose();
    dateController.dispose();
    productNameController.dispose();
    salesPriceController.dispose();
    listPriceController.dispose();
    sizeController.dispose();
    inventoryController.dispose();
    commentController.dispose();
    noteController.dispose();
    productDetailsController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)),
    );
    if (picked != null) {
      setState(() {
        dateController.text = DateFormat('yyyy/MM/dd').format(picked);
      });
    }
  }

  String? _validateSalesPrice(String? value) {
    if (value == null || value.isEmpty) {
      return S.current.registerProductValidationSalesPriceRequired;
    }
    return null;
  }

  String? _validateListPrice(String? value) {
    if (value == null || value.isEmpty) {
      return S.current.registerProductValidationListPriceRequired;
    }
    return null;
  }

  // Format date from input format (yyyy/MM/dd) to ISO format (yyyy-MM-dd)
  String _formatDateForApi(String inputDate) {
    try {
      // Split the date components
      final parts = inputDate.split('/');
      if (parts.length == 3) {
        // Reconstruct in ISO format
        return '${parts[0]}-${parts[1]}-${parts[2].split('.').first.trim()}';
      }
      return inputDate; // Return original if can't parse
    } catch (e) {
      debugPrint('Error formatting date: $e');
      return inputDate; // Return original if any error
    }
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      // Validate tags
      final tags =
          _tagsController.text.split(',').map((e) => e.trim()).toList();

      // Set tags to null if empty
      final finalTags =
          tags.isEmpty || (tags.length == 1 && tags[0].isEmpty) ? null : tags;

      // Validate tag length only if tags are not null
      if (finalTags != null) {
        for (final tag in finalTags) {
          if (tag.length > 8) {
            context.showErrorSnackBar('8文字以内で入力してください。');
            return;
          }
        }
      }

      // Parse price values from text controllers
      final salesPrice = double.tryParse(
              salesPriceController.text.replaceAll(RegExp(r'[^\d]'), '')) ??
          0.0;
      final listPrice = double.tryParse(
              listPriceController.text.replaceAll(RegExp(r'[^\d]'), '')) ??
          0.0;

      // Validate sales price is not greater than list price
      if (salesPrice > listPrice) {
        context.showErrorSnackBar('定価より販売価額が高く入力されています。');
        return;
      }

      // Lấy request từ state
      final stateRequest = ref.read(registerProductNotifierProvider).request;

      // Tạo request mới nếu state không có hoặc cập nhật request hiện tại
      var request = stateRequest != null
          ? stateRequest.copyWith(
              shelfId: widget.args.shelfId,
              name: productNameController.text,
              description: productDetailsController.text,
              comment: commentController.text.trim(),
              note: noteController.text.trim(),
              quantity: int.parse(inventoryController.text),
              expirationDate: dateController.text,
              capacity: sizeController.text.replaceAll(' mm', ''),
              price: listPrice,
              salePrice: salesPrice,
              tag: finalTags,
            )
          : RegisterProductRequest(
              shelfId: widget.args.shelfId,
              name: productNameController.text,
              description: productDetailsController.text,
              comment: commentController.text.trim(),
              note: noteController.text.trim(),
              quantity: int.parse(inventoryController.text),
              expirationDate: dateController.text,
              capacity: sizeController.text.replaceAll(' mm', ''),
              price: listPrice,
              salePrice: salesPrice,
              img: "",
              tag: finalTags,
              images: [],
            );

      var previewProductModel =
          ref.read(registerProductNotifierProvider).previewProductModel;

      ProductModel previewProductModelNew;

      if (previewProductModel == null) {
        // Create a new model if it doesn't exist
        previewProductModelNew = ProductModel(
          name: request.name,
          description: request.description,
          quantity: request.quantity,
          price: request.price.toString(),
          salePrice: request.salePrice.toString(),
          comment: request.comment,
          expirationDate: request.expirationDate != null
              ? _formatDateForApi(request.expirationDate!)
              : null,
          capacity: request.capacity,
          tag: request.tag,
          note: request.note,
          cabinet: CabinetModel(cabinetCode: widget.args.cabinetCode),
          shelf: ShelfModel(
              shelfCode: widget.args.shelfCode, id: widget.args.shelfId),
        );
      } else {
        // Use copyWith on existing model
        previewProductModelNew = previewProductModel.copyWith(
          name: request.name,
          description: request.description,
          quantity: request.quantity,
          price: request.price.toString(),
          salePrice: request.salePrice.toString(),
          comment: request.comment,
          expirationDate: request.expirationDate != null
              ? _formatDateForApi(request.expirationDate!)
              : null,
          capacity: request.capacity,
          tag: request.tag,
          note: request.note,
          cabinet: previewProductModel.cabinet != null
              ? previewProductModel.cabinet!
                  .copyWith(cabinetCode: widget.args.cabinetCode)
              : CabinetModel(cabinetCode: widget.args.cabinetCode),
          shelf: previewProductModel.shelf != null
              ? previewProductModel.shelf!.copyWith(
                  shelfCode: widget.args.shelfCode, id: widget.args.shelfId)
              : ShelfModel(
                  shelfCode: widget.args.shelfCode, id: widget.args.shelfId),
        );
      }

      ref
          .read(registerProductNotifierProvider.notifier)
          .updatePreviewProductModel(previewProductModelNew);

      // Check if this is an update operation
      ref
          .read(registerProductNotifierProvider.notifier)
          .updateRegisterProductRequest(request);
      if (widget.args.product?.images != null) {
        final productImageModels = widget.args.product?.images
            ?.map((img) => ProductImageRegister(path: img.path ?? ""))
            .toList();
        request = request.copyWith(
            images: productImageModels!, img: widget.args.product?.image ?? "");
      }

      ref
          .read(registerProductNotifierProvider.notifier)
          .updateRegisterProductRequestUpdate(request);
      if (widget.args.product != null) {
        // Update existing product
        if (!widget.args.backFromPreview) {
          previewProductModelNew = previewProductModelNew.copyWith(
              id: widget.args.product?.id,
              image: widget.args.product?.image,
              images: widget.args.product?.images);
          ref
              .read(registerProductNotifierProvider.notifier)
              .updatePreviewProductModel(previewProductModelNew);
          context.push(
            RouterPaths.productDetailPreview,
            extra: ProductDetailPreviewArg(
                isUpdateProduct: true,
                isProductActive: widget.args.isProductActive),
          );
        }
      } else {
        // Register new product

        if (!widget.args.backFromPreview) {
          context
              .push(RouterPaths.takePhotoProduct,
                  extra: TakePhotoProductArg(
                    product: null,
                    isProductActive: widget.args.isProductActive,
                  ))
              .then((value) {});
        }
      }
      if (widget.args.backFromPreview) {
        context.pop();
        return;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final registerProductState = ref.watch(registerProductNotifierProvider);
    final isUpdateMode = widget.args.product != null;

    final productManagementState = ref.watch(productManagementNotifierProvider);

    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: isUpdateMode
            ? S.current.registerProductUpdateTitle
            : S.current.registerProductTitle,
        onTap: () {
          context.pop();
        },
      ),
      body: productManagementState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : productManagementState.errorMessage != null
              ? ErrorCommonWidget(
                  error: productManagementState.errorMessage!,
                  onPressed: () {
                    _loadTemplates();
                  },
                )
              : SafeArea(
                  top: false,
                  child: GestureDetector(
                    onTap: () {
                      FocusManager.instance.primaryFocus?.unfocus();
                    },
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildTagRow(widget.args.cabinetCode,
                                  widget.args.shelfCode),
                              const SizedBox(height: 24),
                              _buildSuggestionBox(),
                              const SizedBox(height: 24),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: InputTextField(
                                      textController: salesPriceController,
                                      hintText:
                                          S.current.registerProductSalesPrice,
                                      label:
                                          S.current.registerProductSalesPrice,
                                      maxLine: 1,
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [_formatter],
                                      validator: _validateSalesPrice,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: InputTextField(
                                      textController: listPriceController,
                                      hintText:
                                          S.current.registerProductListPrice,
                                      label: S.current.registerProductListPrice,
                                      maxLine: 1,
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [_formatter],
                                      validator: _validateListPrice,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Padding(
                                padding: const EdgeInsets.only(left: 12),
                                child: Text(
                                  S.current.registerProductIncludingTax,
                                  style: AppTextStyles.regular(12.sp,
                                      color: AppColors.textLightSecondary),
                                ),
                              ),
                              const SizedBox(height: 24),
                              InputTextField(
                                textController: sizeController,
                                inputFormatters: [
                                  DimensionInputFormatter(),
                                ],
                                keyboardType: TextInputType.text,
                                hintText: "71*80*122",
                                label: S.current.registerProductSize,
                                maxLine: 1,
                                onChanged: (value) {
                                  // Remove 'mm' suffix for validation
                                  final text = value.replaceAll(' mm', '');
                                  final parts = text.split('*');
                                  if (widget.args.sizeBox != null) {
                                    final sizeBoxParts =
                                        widget.args.sizeBox!.split('*');
                                    if (sizeBoxParts.length == 3) {
                                      for (int i = 0; i < 3; i++) {
                                        final inputSize = int.parse(parts[i]);
                                        final boxSize =
                                            int.parse(sizeBoxParts[i]);
                                        if (inputSize > boxSize) {
                                          context.showErrorSnackBar(
                                              '棚のサイズより商品が大きい可能性があります');
                                        }
                                      }
                                    }
                                  }
                                },
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'サイズを入力してください。';
                                  }

                                  // Remove 'mm' suffix for validation
                                  final text = value.replaceAll(' mm', '');
                                  final parts = text.split('*');

                                  if (parts.length != 3) {
                                    return '3つの寸法を入力してください (例: 10*10*10)';
                                  }

                                  // Check if all parts are valid numbers
                                  for (final part in parts) {
                                    if (part.isEmpty ||
                                        int.tryParse(part) == null) {
                                      return '有効な数値を入力してください';
                                    }
                                  }

                                  // Check against sizeBox if available and show toast instead of validation error
                                  if (widget.args.sizeBox != null) {
                                    final sizeBoxParts =
                                        widget.args.sizeBox!.split('*');
                                    if (sizeBoxParts.length == 3) {
                                      for (int i = 0; i < 3; i++) {
                                        final inputSize = int.parse(parts[i]);
                                        final boxSize =
                                            int.parse(sizeBoxParts[i]);
                                        if (inputSize > boxSize) {
                                          context.showErrorSnackBar(
                                              '棚のサイズより商品が大きい可能性があります');
                                        }
                                      }
                                    }
                                  }

                                  return null;
                                },
                              ),
                              const SizedBox(height: 8),
                              Padding(
                                padding: const EdgeInsets.only(left: 12),
                                child: Text("幅と奥行と高さの間は[*(半角アスタリスク)]で区切ってください",
                                    style: AppTextStyles.regular(12.sp,
                                        color: AppColors.textLightSecondary)),
                              ),
                              const SizedBox(height: 24),
                              InputTextField(
                                textController: inventoryController,
                                hintText: S.current.registerProductInventory,
                                label: S.current.registerProductInventory,
                                maxLine: 1,
                                keyboardType: TextInputType.number,
                                enabled:
                                    !(widget.args.isProductActive ?? false),
                                fillColor:
                                    (widget.args.isProductActive ?? false)
                                        ? Colors.grey[200]
                                        : null,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                ],
                                onChanged: (value) {
                                  if (value.isNotEmpty &&
                                      int.tryParse(value) == null) {
                                    context.showErrorSnackBar('数値のみ入力可能です。');
                                    // Reset to previous valid value or empty
                                    inventoryController.text = '';
                                    inventoryController.selection =
                                        TextSelection.fromPosition(
                                      TextPosition(
                                          offset:
                                              inventoryController.text.length),
                                    );
                                  }
                                },
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return '在庫数を入力してください。';
                                  }
                                  if (int.tryParse(value.trim()) == null) {
                                    return '数値のみ入力可能です。';
                                  }
                                  if (int.parse(value.trim()) == 0) {
                                    return '在庫数量は0より大きくなければなりません';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 24),
                              GestureDetector(
                                  onTap: (widget.args.isProductActive ?? false)
                                      ? null
                                      : () => _selectDate(context),
                                  child: InputTextField(
                                    textController: dateController,
                                    hintText:
                                        S.current.registerProductExpirationDate,
                                    label:
                                        S.current.registerProductExpirationDate,
                                    maxLine: 1,
                                    enabled: false,
                                    fillColor:
                                        (widget.args.isProductActive ?? false)
                                            ? Colors.grey[200]
                                            : null,
                                    suffixIcon:
                                        const Icon(Icons.calendar_today),
                                    validator: (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        return '賞味期限を入力してください。';
                                      }
                                      return null;
                                    },
                                  )),
                              const SizedBox(height: 24),
                              InputTextField(
                                textController: commentController,
                                hintText: "一言コメント",
                                label: "一言コメント",
                                maxLine: 1,
                                validator: (value) {
                                  if (value == null) {
                                    return null;
                                  }
                                  if (value.isNotEmpty &&
                                      value.trim().isEmpty) {
                                    return '一言コメントを入力してください。';
                                  }
                                  if (value.trim().length > 20) {
                                    return '20文字以内で入力してください。';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 24),
                              InputTextField(
                                textController: noteController,
                                hintText: S.current.registerProductNote,
                                label: S.current.registerProductNote,
                                maxLine: 3,
                                validator: (value) {
                                  if (value == null) {
                                    return null;
                                  }
                                  if (value.isNotEmpty &&
                                      value.trim().isEmpty) {
                                    return 'Noteを入力してください。';
                                  }
                                  if (value.trim().length > 100) {
                                    return '100文字以内で入力してください。';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 24),
                              InputTextField(
                                textController: productDetailsController,
                                hintText: S.current.registerProductDetails,
                                label: S.current.registerProductDetails,
                                maxLine: 5,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'アピール文章を入力してください。';
                                  }
                                  if (value.trim().length > 1000) {
                                    return '1000文字以内で入力してください。';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 24),
                              StringTags(
                                label: "タグ",
                                hintText: "タグ",
                                initialTags: _tags,
                                onTagsChanged: (tags) {
                                  _tags = tags;
                                  _tagsController.text = tags.join(',');
                                },
                                tagColor: const Color(0xFFF5F6F9),
                                tagTextColor: AppColors.textPrimary,
                                validator: (value) {
                                  if (value != null &&
                                      value.trim().isNotEmpty &&
                                      value.trim().length > 8) {
                                    return '8文字以内で入力してください。';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 24),
                              _buildWarningBox(
                                  S.current.registerProductWarning),
                              const SizedBox(height: 12),
                              if (registerProductState.isLoading)
                                const Center(child: CircularProgressIndicator())
                              else
                                CommonButton(
                                  onPressed: _submitForm,
                                  text: isUpdateMode
                                      ? S.current.registerProductUpdate
                                      : S.current.registerProductContinue,
                                ),
                              Center(
                                child: TextButton(
                                    onPressed: () {
                                      if (!isUpdateMode) {
                                        ref
                                            .read(
                                                registerProductNotifierProvider
                                                    .notifier)
                                            .resetState();
                                      }
                                      context.pop();
                                    },
                                    child: Text(
                                      S.current.registerProductCancel,
                                      style: AppTextStyles.bold(15.sp,
                                          color: AppColors.textLightSecondary),
                                    )),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
    );
  }

  Widget _buildTagRow(String cabinetCode, String shelfCode) {
    return Row(
      children: [
        Text(
          "冷凍庫-$cabinetCode",
          style: AppTextStyles.bold(14.sp, color: AppColors.textPrimary),
        ),
        const SizedBox(width: 12),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppColors.mono40,
              width: 1,
            ),
          ),
          child: Text(
            "No.$shelfCode",
            style: AppTextStyles.regular(12.sp, color: AppColors.textPrimary),
          ),
        )
      ],
    );
  }

  Widget _buildWarningBox(String text) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF5CC),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(text,
          style: AppTextStyles.regular(12.sp, color: AppColors.textPrimary)),
    );
  }

  Widget _buildSuggestionBox() {
    return RawAutocomplete<String>(
      focusNode: FocusNode(),
      textEditingController: productNameController,
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text.isEmpty) {
          return const Iterable<String>.empty();
        }
        final matchingTemplates = _templateNames.where((suggestion) =>
            suggestion
                .toLowerCase()
                .contains(textEditingValue.text.toLowerCase()));
        return [textEditingValue.text, ...matchingTemplates];
      },
      onSelected: (String selection) {
        setState(() {
          productNameController.text = selection;
          if (_templateNames.contains(selection)) {
            productDetailsController.text = _templates
                    .firstWhere((template) => template.name == selection)
                    .description ??
                '';
            listPriceController.text =
                '${_templates.firstWhere((template) => template.name == selection).basePrice.toString().priceString()}円';
            ref
                .read(registerProductNotifierProvider.notifier)
                .setSelectedImageTemplate(_templates
                        .firstWhere((template) => template.name == selection)
                        .baseImg ??
                    '');
            ref
                .read(registerProductNotifierProvider.notifier)
                .setIsCreateTemplate(false);
          } else {
            ref
                .read(registerProductNotifierProvider.notifier)
                .setIsCreateTemplate(true);
          }
        });
      },
      fieldViewBuilder: (context, controller, focusNode, onFieldSubmitted) {
        return InputTextField(
          textController: controller,
          focusNode: focusNode,
          hintText: "商品名",
          label: "商品名",
          maxLine: 1,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '商品名を入力してください。';
            }
            if (value.trim().length > 30) {
              return '30文字以内で入力してください。';
            }
            return null;
          },
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Material(
          elevation: 4,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.3,
            ),
            padding: const EdgeInsets.only(right: 16),
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              itemCount: options.length,
              itemBuilder: (context, index) {
                final option = options.elementAt(index);
                final isAddNew = index == 0;

                return InkWell(
                  onTap: () => onSelected(option),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color:
                          isAddNew ? AppColors.primary.withOpacity(0.1) : null,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.withOpacity(0.2)),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            isAddNew
                                ? S.current.registerProductAddNew(option)
                                : option,
                            style: AppTextStyles.regular(12.sp,
                                color: isAddNew
                                    ? AppColors.primary
                                    : AppColors.textPrimary),
                          ),
                        ),
                        if (!isAddNew)
                          const Icon(Icons.radio_button_unchecked,
                              color: Colors.grey),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}

class DimensionInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Allow backspace
    if (newValue.text.length < oldValue.text.length) {
      return newValue;
    }

    // Get the new text without 'mm' suffix
    String text = newValue.text.replaceAll(' mm', '');

    // If text is empty, return empty
    if (text.isEmpty) {
      return newValue;
    }

    // Validate format: should be like "10*10*10"
    final parts = text.split('*');
    if (parts.length > 3) {
      // If more than 3 parts, keep only first 3 parts
      text = parts.sublist(0, 3).join('*');
    }

    // Add 'mm' suffix
    final formattedText = '$text mm';

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}
