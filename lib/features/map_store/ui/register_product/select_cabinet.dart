import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/features/map_store/provider/info_cabinet/info_cabinet_provider.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/register_product_screen.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/model/response/store/store_detail_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

extension ListExtension<T> on List<T> {
  Map<K, List<T>> groupBy<K>(K Function(T) keyFunction) {
    final map = <K, List<T>>{};
    for (final element in this) {
      final key = keyFunction(element);
      map.putIfAbsent(key, () => []).add(element);
    }
    return map;
  }
}

class StorageSelectionScreenArg {
  final int storeId;
  final int? cabinetId;
  StorageSelectionScreenArg({
    required this.storeId,
    this.cabinetId,
  });
}

class StorageSelectionScreen extends ConsumerStatefulWidget {
  const StorageSelectionScreen({super.key, required this.arg});
  final StorageSelectionScreenArg arg;

  @override
  ConsumerState<StorageSelectionScreen> createState() =>
      _StorageSelectionScreenState();
}

class _StorageSelectionScreenState
    extends ConsumerState<StorageSelectionScreen> {
  int? selectedBoxId;
  String? selectedCabinetCode;
  String? selectedShelfCode;
  StoreShelfModel? selectedSheft;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadStoreDetail();
    });
  }

  Future<void> _loadStoreDetail() async {
    await ref
        .read(infoCabinetNotifierProvider.notifier)
        .loadStoreDetail(widget.arg.storeId, 'seller');
  }

  void _onBoxSelected(String cabinetCode, int shelfId, String shelfCode) {
    setState(() {
      if (selectedBoxId == shelfId &&
          selectedCabinetCode == cabinetCode &&
          selectedShelfCode == shelfCode) {
        selectedBoxId = null;
        selectedShelfCode = null;
        selectedCabinetCode = null;
      } else {
        selectedBoxId = shelfId;
        selectedShelfCode = shelfCode;
        selectedCabinetCode = cabinetCode;
      }
    });
  }

  Color _getBoxColor(StoreShelfStatus status, int shelfId, String cabinetCode) {
    if (selectedBoxId == shelfId && selectedCabinetCode == cabinetCode)
      return Colors.black;
    switch (status) {
      case StoreShelfStatus.registered:
        return Colors.yellow.withOpacity(0.3);
      case StoreShelfStatus.booked:
        return Colors.orange.withOpacity(0.3);
      case StoreShelfStatus.available:
      default:
        return Colors.white;
    }
  }

  @override
  Widget build(BuildContext context) {
    final storeState = ref.watch(infoCabinetNotifierProvider);

    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: S.current.selectCabinetTitle,
        onTap: () {
          context.pop(context);
        },
      ),
      body: SafeArea(
          top: false,
          maintainBottomViewPadding: false,
          child: storeState.isLoading
              ? const Center(child: CircularProgressIndicator())
              : storeState.error != null
                  ? ErrorCommonWidget(
                      error: storeState.error!,
                      onPressed: () {
                        ref
                            .read(infoCabinetNotifierProvider.notifier)
                            .loadStoreDetail(widget.arg.storeId, 'seller');
                      },
                    )
                  : SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 24),
                      child: Column(
                        children: [
                          const LegendWidget(),
                          const SizedBox(height: 24),
                          if (storeState.store?.cabinets != null)
                            SizedBox(
                              width: double.infinity,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: storeState.store!.cabinets!
                                    .where((cabinet) {
                                  if (widget.arg.cabinetId != null) {
                                    return cabinet.id == widget.arg.cabinetId;
                                  }
                                  return true;
                                }).map((cabinet) {
                                  if (cabinet.shelves?.length == 0) {
                                    return const SizedBox.shrink();
                                  }

                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("${cabinet.cabinetCode}",
                                          style: AppTextStyles.bold(14.sp,
                                              color: AppColors.textPrimary)),
                                      const SizedBox(height: 8),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: () {
                                          final groupedShelves = cabinet.shelves
                                              ?.groupBy((shelf) => shelf.stage);
                                          if (groupedShelves == null)
                                            return <Widget>[];
                                          final sortedEntries =
                                              groupedShelves.entries.toList()
                                                ..sort((a, b) => (a.key ?? 0)
                                                    .compareTo(b.key ?? 0));
                                          return sortedEntries
                                              .map<Widget>((stageGroup) {
                                            final stage = stageGroup.key;
                                            final shelves = stageGroup.value;
                                            return Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "$stage 段目",
                                                  style: AppTextStyles.bold(
                                                      12.sp,
                                                      color: AppColors
                                                          .textLightSecondary),
                                                ),
                                                const SizedBox(height: 4),
                                                Wrap(
                                                  spacing: 8,
                                                  runSpacing: 8,
                                                  children:
                                                      shelves.map((shelf) {
                                                    return GestureDetector(
                                                      onTap: shelf.statusEnum ==
                                                              StoreShelfStatus
                                                                  .available
                                                          ? () {
                                                              _onBoxSelected(
                                                                  cabinet.cabinetCode ??
                                                                      '',
                                                                  shelf.id ?? 0,
                                                                  shelf.shelfCode ??
                                                                      '');

                                                              setState(() {
                                                                selectedSheft =
                                                                    shelf;
                                                              });
                                                            }
                                                          : null,
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                horizontal: 8,
                                                                vertical: 8),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: _getBoxColor(
                                                              shelf.statusEnum,
                                                              shelf.id ?? 0,
                                                              cabinet.cabinetCode ??
                                                                  ''),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(12),
                                                          border: shelf
                                                                      .statusEnum ==
                                                                  StoreShelfStatus
                                                                      .available
                                                              ? Border.all(
                                                                  color: AppColors
                                                                      .mono40)
                                                              : null,
                                                        ),
                                                        child: Text(
                                                          "No.${shelf.shelfCode}",
                                                          style: AppTextStyles
                                                              .regular(
                                                            12.sp,
                                                            color: selectedBoxId ==
                                                                        shelf
                                                                            .id &&
                                                                    selectedCabinetCode ==
                                                                        cabinet
                                                                            .cabinetCode
                                                                ? Colors.white
                                                                : AppColors
                                                                    .textPrimary,
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  }).toList(),
                                                ),
                                                const SizedBox(height: 8),
                                              ],
                                            );
                                          }).toList();
                                        }(),
                                      ),
                                      const SizedBox(height: 24),
                                    ],
                                  );
                                }).toList(),
                              ),
                            ),
                        ],
                      ),
                    )),
      bottom: selectedBoxId != null
          ? SafeArea(
              maintainBottomViewPadding: false,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: CommonButton(
                  text: S.current.selectCabinetContinue,
                  onPressed: () {
                    context.push(RouterPaths.registerProductScreen,
                        extra: RegisterProductArg(
                            shelfId: selectedBoxId ?? 0,
                            shelfCode: selectedShelfCode ?? '',
                            cabinetCode: selectedCabinetCode ?? '',
                            sizeBox:
                                "${selectedSheft?.width ?? 0}*${selectedSheft?.depth ?? 0}*${selectedSheft?.height ?? 0}"));
                  },
                ),
              ),
            )
          : null,
    );
  }
}

class LegendWidget extends StatelessWidget {
  const LegendWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Flexible(
          child: _buildLegendItem(
            border: Border.all(color: AppColors.mono20, width: 2),
            color: Colors.transparent,
            text: S.current.selectCabinetAvailable,
          ),
        ),
        const SizedBox(width: 16),
        Flexible(
          child: _buildLegendItem(
            color: Colors.yellow.withOpacity(0.3),
            text: S.current.selectCabinetRegistered,
          ),
        ),
        const SizedBox(width: 16),
        _buildLegendItem(
          color: Colors.orange.withOpacity(0.3),
          text: S.current.selectCabinetBooked,
        ),
      ],
    );
  }

  Widget _buildLegendItem({
    Color color = Colors.transparent,
    Border? border,
    required String text,
  }) {
    return Row(
      children: [
        Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
            border: border,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          text,
          style: AppTextStyles.regular(12.sp, color: AppColors.textPrimary),
        )
      ],
    );
  }
}
