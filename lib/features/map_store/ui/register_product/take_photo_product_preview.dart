import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/image_picker/common_image_picker.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/map_store/provider/register_product/register_product_provider.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/model/request/product/register_product_request.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class TakePhotoProductPreviewArg {
  final ProductModel product;
  final bool isUpdateMode;
  TakePhotoProductPreviewArg({
    required this.product,
    required this.isUpdateMode,
  });
}

class TakePhotoProductPreview extends ConsumerStatefulWidget {
  const TakePhotoProductPreview({super.key, this.arg});
  final TakePhotoProductPreviewArg? arg;

  @override
  ConsumerState<TakePhotoProductPreview> createState() =>
      _TakePhotoProductPreviewState();
}

class _TakePhotoProductPreviewState
    extends ConsumerState<TakePhotoProductPreview> {
  final List<XFile> _images = [];
  final CommonImagePicker _imagePicker = CommonImagePicker();
  int _currentIndex = 0;
  bool _isUpdateMode = false;
  final List<String> _existingImageUrls = [];

  @override
  void initState() {
    super.initState();
    _isUpdateMode = widget.arg?.isUpdateMode ?? false;

    // If in update mode, load existing images

    // Add main image if exists
    // final mainImage = widget.arg?.product.image;
    // if (mainImage != null && mainImage.isNotEmpty) {
    //   _existingImageUrls.add(mainImage);
    // }

    // Add additional images if they exist
    if (widget.arg?.product.images != null) {
      for (var image in widget.arg!.product.images!) {
        if (image.path != null && image.path!.isNotEmpty) {
          _existingImageUrls.add(image.path!);
        }
      }
    }
  }

  Future<void> _handleConfirm() async {
    // Check if we have at least one image (existing requirement)
    if (_images.isEmpty && _existingImageUrls.isEmpty) {
      context.showErrorSnackBar(
          S.current.takePhotoProductValidationAtLeastOneImage);
      return;
    }

    // Check if we have at least 6 images total (new requirement)
    // final totalImages = _images.length + _existingImageUrls.length;
    // if (totalImages < 6) {
    //   context.showErrorSnackBar(
    //       S.current.takePhotoProductValidationAtLeastSixImages);
    //   return;
    // }

    ref.read(registerProductNotifierProvider.notifier).setLoading(true);

    // Convert images to base64 and update the request
    final List<ProductImageRegister> productImages = [];
    String? mainImageUrl;

    // First, use existing images if in update mode
    if (_existingImageUrls.isNotEmpty) {
      mainImageUrl = _existingImageUrls.first;
      for (var imgUrl in _existingImageUrls) {
        productImages.add(ProductImageRegister(path: imgUrl));
      }
    }

    // Then, process new images
    List<String> base64Images = [];
    for (var image in _images) {
      final base64Image = await _imagePicker.convertToBase64(File(image.path));
      base64Images.add(base64Image);
    }

    // Upload all images in a single batch
    if (base64Images.isNotEmpty) {
      await ref
          .read(registerProductNotifierProvider.notifier)
          .upListFileImage(base64Images);

      // Get the URLs from the state
      final state = ref.read(registerProductNotifierProvider);
      final imgUrls = state.listUrlImages;
      if (imgUrls != null && imgUrls.isNotEmpty) {
        // Use the first image as the main image if no existing images
        mainImageUrl ??= imgUrls.first;
        for (var imgUrl in imgUrls) {
          productImages.add(ProductImageRegister(path: imgUrl));
        }
      }
    }

    // Update the request with images
    final currentRequest = ref.read(registerProductNotifierProvider).request;
    final updateRequest =
        ref.read(registerProductNotifierProvider).updateRequest;

    if (_isUpdateMode && updateRequest != null) {
      final updatedRequest = updateRequest.copyWith(
        images: productImages,
        img: mainImageUrl ?? productImages.first.path,
      );
      ref
          .read(registerProductNotifierProvider.notifier)
          .updateRegisterProductRequestUpdate(updatedRequest);
    } else if (currentRequest != null) {
      final updatedRequest = currentRequest.copyWith(
        images: productImages,
        img: mainImageUrl ?? productImages.first.path,
      );
      ref
          .read(registerProductNotifierProvider.notifier)
          .updateRegisterProductRequest(updatedRequest);
    }

    final request = ref.read(registerProductNotifierProvider).request;
    var previewProductModel =
        ref.watch(registerProductNotifierProvider).previewProductModel;

    // Create product images
    final productImageModels =
        productImages.map((img) => ProductImageModel(path: img.path)).toList();

    print("22222222222222222222222222 :  ${productImageModels.length}");
    if (previewProductModel == null) {
      // Create a new ProductModel
      previewProductModel = ProductModel(
        name: request?.name,
        description: request?.description,
        price: request?.price.toString(),
        image: mainImageUrl,
        images: productImageModels,
      );
    } else {
      // Use copyWith on existing model

      previewProductModel = previewProductModel.copyWith(
        image: mainImageUrl,
        images: productImageModels,
      );
      print("previewProductModel: $previewProductModel");
      print("11111111111111111111111111");
    }

    ref
        .read(registerProductNotifierProvider.notifier)
        .updatePreviewProductModel(previewProductModel);
    ref.read(registerProductNotifierProvider.notifier).setLoading(false);

    context.pop();

    // Handle the response
    final state = ref.read(registerProductNotifierProvider);
    if (state.isSuccess ?? false) {
      if (mounted) {
        context.showSuccessSnackBar(_isUpdateMode
            ? S.current.takePhotoProductUpdateSuccess
            : S.current.takePhotoProductRegisterSuccess);
        context.go(RouterPaths.home, extra: true);
        ref.read(registerProductNotifierProvider.notifier).resetState();
      }
    } else if (state.errorMessage != null) {
      if (mounted) {
        context.showErrorSnackBar(state.errorMessage!);
      }
    }
    // else if (state.showImageSelectionDialog ?? false) {
    //   if (mounted) {
    //     context.showErrorSnackBar("Please select image");
    //   }
    // }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        context,
        source: ImageSource.camera,
      );

      if (pickedFile != null) {
        setState(() {
          _images.add(pickedFile);
        });
      } else {
        debugPrint("User canceled image capture");
      }
    } catch (e) {
      debugPrint("Error picking image: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.current.takePhotoProductErrorCaptureImage),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _pickMultipleImages() async {
    try {
      final List<XFile> pickedFiles =
          await _imagePicker.pickMultiImage(context);

      if (pickedFiles.isNotEmpty) {
        setState(() {
          _images.addAll(pickedFiles);
        });

        // Show confirmation dialog
        if (mounted) {}
      } else {
        debugPrint("User canceled image selection");
      }
    } catch (e) {
      debugPrint("Error picking images: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.current.takePhotoProductErrorSelectImages),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _removeImage(int index) {
    setState(() {
      final registerProductState = ref.read(registerProductNotifierProvider);
      final hasTemplate = registerProductState.selectedImageTemplate != null &&
          registerProductState.selectedImageTemplate!.isNotEmpty;

      if (hasTemplate) {
        if (index == 0) {
          // Remove template image
          ref
              .read(registerProductNotifierProvider.notifier)
              .setSelectedImageTemplate('');
        } else {
          // Adjust index for other images
          final adjustedIndex = index - 1;
          if (adjustedIndex < _existingImageUrls.length) {
            _existingImageUrls.removeAt(adjustedIndex);
          } else {
            _images.removeAt(adjustedIndex - _existingImageUrls.length);
          }
        }
      } else {
        if (index < _existingImageUrls.length) {
          _existingImageUrls.removeAt(index);
        } else {
          _images.removeAt(index - _existingImageUrls.length);
        }
      }

      // Update current index
      if (_currentIndex >=
          _existingImageUrls.length + _images.length + (hasTemplate ? 1 : 0)) {
        _currentIndex = _existingImageUrls.length +
            _images.length +
            (hasTemplate ? 1 : 0) -
            1;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final registerProductState = ref.watch(registerProductNotifierProvider);
    final allImages = [
      if (registerProductState.selectedImageTemplate != null &&
          registerProductState.selectedImageTemplate!.isNotEmpty)
        registerProductState.selectedImageTemplate!,
      ..._existingImageUrls,
      ..._images.map((img) => img.path)
    ];

    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: _isUpdateMode
            ? S.current.takePhotoProductUpdateTitle
            : S.current.takePhotoProductTitle,
        onTap: () {
          context.pop();
        },
      ),
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  InkWell(
                      onTap: () {
                        _pickMultipleImages();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppColors.textLightSecondary.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Text(S.current.takePhotoProductGallery,
                                style: const TextStyle(
                                    color: AppColors.textLightSecondary,
                                    fontSize: 12)),
                            const SizedBox(width: 4),
                            const Icon(Icons.photo_library,
                                color: AppColors.textLightSecondary),
                          ],
                        ),
                      )),
                  const Spacer(),
                  InkWell(
                      onTap: () {
                        _pickImage();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppColors.textLightSecondary.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Text(S.current.takePhotoProductCamera,
                                style: const TextStyle(
                                    color: AppColors.textLightSecondary,
                                    fontSize: 12)),
                            const SizedBox(width: 4),
                            const Icon(Icons.camera_alt,
                                color: AppColors.textLightSecondary),
                          ],
                        ),
                      )),
                ],
              ),
            ),
            const Spacer(),
            SizedBox(
              height: 300,
              child: allImages.isEmpty
                  ? Center(child: Text(S.current.takePhotoProductNoImages))
                  : Column(
                      children: [
                        Expanded(
                          child: CarouselSlider(
                            options: CarouselOptions(
                              height: double.infinity,
                              viewportFraction: 0.7,
                              enlargeCenterPage: true,
                              enableInfiniteScroll: allImages.length > 1,
                              onPageChanged: (index, reason) {
                                setState(() {
                                  _currentIndex = index;
                                });
                              },
                            ),
                            items: allImages.asMap().entries.map((entry) {
                              final index = entry.key;
                              final imagePath = entry.value;
                              final isExistingImage =
                                  index < _existingImageUrls.length;
        
                              return Stack(
                                children: [
                                  Center(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: isExistingImage
                                          ? BaseCachedNetworkImage(
                                              imageUrl: imagePath,
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              height: double.infinity,
                                            )
                                          : Image.file(
                                              File(imagePath),
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              height: double.infinity,
                                            ),
                                    ),
                                  ),
                                  Positioned(
                                    top: 1,
                                    right: 1,
                                    child: IconButton(
                                      icon: const Icon(Icons.cancel,
                                          color: AppColors.primary),
                                      onPressed: () => _removeImage(index),
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                          ),
                        ),
                        if (allImages.length > 1)
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: allImages.asMap().entries.map((entry) {
                                return Container(
                                  width: 8.0,
                                  height: 8.0,
                                  margin: const EdgeInsets.symmetric(
                                      vertical: 8.0, horizontal: 4.0),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.orange.withOpacity(
                                      _currentIndex == entry.key ? 0.9 : 0.4,
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                      ],
                    ),
            ),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  if (registerProductState.isLoading)
                    const Center(child: CircularProgressIndicator())
                  else
                    CommonButton(
                      onPressed: _handleConfirm,
                      text: _isUpdateMode
                          ? S.current.takePhotoProductUpdate
                          : S.current.takePhotoProductConfirm,
                    ),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(S.current.takePhotoProductCancel,
                        style: const TextStyle(color: Colors.grey)),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
