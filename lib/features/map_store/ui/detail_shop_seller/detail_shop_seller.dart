import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/service/location_service.dart';
import 'package:kitemite_app/features/map_store/provider/detail_store/detail_store_provider.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/select_cabinet.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/store/store_detail_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:url_launcher/url_launcher_string.dart';

class DetailShopSellerArg {
  final int storeId;
  final LatLng userPosition;
  DetailShopSellerArg({
    required this.storeId,
    required this.userPosition,
  });
}

class DetailShopSeller extends ConsumerStatefulWidget {
  final DetailShopSellerArg args;

  const DetailShopSeller({
    super.key,
    required this.args,
  });

  @override
  ConsumerState<DetailShopSeller> createState() => _DetailShopSellerState();
}

class _DetailShopSellerState extends ConsumerState<DetailShopSeller> {
  String? selectedImageUrl;
  LatLng? currentLocation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getCurrentLocation();
      ref.read(detailStoreNotifierProvider.notifier).loadStoreDetail(
            widget.args.storeId,
            'seller',
          );
    });
  }

  Future<void> _getCurrentLocation() async {
    currentLocation = await LocationService.getCurrentLocation();
  }

  String _calculateDistance(StoreDetailModel store) {
    if (store.latitude == null || store.longitude == null) return 'N/A';

    final distance = Geolocator.distanceBetween(
          currentLocation?.latitude ?? 0.0,
          currentLocation?.longitude ?? 0.0,
          double.parse(store.latitude!),
          double.parse(store.longitude!),
        ) /
        1000; // Convert to kilometers

    return '${distance.toStringAsFixed(1)} KM';
  }

  @override
  Widget build(BuildContext context) {
    final storeState = ref.watch(detailStoreNotifierProvider);
    final store = storeState.store;

    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: store?.name ?? "",
        onTap: () {
          context.pop(context);
        },
      ),
      body: storeState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : storeState.error != null
              ? ErrorCommonWidget(
                  error: storeState.error!,
                  onPressed: () {
                    ref
                        .read(detailStoreNotifierProvider.notifier)
                        .loadStoreDetail(
                          widget.args.storeId,
                          'seller',
                        );
                  },
                )
              : store == null
                  ? const Center(child: Text('No store data'))
                  : SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          BaseCachedNetworkImage(
                            imageUrl: selectedImageUrl ??
                                (store.images?.isNotEmpty == true
                                    ? store.images!.first
                                    : ""),
                            width: double.infinity,
                            height: 250,
                            fit: BoxFit.cover,
                            errorWidget: const Icon(Icons.error),
                          ),
                          if ((store.images?.length ?? 0) > 1) ...[
                            SizedBox(height: 8.h),
                            _buildImageGallery(store),
                          ],
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Text(
                                              "${store.province ?? ""}, ${store.city ?? ""}, ${store.street ?? ""}",
                                              style: AppTextStyles.regular(
                                                  12.sp,
                                                  color: AppColors
                                                      .textLightSecondary)),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [
                                              const Icon(Icons.access_time,
                                                  color: AppColors
                                                      .textLightSecondary,
                                                  size: 18),
                                              const SizedBox(width: 4),
                                              Text(
                                                  "毎日 ${store.open?.substring(0, 5) ?? ""} 時から ${store.close?.substring(0, 5) ?? ""} 時まで営業",
                                                  style: AppTextStyles.regular(
                                                      12.sp,
                                                      color: AppColors
                                                          .textLightSecondary)),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 16.w),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: AppColors.primary,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(16),
                                        onTap: () {
                                          _onDirections(
                                              LatLng(
                                                  double.parse(store.latitude!),
                                                  double.parse(
                                                      store.longitude!)),
                                              adressStore: store
                                                  .addressStoreFormatGGMap);
                                        },
                                        child: const Padding(
                                          padding: EdgeInsets.all(8.0),
                                          child: Icon(
                                            Icons.fork_right,
                                            size: 24,
                                            color: AppColors.textPrimary,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                Row(
                                  children: [
                                    _buildInfoBadge(
                                        _calculateDistance(store),
                                        AppColors.textLightSecondary
                                            .withOpacity(0.2),
                                        AppColors.textLightSecondary),
                                    const SizedBox(width: 8),
                                    _buildInfoBadge(
                                        "空きあり ${store.availableShelvesCount ?? 0}",
                                        const Color(0xFF5BE584),
                                        AppColors.white),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
      bottom: storeState.error != null
          ? const SizedBox.shrink()
          : SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: CommonButton(
                  text: "この店舗に商品を登録",
                  onPressed: () => context.push(RouterPaths.selectCabinet,
                      extra: StorageSelectionScreenArg(
                          storeId: widget.args.storeId)),
                ),
              ),
            ),
    );
  }

  Widget _buildImageGallery(StoreDetailModel store) {
    return SizedBox(
      height: 100.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: store.images?.length ?? 0,
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) => Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: GestureDetector(
            onTap: () {
              setState(() {
                selectedImageUrl = store.images?[index];
              });
            },
            child: Container(
              decoration: BoxDecoration(
                border: selectedImageUrl == store.images?[index]
                    ? Border.all(color: AppColors.primary, width: 2)
                    : null,
                borderRadius: BorderRadius.circular(0),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(0),
                child: BaseCachedNetworkImage(
                  imageUrl: store.images?[index] ?? "",
                  width: 115.w,
                  height: 100.h,
                  fit: BoxFit.cover,
                  errorWidget: Assets.a3159511881580040079.image(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoBadge(String text, Color colorBackground, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colorBackground,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(text, style: TextStyle(color: color, fontSize: 12)),
    );
  }

  void _onDirections(LatLng locationStation, {String? adressStore}) async {
    String url = "";
    if (Platform.isIOS) {
      url = AppConstants.urlGoogleMap2IOS(widget.args.userPosition.latitude,
          widget.args.userPosition.longitude, adressStore);
    } else {
      url = AppConstants.urlGoogleMap(
          widget.args.userPosition.latitude,
          widget.args.userPosition.longitude,
          locationStation.latitude,
          locationStation.longitude);
    }

    await canLaunchUrlString(url)
        ? await launchUrlString(url, mode: LaunchMode.externalApplication)
        : throw 'Could not launch $url';
  }
}
