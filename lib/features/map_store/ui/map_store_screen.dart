import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/service/location_service.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/map_store/provider/map_store/map_store_provider.dart';
import 'package:kitemite_app/features/map_store/ui/search_location/search_location_screen.dart';
import 'package:kitemite_app/features/map_store/ui/widget/store_card.dart';
import 'package:kitemite_app/features/map_store/ui/widget/store_card_seller.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/store/nearby_store_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:skeletonizer/skeletonizer.dart';

class MapStoreScreen extends ConsumerStatefulWidget {
  const MapStoreScreen({super.key});

  @override
  ConsumerState<MapStoreScreen> createState() => _MapStoreScreenState();
}

class _MapStoreScreenState extends ConsumerState<MapStoreScreen> {
  final LatLng _initialPosition = const LatLng(10.7769, 106.7009);
  final Completer<GoogleMapController> _controller =
      Completer<GoogleMapController>();
  final DraggableScrollableController _draggableController =
      DraggableScrollableController();
  Timer? _debounceTimer;
  Set<Marker> _markers = {};
  bool userIsSeller = false;
  BitmapDescriptor? _markerIcon;
  BitmapDescriptor? _userMarkerIcon;
  LatLng? _currentLocation;

  @override
  void initState() {
    super.initState();
    _loadMarkerIcon();
    _loadUserMarkerIcon();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeLocation();
    });
  }

  Future<void> _loadMarkerIcon() async {
    _markerIcon = await BitmapDescriptor.fromAssetImage(
      const ImageConfiguration(size: Size(100, 100)),
      Platform.isIOS ? Assets.markerIos.path : Assets.markerStoreNew.path,
    );
  }

  Future<void> _loadUserMarkerIcon() async {
    _userMarkerIcon = await BitmapDescriptor.fromAssetImage(
      const ImageConfiguration(size: Size(100, 100)),
      Assets.markerLocation.path,
    );
  }

  Future<void> _updateMarkers(List<NearbyStoreModel> stores) async {
    if (_markerIcon == null) {
      await _loadMarkerIcon();
    }
    if (_userMarkerIcon == null) {
      await _loadUserMarkerIcon();
    }

    final markers = stores.map((store) {
      return Marker(
        markerId: MarkerId(store.id?.toString() ?? ''),
        position: LatLng(
          double.tryParse(store.latitude ?? "0") ?? 0,
          double.tryParse(store.longitude ?? "0") ?? 0,
        ),
        icon: _markerIcon!,
        infoWindow: InfoWindow(
          title: store.name ?? '',
          snippet: store.street ?? '',
        ),
        onTap: () {
          _onMarkerTapped(store);
        },
      );
    }).toSet();

    if (!mounted) return;
    setState(() {
      _markers = markers;
    });
  }

  void _onMarkerTapped(NearbyStoreModel store) {
    final mapState = ref.watch(mapStoreNotifierProvider);
    final positionStore = LatLng(double.parse(store.latitude ?? "0"),
        double.parse(store.longitude ?? "0"));
    final role = ref.watch(modeNotifierProvider).mode == ModeAccount.seller
        ? "seller"
        : "buyer";

    if (userIsSeller) {
      ref.read(mapStoreNotifierProvider.notifier).loadNearbyStoresSeller(
          location: positionStore, role: role, isSearch: false);
    } else {
      ref
          .read(mapStoreNotifierProvider.notifier)
          .loadNearbyStores(location: positionStore, role: role);
    }
    ref.read(mapStoreNotifierProvider.notifier).updateMapPosition(
          positionStore,
          mapState.currentZoom,
        );
  }

  Future<void> _initializeLocation() async {
    final roleSeller =
        ref.watch(modeNotifierProvider).mode == ModeAccount.seller;
    final currentLocation = await LocationService.getCurrentLocation();
    const double zoomLevel = 12.0;
    _currentLocation = currentLocation;

    if (currentLocation != null) {
      roleSeller
          ? ref.read(mapStoreNotifierProvider.notifier).loadNearbyStoresSeller(
              role: "seller", isSearch: false, location: currentLocation)
          : ref
              .read(mapStoreNotifierProvider.notifier)
              .loadNearbyStores(location: currentLocation, role: "buyer");
      ref.read(mapStoreNotifierProvider.notifier).updateMapPosition(
            currentLocation,
            zoomLevel,
          );

      final GoogleMapController controller = await _controller.future;
      controller.animateCamera(
        CameraUpdate.newLatLngZoom(currentLocation, zoomLevel),
      );
    } else {
      roleSeller
          ? ref.read(mapStoreNotifierProvider.notifier).loadNearbyStoresSeller(
              role: "seller", isSearch: false, location: _initialPosition)
          : ref
              .read(mapStoreNotifierProvider.notifier)
              .loadNearbyStores(location: _initialPosition, role: "buyer");
      ref.read(mapStoreNotifierProvider.notifier).updateMapPosition(
            _initialPosition,
            zoomLevel,
          );
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onCameraMove(CameraPosition position) {
    // final role = ref.watch(modeNotifierProvider).mode == ModeAccount.seller
    //     ? "seller"
    //     : "buyer";
    // if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();
    // _debounceTimer = Timer(const Duration(milliseconds: 500), () {
    //   if (userIsSeller) {
    //     ref
    //         .read(mapStoreNotifierProvider.notifier)
    //         .loadNearbyStoresSeller(location: position.target, role: role);
    //   } else {
    //     ref
    //         .read(mapStoreNotifierProvider.notifier)
    //         .loadNearbyStores(location: position.target, role: role);
    //   }
    //   ref.read(mapStoreNotifierProvider.notifier).updateMapPosition(
    //         position.target,
    //         position.zoom,
    //       );
    // });
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    context.showErrorSnackBar(message);
  }

  @override
  Widget build(BuildContext context) {
    final mapState = ref.watch(mapStoreNotifierProvider);
    userIsSeller = ref.watch(modeNotifierProvider).mode == ModeAccount.seller;

    if (mapState.stores.isNotEmpty) {
      _updateMarkers(mapState.stores);
    }

    return SafeArea(
      top: false,
      maintainBottomViewPadding: false,
      child: Stack(
        children: [
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: mapState.currentLocation ?? _initialPosition,
              zoom: mapState.currentZoom,
            ),
            markers: _markers,
            myLocationEnabled: !userIsSeller,
            myLocationButtonEnabled: !userIsSeller,
            onMapCreated: (GoogleMapController controller) {
              _controller.complete(controller);
            },
            onCameraMove: _onCameraMove,
          ),
          if (userIsSeller)
            GestureDetector(
              onTap: () {
                context
                    .push(RouterPaths.searchLocationSearch,
                        extra: SearchLocationArg(
                          storeName: mapState.currentName,
                          openTime: mapState.currentOpen,
                          closeTime: mapState.currentClose,
                          province: mapState.currentProvince,
                        ))
                    .then((value) {
                  if (value = true) {
                    ref
                        .read(mapStoreNotifierProvider.notifier)
                        .loadNearbyStoresSeller(
                            location: mapState.currentLocation,
                            role: "seller",
                            isSearch: true);
                  }
                });
              },
              child: Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: SvgPicture.asset(
                    Assets.buttonSearch,
                    width: 56,
                    height: 56,
                  ),
                ),
              ),
            ),
          // if (mapState.isLoading)
          //   const Center(
          //     child: CircularProgressIndicator(),
          //   ),
          if (mapState.firstStoreLocation != null)
            FutureBuilder(
              future: _controller.future,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final controller = snapshot.data as GoogleMapController;
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    controller.animateCamera(
                      CameraUpdate.newLatLngZoom(
                          mapState.firstStoreLocation!, 12.0),
                    );
                  });
                }
                return const SizedBox.shrink();
              },
            ),
          if (mapState.isZoomCurrentLocation == true &&
              _currentLocation != null)
            FutureBuilder(
              future: _controller.future,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final controller = snapshot.data as GoogleMapController;
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    controller.animateCamera(
                      CameraUpdate.newLatLngZoom(_currentLocation!, 15.0),
                    );
                  });
                }
                return const SizedBox.shrink();
              },
            ),
          DraggableScrollableSheet(
            controller: _draggableController,
            initialChildSize: 0.3,
            minChildSize: 0.3,
            maxChildSize: 0.8,
            builder: (context, scrollController) {
              return Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                ),
                child: Column(
                  children: [
                    const SizedBox(height: 8),
                    GestureDetector(
                      onVerticalDragUpdate: (details) {
                        final newSize = (MediaQuery.of(context).size.height -
                                details.globalPosition.dy) /
                            MediaQuery.of(context).size.height;

                        final clampedSize = newSize.clamp(0.3, 0.8);

                        _draggableController.animateTo(
                          clampedSize,
                          duration: const Duration(milliseconds: 1),
                          curve: Curves.easeInOut,
                        );
                      },
                      child: Container(
                        height: 5,
                        width: 50,
                        decoration: BoxDecoration(
                          color: AppColors.actionLightActive,
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: mapState.isLoading
                          ? _buildSkeletonLoading()
                          : mapState.error != null
                              ? Column(children: [
                                  ErrorCommonWidget(
                                    error: mapState.error!,
                                    onPressed: () {
                                      ref
                                          .read(
                                              mapStoreNotifierProvider.notifier)
                                          .reloadMap();
                                    },
                                  )
                                ])
                              : mapState.stores.isEmpty
                                  ? Column(
                                      children: [
                                        SvgPicture.asset(
                                          Assets.shopGeneric,
                                          width: 100,
                                          height: 100,
                                        ),
                                        const SizedBox(height: 16),
                                        const Center(
                                          child: Text(
                                            '店舗がありません。',
                                            style:
                                                TextStyle(color: Colors.grey),
                                          ),
                                        ),
                                      ],
                                    )
                                  : ListView.builder(
                                      controller: scrollController,
                                      padding: EdgeInsets.fromLTRB(
                                          16,
                                          16,
                                          16,
                                          MediaQuery.of(context)
                                                  .padding
                                                  .bottom +
                                              32),
                                      itemCount: mapState.stores.length,
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      shrinkWrap: true,
                                      itemBuilder: (context, index) {
                                        final store = mapState.stores[index];

                                        return Column(
                                          children: [
                                            userIsSeller
                                                ? StoreCardSeller(
                                                    store: store,
                                                    userPosition:
                                                        _currentLocation ??
                                                            const LatLng(0, 0),
                                                  )
                                                : StoreCard(
                                                    store: store,
                                                    userPosition: mapState
                                                            .currentLocation ??
                                                        _initialPosition,
                                                  ),
                                            const SizedBox(height: 16),
                                          ],
                                        );
                                      },
                                    ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonLoading() {
    return Skeletonizer(
      enabled: true,
      effect: const ShimmerEffect(
        baseColor: AppColors.mono20,
        highlightColor: AppColors.mono40,
      ),
      child: ListView.builder(
        padding: EdgeInsets.fromLTRB(
            16, 16, 16, MediaQuery.of(context).padding.bottom + 32),
        itemCount: 3,
        physics: const AlwaysScrollableScrollPhysics(),
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Column(
            children: [
              _buildStoreCardSkeleton(),
              const SizedBox(height: 16),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStoreCardSkeleton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.mono20,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.mono40,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 200,
                      height: 20,
                      color: AppColors.mono40,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: 150,
                      height: 16,
                      color: AppColors.mono40,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          width: 80,
                          height: 24,
                          decoration: BoxDecoration(
                            color: AppColors.mono40,
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 60,
                          height: 24,
                          decoration: BoxDecoration(
                            color: AppColors.mono40,
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.mono40,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ],
      ),
    );
  }
}
