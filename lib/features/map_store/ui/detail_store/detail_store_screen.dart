import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/ui/base_search_widget.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/home/<USER>/widget/product_item.dart';
import 'package:kitemite_app/features/map_store/provider/detail_store/detail_store_provider.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/store/store_detail_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:url_launcher/url_launcher_string.dart';

class DetailStoreScreenArg {
  final int storeId;
  final LatLng userPosition;
  DetailStoreScreenArg({
    required this.storeId,
    required this.userPosition,
  });
}

class DetailStoreScreen extends ConsumerStatefulWidget {
  final DetailStoreScreenArg args;

  const DetailStoreScreen({
    super.key,
    required this.args,
  });

  @override
  ConsumerState<DetailStoreScreen> createState() => _DetailStoreScreenState();
}

class _DetailStoreScreenState extends ConsumerState<DetailStoreScreen> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userIsSeller =
          ref.read(modeNotifierProvider).mode == ModeAccount.seller;
      ref.read(detailStoreNotifierProvider.notifier).loadStoreDetail(
            widget.args.storeId,
            userIsSeller ? 'seller' : 'buyer',
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final userIsSeller =
        ref.watch(modeNotifierProvider).mode == ModeAccount.seller;
    final storeState = ref.watch(detailStoreNotifierProvider);
    final store = storeState.store;

    return BaseScaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppbar.basic(
        title: store?.name ?? "",
        onTap: () {
          context.pop();
        },
      ),
      body: storeState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : storeState.error != null
              ? Center(
                  child: Text(
                    storeState.error!,
                    style: const TextStyle(color: Colors.red),
                  ),
                )
              : store == null
                  ? const Center(child: Text('No store data'))
                  : SafeArea(
                      top: false,
                      maintainBottomViewPadding: true,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "${store.province ?? ""}, ${store.city ?? ""}, ${store.street ?? ""}",
                                      style: AppTextStyles.regular(14.sp,
                                          color: AppColors.textLightSecondary),
                                    ),
                                    SizedBox(height: 8.h),
                                    BaseSearchWidget(
                                      onSearch: (keyword) {
                                        ref
                                            .read(detailStoreNotifierProvider
                                                .notifier)
                                            .searchProducts(keyword);
                                      },
                                    ),
                                  ])),
                          SizedBox(height: 8.h),
                          // Add empty search results UI
                          if (storeState.searchKeyword.isNotEmpty &&
                              store.cabinets != null &&
                              store.cabinets!.every((cabinet) {
                                final hasMatchingProducts = cabinet.products
                                        ?.any((product) =>
                                            product.name
                                                ?.toLowerCase()
                                                .contains(storeState
                                                    .searchKeyword
                                                    .toLowerCase()) ??
                                            false) ??
                                    false;
                                return !hasMatchingProducts;
                              }))
                            Expanded(
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Assets.iconEmptyProduct.image(),
                                    SizedBox(height: 16.h),
                                    Text(
                                      'No products found for "${storeState.searchKeyword}"',
                                      style: AppTextStyles.regular(14.sp,
                                          color: AppColors.textLightSecondary),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                            )
                          else if (store.cabinets == null ||
                              store.cabinets!.isEmpty ||
                              store.cabinets!.every((cabinet) =>
                                  cabinet.products == null ||
                                  cabinet.products!.isEmpty))
                            Expanded(
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Assets.iconEmptyProduct.image(),
                                    SizedBox(height: 16.h),
                                    Text(
                                      '在庫商品がありません。 \nまたお越しください。',
                                      style: AppTextStyles.regular(14.sp,
                                          color: AppColors.textLightSecondary),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                            )
                          else
                            Expanded(
                              child: ListView.builder(
                                itemCount: store.cabinets?.length ?? 0,
                                padding: const EdgeInsets.only(top: 16),
                                itemBuilder: (context, cabinetIndex) {
                                  final cabinet = store.cabinets![cabinetIndex];
                                  // Filter products based on search keyword
                                  final filteredProducts =
                                      cabinet.products?.where((product) {
                                            final searchKeyword = storeState
                                                .searchKeyword
                                                .toLowerCase();
                                            return product.name
                                                    ?.toLowerCase()
                                                    .contains(searchKeyword) ??
                                                false;
                                          }).toList() ??
                                          [];

                                  // Skip cabinet if no products match search and there is a search keyword
                                  if (filteredProducts.isEmpty) {
                                    return const SizedBox.shrink();
                                  }

                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 16),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16),
                                          child: Row(
                                            children: [
                                              Text(
                                                "冷凍庫-${cabinet.cabinetCode ?? ""}",
                                                textAlign: TextAlign.start,
                                                style: AppTextStyles.bold(14.sp,
                                                    color:
                                                        AppColors.textPrimary),
                                              ),
                                              const Spacer(),
                                              if (userIsSeller)
                                                SizedBox(
                                                  height: 40.h,
                                                  child: ElevatedButton(
                                                    style: ElevatedButton
                                                        .styleFrom(
                                                      backgroundColor:
                                                          AppColors.primary,
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 8,
                                                          vertical: 2),
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                      ),
                                                    ),
                                                    onPressed: () {},
                                                    child: Row(
                                                      children: [
                                                        Text(
                                                          "解除",
                                                          style: AppTextStyles.bold(
                                                              15.sp,
                                                              color: AppColors
                                                                  .textPrimary),
                                                        ),
                                                        const Icon(Icons.lock,
                                                            color: AppColors
                                                                .textPrimary)
                                                      ],
                                                    ),
                                                  ),
                                                )
                                            ],
                                          ),
                                        ),
                                        GridView.builder(
                                          controller: _scrollController,
                                          itemCount: filteredProducts.length,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16),
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          gridDelegate:
                                              const SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 3,
                                            crossAxisSpacing: 8.0,
                                            mainAxisSpacing: 4.0,
                                            childAspectRatio: 0.6,
                                          ),
                                          itemBuilder: (contextItem, index) {
                                            final product =
                                                filteredProducts[index];
                                            bool isGuest = ref
                                                    .watch(modeNotifierProvider)
                                                    .mode ==
                                                ModeAccount.guest;
                                            return InkWell(
                                              onTap: () {
                                                context.push(
                                                    RouterPaths.productDetail,
                                                    extra:
                                                        ProductDetailScreenArg(
                                                            productId:
                                                                product.id ?? 0,
                                                            isLoginWithAccount:
                                                                isGuest
                                                                    ? false
                                                                    : true,
                                                            isUpdateProduct:
                                                                false));
                                              },
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    bottom: 12, top: 12),
                                                child: ProductItem(
                                                  imagePath: product.img ?? "",
                                                  title: product.name ?? "",
                                                  price:
                                                      "${product.salePrice?.priceString()}円",
                                                  remainingStock: product
                                                          .quantity
                                                          ?.toString() ??
                                                      "0",
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          SizedBox(height: 8.h),
                          (userIsSeller)
                              ? Container()
                              : Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0),
                                  child: CommonButton(
                                    customTitle: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.fork_right,
                                            color: AppColors.textPrimary,
                                            size: 24.sp),
                                        SizedBox(width: 8.w),
                                        Text("ルートを確認",
                                            style: AppTextStyles.bold(14.sp,
                                                color: AppColors.textPrimary)),
                                      ],
                                    ),
                                    onPressed: () {
                                      _onDirections(LatLng(
                                          double.parse(store.latitude ?? "0"),
                                          double.parse(
                                              store.longitude ?? "0")),  store.addressStoreFormatGGMap);
                                    },
                                  ),
                                ),
                        ],
                      ),
                    ),
    );
  }

  void _onDirections(LatLng locationStation, String adressStore) async {
    String url = "";
    if (Platform.isIOS) {
      url = AppConstants.urlGoogleMap2IOS(widget.args.userPosition.latitude,
          widget.args.userPosition.longitude, adressStore);
    } else {
      url = AppConstants.urlGoogleMap(
          widget.args.userPosition.latitude,
          widget.args.userPosition.longitude,
          locationStation.latitude,
          locationStation.longitude);
    }

    await canLaunchUrlString(url)
        ? await launchUrlString(url, mode: LaunchMode.externalApplication)
        : throw 'Could not launch $url';
  }
}
