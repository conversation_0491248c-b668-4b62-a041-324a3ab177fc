// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_store_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$detailStoreNotifierHash() =>
    r'a83ee1af12869032ede60567d4102510551796e6';

/// See also [DetailStoreNotifier].
@ProviderFor(DetailStoreNotifier)
final detailStoreNotifierProvider =
    AutoDisposeNotifierProvider<DetailStoreNotifier, DetailStoreState>.internal(
  DetailStoreNotifier.new,
  name: r'detailStoreNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$detailStoreNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DetailStoreNotifier = AutoDisposeNotifier<DetailStoreState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
