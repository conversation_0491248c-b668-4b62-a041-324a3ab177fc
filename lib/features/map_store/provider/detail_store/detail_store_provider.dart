import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/features/map_store/provider/detail_store/detail_store_state.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/response/store/store_detail_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'detail_store_provider.g.dart';

@riverpod
class DetailStoreNotifier extends _$DetailStoreNotifier {
  @override
  DetailStoreState build() {
    return const DetailStoreState();
  }

  Future<void> loadStoreDetail(int id, String role) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      bool isSeller =
          ref.watch(modeNotifierProvider).mode == ModeAccount.seller;
      BaseResponse<StoreDetailModel> response;

      if (isSeller) {
        response =
            await ref.read(getStoreRepositoryProvider).getStoreDetail(id, role);
      } else {
        response =
            await ref.read(getStoreRepositoryProvider).getStoreDetailGuest(id);
      }

      state = state.copyWith(
        store: response.data,
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }

  void searchProducts(String keyword) {
    state = state.copyWith(searchKeyword: keyword);
  }
}
