// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'detail_store_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DetailStoreState {
  StoreDetailModel? get store => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String get searchKeyword => throw _privateConstructorUsedError;

  /// Create a copy of DetailStoreState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DetailStoreStateCopyWith<DetailStoreState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DetailStoreStateCopyWith<$Res> {
  factory $DetailStoreStateCopyWith(
          DetailStoreState value, $Res Function(DetailStoreState) then) =
      _$DetailStoreStateCopyWithImpl<$Res, DetailStoreState>;
  @useResult
  $Res call(
      {StoreDetailModel? store,
      bool isLoading,
      String? error,
      String searchKeyword});

  $StoreDetailModelCopyWith<$Res>? get store;
}

/// @nodoc
class _$DetailStoreStateCopyWithImpl<$Res, $Val extends DetailStoreState>
    implements $DetailStoreStateCopyWith<$Res> {
  _$DetailStoreStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DetailStoreState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? store = freezed,
    Object? isLoading = null,
    Object? error = freezed,
    Object? searchKeyword = null,
  }) {
    return _then(_value.copyWith(
      store: freezed == store
          ? _value.store
          : store // ignore: cast_nullable_to_non_nullable
              as StoreDetailModel?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      searchKeyword: null == searchKeyword
          ? _value.searchKeyword
          : searchKeyword // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of DetailStoreState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $StoreDetailModelCopyWith<$Res>? get store {
    if (_value.store == null) {
      return null;
    }

    return $StoreDetailModelCopyWith<$Res>(_value.store!, (value) {
      return _then(_value.copyWith(store: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DetailStoreStateImplCopyWith<$Res>
    implements $DetailStoreStateCopyWith<$Res> {
  factory _$$DetailStoreStateImplCopyWith(_$DetailStoreStateImpl value,
          $Res Function(_$DetailStoreStateImpl) then) =
      __$$DetailStoreStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {StoreDetailModel? store,
      bool isLoading,
      String? error,
      String searchKeyword});

  @override
  $StoreDetailModelCopyWith<$Res>? get store;
}

/// @nodoc
class __$$DetailStoreStateImplCopyWithImpl<$Res>
    extends _$DetailStoreStateCopyWithImpl<$Res, _$DetailStoreStateImpl>
    implements _$$DetailStoreStateImplCopyWith<$Res> {
  __$$DetailStoreStateImplCopyWithImpl(_$DetailStoreStateImpl _value,
      $Res Function(_$DetailStoreStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DetailStoreState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? store = freezed,
    Object? isLoading = null,
    Object? error = freezed,
    Object? searchKeyword = null,
  }) {
    return _then(_$DetailStoreStateImpl(
      store: freezed == store
          ? _value.store
          : store // ignore: cast_nullable_to_non_nullable
              as StoreDetailModel?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      searchKeyword: null == searchKeyword
          ? _value.searchKeyword
          : searchKeyword // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DetailStoreStateImpl implements _DetailStoreState {
  const _$DetailStoreStateImpl(
      {this.store,
      this.isLoading = false,
      this.error,
      this.searchKeyword = ''});

  @override
  final StoreDetailModel? store;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;
  @override
  @JsonKey()
  final String searchKeyword;

  @override
  String toString() {
    return 'DetailStoreState(store: $store, isLoading: $isLoading, error: $error, searchKeyword: $searchKeyword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DetailStoreStateImpl &&
            (identical(other.store, store) || other.store == store) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.searchKeyword, searchKeyword) ||
                other.searchKeyword == searchKeyword));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, store, isLoading, error, searchKeyword);

  /// Create a copy of DetailStoreState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DetailStoreStateImplCopyWith<_$DetailStoreStateImpl> get copyWith =>
      __$$DetailStoreStateImplCopyWithImpl<_$DetailStoreStateImpl>(
          this, _$identity);
}

abstract class _DetailStoreState implements DetailStoreState {
  const factory _DetailStoreState(
      {final StoreDetailModel? store,
      final bool isLoading,
      final String? error,
      final String searchKeyword}) = _$DetailStoreStateImpl;

  @override
  StoreDetailModel? get store;
  @override
  bool get isLoading;
  @override
  String? get error;
  @override
  String get searchKeyword;

  /// Create a copy of DetailStoreState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DetailStoreStateImplCopyWith<_$DetailStoreStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
