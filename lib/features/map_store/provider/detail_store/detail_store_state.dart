import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/store/store_detail_model.dart';

part 'detail_store_state.freezed.dart';

@freezed
class DetailStoreState with _$DetailStoreState {
  const factory DetailStoreState({
    StoreDetailModel? store,
    @Default(false) bool isLoading,
    String? error,
    @Default('') String searchKeyword,
  }) = _DetailStoreState;
}
