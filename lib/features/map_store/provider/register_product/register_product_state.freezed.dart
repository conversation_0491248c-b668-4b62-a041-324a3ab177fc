// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_product_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RegisterProductState _$RegisterProductStateFromJson(Map<String, dynamic> json) {
  return _RegisterProductState.fromJson(json);
}

/// @nodoc
mixin _$RegisterProductState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isSuccess => throw _privateConstructorUsedError;
  RegisterProductRequest? get request => throw _privateConstructorUsedError;
  RegisterProductRequest? get updateRequest =>
      throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  String? get urlImage => throw _privateConstructorUsedError;
  List<String>? get listUrlImages => throw _privateConstructorUsedError;
  List<ProductImageRegister>? get images => throw _privateConstructorUsedError;
  bool get isUploadingImage => throw _privateConstructorUsedError;
  String? get upFileImageFailure => throw _privateConstructorUsedError;
  bool get showImageSelectionDialog => throw _privateConstructorUsedError;
  String? get selectedImageTemplate => throw _privateConstructorUsedError;
  bool get isCreateTemplate => throw _privateConstructorUsedError;
  ProductModel? get previewProductModel => throw _privateConstructorUsedError;

  /// Serializes this RegisterProductState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RegisterProductState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RegisterProductStateCopyWith<RegisterProductState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterProductStateCopyWith<$Res> {
  factory $RegisterProductStateCopyWith(RegisterProductState value,
          $Res Function(RegisterProductState) then) =
      _$RegisterProductStateCopyWithImpl<$Res, RegisterProductState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      RegisterProductRequest? request,
      RegisterProductRequest? updateRequest,
      String? errorMessage,
      String? urlImage,
      List<String>? listUrlImages,
      List<ProductImageRegister>? images,
      bool isUploadingImage,
      String? upFileImageFailure,
      bool showImageSelectionDialog,
      String? selectedImageTemplate,
      bool isCreateTemplate,
      ProductModel? previewProductModel});

  $RegisterProductRequestCopyWith<$Res>? get request;
  $RegisterProductRequestCopyWith<$Res>? get updateRequest;
  $ProductModelCopyWith<$Res>? get previewProductModel;
}

/// @nodoc
class _$RegisterProductStateCopyWithImpl<$Res,
        $Val extends RegisterProductState>
    implements $RegisterProductStateCopyWith<$Res> {
  _$RegisterProductStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RegisterProductState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? request = freezed,
    Object? updateRequest = freezed,
    Object? errorMessage = freezed,
    Object? urlImage = freezed,
    Object? listUrlImages = freezed,
    Object? images = freezed,
    Object? isUploadingImage = null,
    Object? upFileImageFailure = freezed,
    Object? showImageSelectionDialog = null,
    Object? selectedImageTemplate = freezed,
    Object? isCreateTemplate = null,
    Object? previewProductModel = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      request: freezed == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RegisterProductRequest?,
      updateRequest: freezed == updateRequest
          ? _value.updateRequest
          : updateRequest // ignore: cast_nullable_to_non_nullable
              as RegisterProductRequest?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      listUrlImages: freezed == listUrlImages
          ? _value.listUrlImages
          : listUrlImages // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      images: freezed == images
          ? _value.images
          : images // ignore: cast_nullable_to_non_nullable
              as List<ProductImageRegister>?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      showImageSelectionDialog: null == showImageSelectionDialog
          ? _value.showImageSelectionDialog
          : showImageSelectionDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedImageTemplate: freezed == selectedImageTemplate
          ? _value.selectedImageTemplate
          : selectedImageTemplate // ignore: cast_nullable_to_non_nullable
              as String?,
      isCreateTemplate: null == isCreateTemplate
          ? _value.isCreateTemplate
          : isCreateTemplate // ignore: cast_nullable_to_non_nullable
              as bool,
      previewProductModel: freezed == previewProductModel
          ? _value.previewProductModel
          : previewProductModel // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
    ) as $Val);
  }

  /// Create a copy of RegisterProductState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RegisterProductRequestCopyWith<$Res>? get request {
    if (_value.request == null) {
      return null;
    }

    return $RegisterProductRequestCopyWith<$Res>(_value.request!, (value) {
      return _then(_value.copyWith(request: value) as $Val);
    });
  }

  /// Create a copy of RegisterProductState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RegisterProductRequestCopyWith<$Res>? get updateRequest {
    if (_value.updateRequest == null) {
      return null;
    }

    return $RegisterProductRequestCopyWith<$Res>(_value.updateRequest!,
        (value) {
      return _then(_value.copyWith(updateRequest: value) as $Val);
    });
  }

  /// Create a copy of RegisterProductState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductModelCopyWith<$Res>? get previewProductModel {
    if (_value.previewProductModel == null) {
      return null;
    }

    return $ProductModelCopyWith<$Res>(_value.previewProductModel!, (value) {
      return _then(_value.copyWith(previewProductModel: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RegisterProductStateImplCopyWith<$Res>
    implements $RegisterProductStateCopyWith<$Res> {
  factory _$$RegisterProductStateImplCopyWith(_$RegisterProductStateImpl value,
          $Res Function(_$RegisterProductStateImpl) then) =
      __$$RegisterProductStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      RegisterProductRequest? request,
      RegisterProductRequest? updateRequest,
      String? errorMessage,
      String? urlImage,
      List<String>? listUrlImages,
      List<ProductImageRegister>? images,
      bool isUploadingImage,
      String? upFileImageFailure,
      bool showImageSelectionDialog,
      String? selectedImageTemplate,
      bool isCreateTemplate,
      ProductModel? previewProductModel});

  @override
  $RegisterProductRequestCopyWith<$Res>? get request;
  @override
  $RegisterProductRequestCopyWith<$Res>? get updateRequest;
  @override
  $ProductModelCopyWith<$Res>? get previewProductModel;
}

/// @nodoc
class __$$RegisterProductStateImplCopyWithImpl<$Res>
    extends _$RegisterProductStateCopyWithImpl<$Res, _$RegisterProductStateImpl>
    implements _$$RegisterProductStateImplCopyWith<$Res> {
  __$$RegisterProductStateImplCopyWithImpl(_$RegisterProductStateImpl _value,
      $Res Function(_$RegisterProductStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegisterProductState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? request = freezed,
    Object? updateRequest = freezed,
    Object? errorMessage = freezed,
    Object? urlImage = freezed,
    Object? listUrlImages = freezed,
    Object? images = freezed,
    Object? isUploadingImage = null,
    Object? upFileImageFailure = freezed,
    Object? showImageSelectionDialog = null,
    Object? selectedImageTemplate = freezed,
    Object? isCreateTemplate = null,
    Object? previewProductModel = freezed,
  }) {
    return _then(_$RegisterProductStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      request: freezed == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RegisterProductRequest?,
      updateRequest: freezed == updateRequest
          ? _value.updateRequest
          : updateRequest // ignore: cast_nullable_to_non_nullable
              as RegisterProductRequest?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      listUrlImages: freezed == listUrlImages
          ? _value._listUrlImages
          : listUrlImages // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      images: freezed == images
          ? _value._images
          : images // ignore: cast_nullable_to_non_nullable
              as List<ProductImageRegister>?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      showImageSelectionDialog: null == showImageSelectionDialog
          ? _value.showImageSelectionDialog
          : showImageSelectionDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedImageTemplate: freezed == selectedImageTemplate
          ? _value.selectedImageTemplate
          : selectedImageTemplate // ignore: cast_nullable_to_non_nullable
              as String?,
      isCreateTemplate: null == isCreateTemplate
          ? _value.isCreateTemplate
          : isCreateTemplate // ignore: cast_nullable_to_non_nullable
              as bool,
      previewProductModel: freezed == previewProductModel
          ? _value.previewProductModel
          : previewProductModel // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RegisterProductStateImpl implements _RegisterProductState {
  const _$RegisterProductStateImpl(
      {this.isLoading = false,
      this.isSuccess = false,
      this.request,
      this.updateRequest,
      this.errorMessage,
      this.urlImage,
      final List<String>? listUrlImages,
      final List<ProductImageRegister>? images,
      this.isUploadingImage = false,
      this.upFileImageFailure,
      this.showImageSelectionDialog = false,
      this.selectedImageTemplate,
      this.isCreateTemplate = true,
      this.previewProductModel})
      : _listUrlImages = listUrlImages,
        _images = images;

  factory _$RegisterProductStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$RegisterProductStateImplFromJson(json);

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSuccess;
  @override
  final RegisterProductRequest? request;
  @override
  final RegisterProductRequest? updateRequest;
  @override
  final String? errorMessage;
  @override
  final String? urlImage;
  final List<String>? _listUrlImages;
  @override
  List<String>? get listUrlImages {
    final value = _listUrlImages;
    if (value == null) return null;
    if (_listUrlImages is EqualUnmodifiableListView) return _listUrlImages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductImageRegister>? _images;
  @override
  List<ProductImageRegister>? get images {
    final value = _images;
    if (value == null) return null;
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final bool isUploadingImage;
  @override
  final String? upFileImageFailure;
  @override
  @JsonKey()
  final bool showImageSelectionDialog;
  @override
  final String? selectedImageTemplate;
  @override
  @JsonKey()
  final bool isCreateTemplate;
  @override
  final ProductModel? previewProductModel;

  @override
  String toString() {
    return 'RegisterProductState(isLoading: $isLoading, isSuccess: $isSuccess, request: $request, updateRequest: $updateRequest, errorMessage: $errorMessage, urlImage: $urlImage, listUrlImages: $listUrlImages, images: $images, isUploadingImage: $isUploadingImage, upFileImageFailure: $upFileImageFailure, showImageSelectionDialog: $showImageSelectionDialog, selectedImageTemplate: $selectedImageTemplate, isCreateTemplate: $isCreateTemplate, previewProductModel: $previewProductModel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterProductStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSuccess, isSuccess) ||
                other.isSuccess == isSuccess) &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.updateRequest, updateRequest) ||
                other.updateRequest == updateRequest) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.urlImage, urlImage) ||
                other.urlImage == urlImage) &&
            const DeepCollectionEquality()
                .equals(other._listUrlImages, _listUrlImages) &&
            const DeepCollectionEquality().equals(other._images, _images) &&
            (identical(other.isUploadingImage, isUploadingImage) ||
                other.isUploadingImage == isUploadingImage) &&
            (identical(other.upFileImageFailure, upFileImageFailure) ||
                other.upFileImageFailure == upFileImageFailure) &&
            (identical(
                    other.showImageSelectionDialog, showImageSelectionDialog) ||
                other.showImageSelectionDialog == showImageSelectionDialog) &&
            (identical(other.selectedImageTemplate, selectedImageTemplate) ||
                other.selectedImageTemplate == selectedImageTemplate) &&
            (identical(other.isCreateTemplate, isCreateTemplate) ||
                other.isCreateTemplate == isCreateTemplate) &&
            (identical(other.previewProductModel, previewProductModel) ||
                other.previewProductModel == previewProductModel));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isSuccess,
      request,
      updateRequest,
      errorMessage,
      urlImage,
      const DeepCollectionEquality().hash(_listUrlImages),
      const DeepCollectionEquality().hash(_images),
      isUploadingImage,
      upFileImageFailure,
      showImageSelectionDialog,
      selectedImageTemplate,
      isCreateTemplate,
      previewProductModel);

  /// Create a copy of RegisterProductState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterProductStateImplCopyWith<_$RegisterProductStateImpl>
      get copyWith =>
          __$$RegisterProductStateImplCopyWithImpl<_$RegisterProductStateImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RegisterProductStateImplToJson(
      this,
    );
  }
}

abstract class _RegisterProductState implements RegisterProductState {
  const factory _RegisterProductState(
      {final bool isLoading,
      final bool isSuccess,
      final RegisterProductRequest? request,
      final RegisterProductRequest? updateRequest,
      final String? errorMessage,
      final String? urlImage,
      final List<String>? listUrlImages,
      final List<ProductImageRegister>? images,
      final bool isUploadingImage,
      final String? upFileImageFailure,
      final bool showImageSelectionDialog,
      final String? selectedImageTemplate,
      final bool isCreateTemplate,
      final ProductModel? previewProductModel}) = _$RegisterProductStateImpl;

  factory _RegisterProductState.fromJson(Map<String, dynamic> json) =
      _$RegisterProductStateImpl.fromJson;

  @override
  bool get isLoading;
  @override
  bool get isSuccess;
  @override
  RegisterProductRequest? get request;
  @override
  RegisterProductRequest? get updateRequest;
  @override
  String? get errorMessage;
  @override
  String? get urlImage;
  @override
  List<String>? get listUrlImages;
  @override
  List<ProductImageRegister>? get images;
  @override
  bool get isUploadingImage;
  @override
  String? get upFileImageFailure;
  @override
  bool get showImageSelectionDialog;
  @override
  String? get selectedImageTemplate;
  @override
  bool get isCreateTemplate;
  @override
  ProductModel? get previewProductModel;

  /// Create a copy of RegisterProductState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterProductStateImplCopyWith<_$RegisterProductStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
