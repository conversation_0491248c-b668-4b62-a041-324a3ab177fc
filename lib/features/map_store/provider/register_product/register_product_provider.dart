import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/auth/auth_repository.dart';
import 'package:kitemite_app/core/repository/product/product_repository.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/features/map_store/provider/register_product/register_product_state.dart';
import 'package:kitemite_app/model/request/product/register_product_request.dart';
import 'package:kitemite_app/model/request/store/template_request.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'register_product_provider.g.dart';

@Riverpod(keepAlive: true)
class RegisterProductNotifier extends _$RegisterProductNotifier {
  @override
  RegisterProductState build() => const RegisterProductState();

  void updateRegisterProductRequest(RegisterProductRequest request) {
    state = state.copyWith(request: request);
  }

  void updateRegisterProductRequestUpdate(RegisterProductRequest request) {
    state = state.copyWith(updateRequest: request);
  }

  void setIsCreateTemplate(bool value) {
    state = state.copyWith(isCreateTemplate: value);
  }

  Future<void> registerProduct() async {
    // Check if image is required but not provided
    if (state.urlImage == null && state.request?.images.isEmpty == true) {
      state = state.copyWith(showImageSelectionDialog: true);
      return;
    }
    state = state.copyWith(request: state.request);
    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
      showImageSelectionDialog: false,
      isSuccess: false,
    );

    try {
      final futures = <Future>[];
      if (state.isCreateTemplate) {
        futures.add(createTemplate());
      }
      futures.add(ref
          .read(getProductRepositoryProvider)
          .registerProduct(state.request!));

      await Future.wait(futures);
      state = state.copyWith(isSuccess: true, isLoading: false);
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.message,
        showImageSelectionDialog: false,
        isSuccess: false,
      );
    }
  }

  Future<void> updateProduct(int id) async {
    if (state.urlImage == null && state.updateRequest?.images.isEmpty == true) {
      state = state.copyWith(showImageSelectionDialog: true);
      return;
    }
    state = state.copyWith(updateRequest: state.updateRequest);
    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
      showImageSelectionDialog: false,
      isSuccess: false,
    );

    try {
      await ref
          .read(getProductRepositoryProvider)
          .updateProduct(id, state.updateRequest!);
      state = state.copyWith(isSuccess: true, isLoading: false);
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.message,
        showImageSelectionDialog: false,
        isSuccess: false,
      );
    }
  }

  void resetState() {
    state = const RegisterProductState();
  }

  Future<void> upFileImage(String imageBase64) async {
    state = state.copyWith(
      upFileImageFailure: null,
      isUploadingImage: true,
    );
    try {
      final authRepo = ref.read(getAuthRepositoryProvider);

      final result = await authRepo.upLoadFile(base64: imageBase64);
      state = state.copyWith(
        urlImage: result.links?.first ?? "",
        isUploadingImage: false,
        showImageSelectionDialog: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        isUploadingImage: false,
        upFileImageFailure: e.message,
      );
    }
  }

  Future<void> upListFileImage(List<String> imageBase64) async {
    state = state.copyWith(
      upFileImageFailure: null,
      isUploadingImage: true,
      showImageSelectionDialog: false,
    );
    try {
      final authRepo = ref.read(getAuthRepositoryProvider);

      final result = await authRepo.upListFileImage(base64: imageBase64);
      state = state.copyWith(
        listUrlImages: result.links,
        isUploadingImage: false,
        showImageSelectionDialog: false,
        upFileImageFailure: null,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        isUploadingImage: false,
        upFileImageFailure: e.message,
      );
    }
  }

  void resetImageSelectionDialog() {
    state = state.copyWith(showImageSelectionDialog: false);
  }

  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  void setSelectedImageTemplate(String selectedImageTemplate) {
    state = state.copyWith(selectedImageTemplate: selectedImageTemplate);
  }

  void updatePreviewProductModel(ProductModel previewProductModel) {
    print("previewProductModel: $previewProductModel");
    state = state.copyWith(previewProductModel: previewProductModel);
  }

  Future<void> createTemplate() async {
    state = state.copyWith(isCreateTemplate: true);
    try {
      await ref
          .read(getStoreRepositoryProvider)
          .createTemplateWarehouse(TemplateRequest(
            name: state.request?.name,
            description: state.request?.description,
            baseImg: state.request?.images.first.path,
            basePrice: state.request?.price.toString(),
          ));
    } on AppFailure catch (e) {}
  }

  void checkImageInUpdateRequest() {
    if (state.updateRequest?.images.isEmpty == true) {
      state = state.copyWith(showImageSelectionDialog: true);
    } else {
      state = state.copyWith(showImageSelectionDialog: false);
    }
  }
}
