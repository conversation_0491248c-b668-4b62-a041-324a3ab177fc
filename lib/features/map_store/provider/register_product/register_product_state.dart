import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../model/request/product/register_product_request.dart';
import '../../../../model/response/product/product_model.dart';

part 'register_product_state.freezed.dart';
part 'register_product_state.g.dart';

@freezed
class RegisterProductState with _$RegisterProductState {
  const factory RegisterProductState({
    @Default(false) bool isLoading,
    @Default(false) bool isSuccess,
    RegisterProductRequest? request,
    RegisterProductRequest? updateRequest,
    String? errorMessage,
    String? urlImage,
    List<String>? listUrlImages,
    List<ProductImageRegister>? images,
    @Default(false) bool isUploadingImage,
    String? upFileImageFailure,
    @Default(false) bool showImageSelectionDialog,
    String? selectedImageTemplate,
    @Default(true) bool isCreateTemplate,
    ProductModel? previewProductModel,
  }) = _RegisterProductState;

  factory RegisterProductState.fromJson(Map<String, dynamic> json) =>
      _$RegisterProductStateFromJson(json);
}
