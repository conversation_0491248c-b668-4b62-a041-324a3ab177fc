// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_product_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RegisterProductStateImpl _$$RegisterProductStateImplFromJson(
        Map<String, dynamic> json) =>
    _$RegisterProductStateImpl(
      isLoading: json['isLoading'] as bool? ?? false,
      isSuccess: json['isSuccess'] as bool? ?? false,
      request: json['request'] == null
          ? null
          : RegisterProductRequest.fromJson(
              json['request'] as Map<String, dynamic>),
      updateRequest: json['updateRequest'] == null
          ? null
          : RegisterProductRequest.fromJson(
              json['updateRequest'] as Map<String, dynamic>),
      errorMessage: json['errorMessage'] as String?,
      urlImage: json['urlImage'] as String?,
      listUrlImages: (json['listUrlImages'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => ProductImageRegister.fromJson(e as Map<String, dynamic>))
          .toList(),
      isUploadingImage: json['isUploadingImage'] as bool? ?? false,
      upFileImageFailure: json['upFileImageFailure'] as String?,
      showImageSelectionDialog:
          json['showImageSelectionDialog'] as bool? ?? false,
      selectedImageTemplate: json['selectedImageTemplate'] as String?,
      isCreateTemplate: json['isCreateTemplate'] as bool? ?? true,
      previewProductModel: json['previewProductModel'] == null
          ? null
          : ProductModel.fromJson(
              json['previewProductModel'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$RegisterProductStateImplToJson(
        _$RegisterProductStateImpl instance) =>
    <String, dynamic>{
      'isLoading': instance.isLoading,
      'isSuccess': instance.isSuccess,
      'request': instance.request,
      'updateRequest': instance.updateRequest,
      'errorMessage': instance.errorMessage,
      'urlImage': instance.urlImage,
      'listUrlImages': instance.listUrlImages,
      'images': instance.images,
      'isUploadingImage': instance.isUploadingImage,
      'upFileImageFailure': instance.upFileImageFailure,
      'showImageSelectionDialog': instance.showImageSelectionDialog,
      'selectedImageTemplate': instance.selectedImageTemplate,
      'isCreateTemplate': instance.isCreateTemplate,
      'previewProductModel': instance.previewProductModel,
    };
