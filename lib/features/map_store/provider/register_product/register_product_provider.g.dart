// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_product_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$registerProductNotifierHash() =>
    r'9a4129c6fe004ba728d4c6fa5c16239e601513eb';

/// See also [RegisterProductNotifier].
@ProviderFor(RegisterProductNotifier)
final registerProductNotifierProvider =
    NotifierProvider<RegisterProductNotifier, RegisterProductState>.internal(
  RegisterProductNotifier.new,
  name: r'registerProductNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$registerProductNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RegisterProductNotifier = Notifier<RegisterProductState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
