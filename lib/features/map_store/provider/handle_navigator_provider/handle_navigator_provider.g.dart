// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'handle_navigator_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$handleNavigatorNotifierHash() =>
    r'f6e17289206565a58556fd5ec4b1a4308139963c';

/// See also [HandleNavigatorNotifier].
@ProviderFor(HandleNavigatorNotifier)
final handleNavigatorNotifierProvider =
    NotifierProvider<HandleNavigatorNotifier, String>.internal(
  HandleNavigatorNotifier.new,
  name: r'handleNavigatorNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$handleNavigatorNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HandleNavigatorNotifier = Notifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
