import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/features/map_store/provider/info_cabinet/info_cabinet_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'info_cabinet_provider.g.dart';

@riverpod
class InfoCabinetNotifier extends _$InfoCabinetNotifier {
  @override
  InfoCabinetState build() {
    return const InfoCabinetState();
  }

  Future<void> loadStoreDetail(int id, String role) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response =
          await ref.read(getStoreRepositoryProvider).getStoreDetail(id, role);
      state = state.copyWith(
        store: response.data,
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }
}
