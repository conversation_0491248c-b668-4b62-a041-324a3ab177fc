// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'info_cabinet_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$InfoCabinetState {
  StoreDetailModel? get store => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of InfoCabinetState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InfoCabinetStateCopyWith<InfoCabinetState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InfoCabinetStateCopyWith<$Res> {
  factory $InfoCabinetStateCopyWith(
          InfoCabinetState value, $Res Function(InfoCabinetState) then) =
      _$InfoCabinetStateCopyWithImpl<$Res, InfoCabinetState>;
  @useResult
  $Res call({StoreDetailModel? store, bool isLoading, String? error});

  $StoreDetailModelCopyWith<$Res>? get store;
}

/// @nodoc
class _$InfoCabinetStateCopyWithImpl<$Res, $Val extends InfoCabinetState>
    implements $InfoCabinetStateCopyWith<$Res> {
  _$InfoCabinetStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InfoCabinetState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? store = freezed,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      store: freezed == store
          ? _value.store
          : store // ignore: cast_nullable_to_non_nullable
              as StoreDetailModel?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of InfoCabinetState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $StoreDetailModelCopyWith<$Res>? get store {
    if (_value.store == null) {
      return null;
    }

    return $StoreDetailModelCopyWith<$Res>(_value.store!, (value) {
      return _then(_value.copyWith(store: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$InfoCabinetStateImplCopyWith<$Res>
    implements $InfoCabinetStateCopyWith<$Res> {
  factory _$$InfoCabinetStateImplCopyWith(_$InfoCabinetStateImpl value,
          $Res Function(_$InfoCabinetStateImpl) then) =
      __$$InfoCabinetStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({StoreDetailModel? store, bool isLoading, String? error});

  @override
  $StoreDetailModelCopyWith<$Res>? get store;
}

/// @nodoc
class __$$InfoCabinetStateImplCopyWithImpl<$Res>
    extends _$InfoCabinetStateCopyWithImpl<$Res, _$InfoCabinetStateImpl>
    implements _$$InfoCabinetStateImplCopyWith<$Res> {
  __$$InfoCabinetStateImplCopyWithImpl(_$InfoCabinetStateImpl _value,
      $Res Function(_$InfoCabinetStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of InfoCabinetState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? store = freezed,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_$InfoCabinetStateImpl(
      store: freezed == store
          ? _value.store
          : store // ignore: cast_nullable_to_non_nullable
              as StoreDetailModel?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$InfoCabinetStateImpl implements _InfoCabinetState {
  const _$InfoCabinetStateImpl(
      {this.store, this.isLoading = false, this.error});

  @override
  final StoreDetailModel? store;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;

  @override
  String toString() {
    return 'InfoCabinetState(store: $store, isLoading: $isLoading, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InfoCabinetStateImpl &&
            (identical(other.store, store) || other.store == store) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, store, isLoading, error);

  /// Create a copy of InfoCabinetState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InfoCabinetStateImplCopyWith<_$InfoCabinetStateImpl> get copyWith =>
      __$$InfoCabinetStateImplCopyWithImpl<_$InfoCabinetStateImpl>(
          this, _$identity);
}

abstract class _InfoCabinetState implements InfoCabinetState {
  const factory _InfoCabinetState(
      {final StoreDetailModel? store,
      final bool isLoading,
      final String? error}) = _$InfoCabinetStateImpl;

  @override
  StoreDetailModel? get store;
  @override
  bool get isLoading;
  @override
  String? get error;

  /// Create a copy of InfoCabinetState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InfoCabinetStateImplCopyWith<_$InfoCabinetStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
