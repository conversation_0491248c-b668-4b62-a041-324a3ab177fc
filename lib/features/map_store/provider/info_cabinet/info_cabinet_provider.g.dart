// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'info_cabinet_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$infoCabinetNotifierHash() =>
    r'24c00a0f7e4c350d04474c6b63de07593c83e354';

/// See also [InfoCabinetNotifier].
@ProviderFor(InfoCabinetNotifier)
final infoCabinetNotifierProvider =
    AutoDisposeNotifierProvider<InfoCabinetNotifier, InfoCabinetState>.internal(
  InfoCabinetNotifier.new,
  name: r'infoCabinetNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$infoCabinetNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InfoCabinetNotifier = AutoDisposeNotifier<InfoCabinetState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
