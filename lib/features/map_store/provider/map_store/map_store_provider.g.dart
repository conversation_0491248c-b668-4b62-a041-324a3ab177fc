// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'map_store_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mapStoreNotifierHash() => r'566c5af2bfec7f650c1fa7bf0a2af9beeadb30b8';

/// See also [MapStoreNotifier].
@ProviderFor(MapStoreNotifier)
final mapStoreNotifierProvider =
    NotifierProvider<MapStoreNotifier, MapStoreState>.internal(
  MapStoreNotifier.new,
  name: r'mapStoreNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mapStoreNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MapStoreNotifier = Notifier<MapStoreState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
