// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'map_store_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MapStoreState {
  bool get isLoading => throw _privateConstructorUsedError;
  List<NearbyStoreModel> get stores => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  LatLng? get currentLocation => throw _privateConstructorUsedError;
  String? get currentName => throw _privateConstructorUsedError;
  String? get currentOpen => throw _privateConstructorUsedError;
  String? get currentClose => throw _privateConstructorUsedError;
  String? get currentProvince => throw _privateConstructorUsedError;
  double get currentZoom => throw _privateConstructorUsedError;
  LatLng? get firstStoreLocation => throw _privateConstructorUsedError;
  bool? get isZoomCurrentLocation => throw _privateConstructorUsedError;

  /// Create a copy of MapStoreState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MapStoreStateCopyWith<MapStoreState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MapStoreStateCopyWith<$Res> {
  factory $MapStoreStateCopyWith(
          MapStoreState value, $Res Function(MapStoreState) then) =
      _$MapStoreStateCopyWithImpl<$Res, MapStoreState>;
  @useResult
  $Res call(
      {bool isLoading,
      List<NearbyStoreModel> stores,
      String? error,
      LatLng? currentLocation,
      String? currentName,
      String? currentOpen,
      String? currentClose,
      String? currentProvince,
      double currentZoom,
      LatLng? firstStoreLocation,
      bool? isZoomCurrentLocation});
}

/// @nodoc
class _$MapStoreStateCopyWithImpl<$Res, $Val extends MapStoreState>
    implements $MapStoreStateCopyWith<$Res> {
  _$MapStoreStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MapStoreState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? stores = null,
    Object? error = freezed,
    Object? currentLocation = freezed,
    Object? currentName = freezed,
    Object? currentOpen = freezed,
    Object? currentClose = freezed,
    Object? currentProvince = freezed,
    Object? currentZoom = null,
    Object? firstStoreLocation = freezed,
    Object? isZoomCurrentLocation = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      stores: null == stores
          ? _value.stores
          : stores // ignore: cast_nullable_to_non_nullable
              as List<NearbyStoreModel>,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      currentLocation: freezed == currentLocation
          ? _value.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as LatLng?,
      currentName: freezed == currentName
          ? _value.currentName
          : currentName // ignore: cast_nullable_to_non_nullable
              as String?,
      currentOpen: freezed == currentOpen
          ? _value.currentOpen
          : currentOpen // ignore: cast_nullable_to_non_nullable
              as String?,
      currentClose: freezed == currentClose
          ? _value.currentClose
          : currentClose // ignore: cast_nullable_to_non_nullable
              as String?,
      currentProvince: freezed == currentProvince
          ? _value.currentProvince
          : currentProvince // ignore: cast_nullable_to_non_nullable
              as String?,
      currentZoom: null == currentZoom
          ? _value.currentZoom
          : currentZoom // ignore: cast_nullable_to_non_nullable
              as double,
      firstStoreLocation: freezed == firstStoreLocation
          ? _value.firstStoreLocation
          : firstStoreLocation // ignore: cast_nullable_to_non_nullable
              as LatLng?,
      isZoomCurrentLocation: freezed == isZoomCurrentLocation
          ? _value.isZoomCurrentLocation
          : isZoomCurrentLocation // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MapStoreStateImplCopyWith<$Res>
    implements $MapStoreStateCopyWith<$Res> {
  factory _$$MapStoreStateImplCopyWith(
          _$MapStoreStateImpl value, $Res Function(_$MapStoreStateImpl) then) =
      __$$MapStoreStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      List<NearbyStoreModel> stores,
      String? error,
      LatLng? currentLocation,
      String? currentName,
      String? currentOpen,
      String? currentClose,
      String? currentProvince,
      double currentZoom,
      LatLng? firstStoreLocation,
      bool? isZoomCurrentLocation});
}

/// @nodoc
class __$$MapStoreStateImplCopyWithImpl<$Res>
    extends _$MapStoreStateCopyWithImpl<$Res, _$MapStoreStateImpl>
    implements _$$MapStoreStateImplCopyWith<$Res> {
  __$$MapStoreStateImplCopyWithImpl(
      _$MapStoreStateImpl _value, $Res Function(_$MapStoreStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of MapStoreState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? stores = null,
    Object? error = freezed,
    Object? currentLocation = freezed,
    Object? currentName = freezed,
    Object? currentOpen = freezed,
    Object? currentClose = freezed,
    Object? currentProvince = freezed,
    Object? currentZoom = null,
    Object? firstStoreLocation = freezed,
    Object? isZoomCurrentLocation = freezed,
  }) {
    return _then(_$MapStoreStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      stores: null == stores
          ? _value._stores
          : stores // ignore: cast_nullable_to_non_nullable
              as List<NearbyStoreModel>,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      currentLocation: freezed == currentLocation
          ? _value.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as LatLng?,
      currentName: freezed == currentName
          ? _value.currentName
          : currentName // ignore: cast_nullable_to_non_nullable
              as String?,
      currentOpen: freezed == currentOpen
          ? _value.currentOpen
          : currentOpen // ignore: cast_nullable_to_non_nullable
              as String?,
      currentClose: freezed == currentClose
          ? _value.currentClose
          : currentClose // ignore: cast_nullable_to_non_nullable
              as String?,
      currentProvince: freezed == currentProvince
          ? _value.currentProvince
          : currentProvince // ignore: cast_nullable_to_non_nullable
              as String?,
      currentZoom: null == currentZoom
          ? _value.currentZoom
          : currentZoom // ignore: cast_nullable_to_non_nullable
              as double,
      firstStoreLocation: freezed == firstStoreLocation
          ? _value.firstStoreLocation
          : firstStoreLocation // ignore: cast_nullable_to_non_nullable
              as LatLng?,
      isZoomCurrentLocation: freezed == isZoomCurrentLocation
          ? _value.isZoomCurrentLocation
          : isZoomCurrentLocation // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$MapStoreStateImpl extends _MapStoreState {
  const _$MapStoreStateImpl(
      {this.isLoading = false,
      final List<NearbyStoreModel> stores = const [],
      this.error,
      this.currentLocation,
      this.currentName,
      this.currentOpen,
      this.currentClose,
      this.currentProvince,
      this.currentZoom = 14.0,
      this.firstStoreLocation,
      this.isZoomCurrentLocation})
      : _stores = stores,
        super._();

  @override
  @JsonKey()
  final bool isLoading;
  final List<NearbyStoreModel> _stores;
  @override
  @JsonKey()
  List<NearbyStoreModel> get stores {
    if (_stores is EqualUnmodifiableListView) return _stores;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_stores);
  }

  @override
  final String? error;
  @override
  final LatLng? currentLocation;
  @override
  final String? currentName;
  @override
  final String? currentOpen;
  @override
  final String? currentClose;
  @override
  final String? currentProvince;
  @override
  @JsonKey()
  final double currentZoom;
  @override
  final LatLng? firstStoreLocation;
  @override
  final bool? isZoomCurrentLocation;

  @override
  String toString() {
    return 'MapStoreState(isLoading: $isLoading, stores: $stores, error: $error, currentLocation: $currentLocation, currentName: $currentName, currentOpen: $currentOpen, currentClose: $currentClose, currentProvince: $currentProvince, currentZoom: $currentZoom, firstStoreLocation: $firstStoreLocation, isZoomCurrentLocation: $isZoomCurrentLocation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MapStoreStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            const DeepCollectionEquality().equals(other._stores, _stores) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.currentName, currentName) ||
                other.currentName == currentName) &&
            (identical(other.currentOpen, currentOpen) ||
                other.currentOpen == currentOpen) &&
            (identical(other.currentClose, currentClose) ||
                other.currentClose == currentClose) &&
            (identical(other.currentProvince, currentProvince) ||
                other.currentProvince == currentProvince) &&
            (identical(other.currentZoom, currentZoom) ||
                other.currentZoom == currentZoom) &&
            (identical(other.firstStoreLocation, firstStoreLocation) ||
                other.firstStoreLocation == firstStoreLocation) &&
            (identical(other.isZoomCurrentLocation, isZoomCurrentLocation) ||
                other.isZoomCurrentLocation == isZoomCurrentLocation));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      const DeepCollectionEquality().hash(_stores),
      error,
      currentLocation,
      currentName,
      currentOpen,
      currentClose,
      currentProvince,
      currentZoom,
      firstStoreLocation,
      isZoomCurrentLocation);

  /// Create a copy of MapStoreState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MapStoreStateImplCopyWith<_$MapStoreStateImpl> get copyWith =>
      __$$MapStoreStateImplCopyWithImpl<_$MapStoreStateImpl>(this, _$identity);
}

abstract class _MapStoreState extends MapStoreState {
  const factory _MapStoreState(
      {final bool isLoading,
      final List<NearbyStoreModel> stores,
      final String? error,
      final LatLng? currentLocation,
      final String? currentName,
      final String? currentOpen,
      final String? currentClose,
      final String? currentProvince,
      final double currentZoom,
      final LatLng? firstStoreLocation,
      final bool? isZoomCurrentLocation}) = _$MapStoreStateImpl;
  const _MapStoreState._() : super._();

  @override
  bool get isLoading;
  @override
  List<NearbyStoreModel> get stores;
  @override
  String? get error;
  @override
  LatLng? get currentLocation;
  @override
  String? get currentName;
  @override
  String? get currentOpen;
  @override
  String? get currentClose;
  @override
  String? get currentProvince;
  @override
  double get currentZoom;
  @override
  LatLng? get firstStoreLocation;
  @override
  bool? get isZoomCurrentLocation;

  /// Create a copy of MapStoreState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MapStoreStateImplCopyWith<_$MapStoreStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
