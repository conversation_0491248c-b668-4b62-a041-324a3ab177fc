import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/core/service/location_service.dart';
import 'package:kitemite_app/features/map_store/provider/map_store/map_store_state.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'map_store_provider.g.dart';

@Riverpod(keepAlive: true)
class MapStoreNotifier extends _$MapStoreNotifier {
  @override
  MapStoreState build() {
    return const MapStoreState();
  }

  Future<void> loadNearbyStores({
    required LatLng location,
    required String role,
  }) async {
    state = state.copyWith(
        isLoading: true, error: null, isZoomCurrentLocation: false);

    try {
      bool isGuest = ref.watch(modeNotifierProvider).mode == ModeAccount.guest;
      // BaseListResponse<NearbyStoreModel> response;
      var response =
          await ref.read(getStoreRepositoryProvider).getNearbyStoresGuest(
                location.latitude,
                location.longitude,
              );

      print("-------------- call api buyer  map ${response.data?.length} ");
      // if (isGuest) {
      //   response = await repo.getNearbyStoresGuest(
      //     location.latitude,
      //     location.longitude,
      //   );
      // } else {
      //   response = await repo.getNearbyStores(
      //     location.latitude,
      //     location.longitude,
      //     null,
      //     null,
      //     null,
      //     null,
      //     role,
      //   );
      // }

      // final stores = response.data ?? [];

      state = state.copyWith(
        stores: response.data ?? [],
        currentLocation: location,
        isLoading: false,
        error: null,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }

  Future<void> loadNearbyStoresSeller({
    LatLng? location,
    required String role,
    required bool isSearch,
  }) async {
    state = state.copyWith(
        isLoading: true, error: null, isZoomCurrentLocation: false);

    try {
      final response =
          await ref.read(getStoreRepositoryProvider).getNearbyStores(
                isSearch
                    ? null
                    : location?.latitude ?? state.currentLocation!.latitude,
                isSearch
                    ? null
                    : location?.longitude ?? state.currentLocation!.longitude,
                state.currentName,
                state.currentOpen,
                state.currentClose,
                state.currentProvince,
                role,
              );

      final stores = (response.data ?? [])
          .where((store) =>
              store.availableShelvesCount != null &&
              store.availableShelvesCount! > 0)
          .toList();
      print("-------------- call api seller map ${stores.length} ");
      // Store the location of the first store when isSearch is true
      LatLng? firstStoreLocation;
      if (isSearch && stores.isNotEmpty) {
        final firstStore = stores.first;
        final lat = double.tryParse(firstStore.latitude ?? "0");
        final lng = double.tryParse(firstStore.longitude ?? "0");
        if (lat != null && lng != null) {
          firstStoreLocation = LatLng(lat, lng);
        }
      }

      state = state.copyWith(
        stores: stores,
        currentLocation: location,
        isLoading: false,
        error: null,
        firstStoreLocation: firstStoreLocation,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }

  Future<void> getStoreDetails({
    required String storeId,
    required String role,
  }) async {
    state = state.copyWith(
        isLoading: true, error: null, isZoomCurrentLocation: false);

    try {
      final response =
          await ref.read(getStoreRepositoryProvider).getStoreDetail(
                int.parse(storeId),
                role,
              );

      // You can handle the response here, perhaps navigate to a detail screen
      // or update a state variable with the store details

      state = state.copyWith(
        isLoading: false,
        error: null,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }

  void updateMapPosition(LatLng position, double zoom) {
    state = state.copyWith(
      currentLocation: position,
      currentZoom: zoom,
    );
  }

  void resetFirstStoreLocation() {
    state = state.copyWith(
      firstStoreLocation: null,
    );
  }

  void updateSearchLocation(
      String? name, String? open, String? close, String? province) {
    state = state.copyWith(
      currentName: name,
      currentOpen: open,
      currentClose: close,
      currentProvince: province,
    );
  }

  void resetSearchLocation() {
    state = state.copyWith(
      currentName: null,
      currentOpen: null,
      currentClose: null,
      currentProvince: null,
      firstStoreLocation: null,
    );
  }

  Future<void> reloadMap() async {
    state = state.copyWith(firstStoreLocation: null, isLoading: true);
    final currentLocation = await LocationService.getCurrentLocation();
    resetSearchLocation();
    print("------------------------- current location $currentLocation");
    if (currentLocation != null) {
      var isRoleSeller =
          ref.watch(modeNotifierProvider).mode == ModeAccount.seller;
      print("------------------- isRoleSeller: $isRoleSeller");
      isRoleSeller
          ? loadNearbyStoresSeller(
              location: currentLocation, role: "seller", isSearch: false)
          : loadNearbyStores(
              location: currentLocation,
              role: "buyer",
            );

      state = state.copyWith(isZoomCurrentLocation: true);
    }
  }
}
