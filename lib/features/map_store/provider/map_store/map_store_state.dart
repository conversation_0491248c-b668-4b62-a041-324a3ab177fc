import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kitemite_app/model/response/store/nearby_store_model.dart';

part 'map_store_state.freezed.dart';

@freezed
class MapStoreState with _$MapStoreState {
  const factory MapStoreState({
    @Default(false) bool isLoading,
    @Default([]) List<NearbyStoreModel> stores,
    String? error,
    LatLng? currentLocation,
    String? currentName,
    String? currentOpen,
    String? currentClose,
    String? currentProvince,
    @Default(14.0) double currentZoom,
    LatLng? firstStoreLocation,
    bool? isZoomCurrentLocation
  }) = _MapStoreState;

  const MapStoreState._();
}
