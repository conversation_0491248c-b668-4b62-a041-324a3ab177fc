// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_lock_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$openLockNotifierHash() => r'e4e1150bb5d3d230f6537666d11ed4194bb36969';

/// See also [OpenLockNotifier].
@ProviderFor(OpenLockNotifier)
final openLockNotifierProvider =
    AutoDisposeNotifierProvider<OpenLockNotifier, BLELockState>.internal(
  OpenLockNotifier.new,
  name: r'openLockNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$openLockNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OpenLockNotifier = AutoDisposeNotifier<BLELockState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
