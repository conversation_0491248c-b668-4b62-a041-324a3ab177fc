import 'dart:async';
import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:kitemite_app/core/utils/lock_utils.dart';
import 'package:kitemite_app/features/shopping_cart/model/blue_info_model.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'open_lock_provider.g.dart';

String NOTIY =
    Platform.isAndroid ? "00002902-0000-1000-8000-00805f9b34fb" : "0000";
String SERVICE_DATA = "FFF0";
String DATA_CHARACTERISTIC = "FFF2";
String NOTIY_CHARACTERISTIC = "FFF1";

enum BleStatus {
  init,
  start,
  notHavePermission,
  failed,
  scanning,
  noDevice,
  connected,
  openSuccess
}

class BLELockState extends Equatable {
  final BleStatus status;
  const BLELockState({this.status = BleStatus.init});

  @override
  List<Object?> get props => [status];

  BLELockState copyWith({
    BleStatus? status,
  }) {
    return BLELockState(
      status: status ?? this.status,
    );
  }
}

@riverpod
class OpenLockNotifier extends _$OpenLockNotifier {
  BluetoothCharacteristic? blueCharacteristicWrite;
  BluetoothCharacteristic? blueCharacteristicNotify;
  BluetoothConnectionState? _bleConnection;
  BluetoothConnectionState? get bleConnection => _bleConnection;
  bool isConnecting = false;
  bool _servicesDiscovered = false;
  StreamSubscription<int>? mtuSubscription;
  BluetoothDevice? bluetoothDevice;
  BlueInfo? _ble;
  final bool _pairBle = false;
  bool get pairBle => _pairBle;

  String lockId = "";

  @override
  BLELockState build() {
    return const BLELockState();
  }

  void initialize(String lockValue) {
    lockId = lockValue;
  }

  void scanBle() {
    requestPermission();
  }

  Future<void> requestPermission({bool isScan = true}) async {
    try {
      state = state.copyWith(status: BleStatus.start);

      if (Platform.isIOS) {
        _connectBluetooth();
      } else {
        Map<Permission, PermissionStatus> statuses = await [
          Permission.location,
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
        ].request();

        for (var status in statuses.entries) {
          if (status.key == Permission.location) {
            if (status.value.isGranted) {
              //debugPrint('Location permission granted');
            } else {
              // debugPrint("Location permission not granted");
            }
          } else if (status.key == Permission.bluetoothScan) {
            if (status.value.isGranted) {
              _connectBluetooth();
              //  debugPrint('Bluetooth scan permission granted');
            } else {
              // debugPrint('Bluetooth scan permission not granted');
            }
          } else if (status.key == Permission.bluetoothConnect) {
            if (status.value.isGranted) {
              //debugPrint('Bluetooth connect permission granted');
            } else {
              //    debugPrint('Bluetooth connect permission not granted');
            }
          }
        }

        final permission = await Permission.bluetoothScan.status;
        if (permission.isGranted || permission.isLimited) {
          if (isScan) {
            _connectBluetooth();
          }
        } else {
          await Permission.bluetoothScan.request();
        }
      }
    } catch (e) {
      state = state.copyWith(status: BleStatus.failed);
    }
  }

  Future<void> _connectBluetooth() async {
    isConnecting = false;
    _servicesDiscovered = false;
    var subscription =
        FlutterBluePlus.adapterState.listen((BluetoothAdapterState bleState) {
      if (bleState == BluetoothAdapterState.on) {
        // usually start scanning, connecting, etc
      } else  if (bleState == BluetoothAdapterState.off) { 
        state = state.copyWith(status: BleStatus.notHavePermission);
       
      }
    });
    final devices = await _scanDevices();
    if (devices.isNotEmpty && devices.first.name == lockId) {
      final device = devices.first;
      connectDevice(device);
    } else {
      state = state.copyWith(status: BleStatus.noDevice);
    }
  }

  void connectDevice(BlueInfo ble) async {
    final device = ble.result?.device;
    if (device != null) {
      bluetoothDevice = device;
      _ble = ble;
      device.disconnect();
      await device.connect();

      // Discover services
      List<BluetoothService> services = await device.discoverServices();
      for (BluetoothService service in services) {
        if (service.uuid.str.toUpperCase() == SERVICE_DATA) {
          interactWithCharacteristics(service);
          setNotifications();
        }
      }
    }
  }

  void interactWithCharacteristics(BluetoothService service) async {
    try {
      for (BluetoothCharacteristic characteristic in service.characteristics) {
        // Check for TX (FFF6)
        if (characteristic.uuid.str.toUpperCase() == DATA_CHARACTERISTIC) {
          blueCharacteristicWrite = characteristic;
          print("LOCK connected");
          state = state.copyWith(status: BleStatus.connected);
        }
        if (characteristic.uuid.str.toUpperCase() == NOTIY_CHARACTERISTIC) {
          blueCharacteristicNotify = characteristic;
        }
      }
    } catch (e) {
      print("Error interacting with characteristics: $e");
    }
  }

  Future<void> setNotifications() async {
    try {
      await blueCharacteristicNotify?.setNotifyValue(true);
      await blueCharacteristicNotify?.read();
      print('Notifying: ${blueCharacteristicNotify?.isNotifying}');
      final subscription = blueCharacteristicNotify?.onValueReceived.listen((
        value,
      ) {
        String receiveResult = bytesToHexString(value, value.length);
        print("response$value");
        print("response receive$receiveResult");
        if (receiveResult.split(" ").length == 12) {
          final lists = receiveResult.split(" ");
          if (lists[lists.length - 2] == "01") {
            state = state.copyWith(status: BleStatus.openSuccess);
          }
        }
        // !!! PRINTS FALSE

        //  eventBus.fire(DataDeviceEvent(BleSDK.DataParsingWithData(value)));
      });

      // cleanup: cancel subscription when disconnected
      if (subscription != null) {
        bluetoothDevice?.cancelWhenDisconnected(subscription);
      }
    } catch (e) {
      //  await subscription.cancel();
      print('Failed to enable notifications: $e'); // !!! NOT CALLED
    }

    // _requestMtu();
  }

  String bytesToHexString(List<int> bytes, int length) {
    StringBuffer result = StringBuffer();
    for (int i = 0; i < length; i++) {
      String hex = bytes[i].toRadixString(16).padLeft(2, '0').toUpperCase();
      result.write(hex);
      if (i < length - 1) {
        result.write(" "); // Add space between hex values
      }
    }
    return result.toString();
  }

  Future<List<BlueInfo>> _scanDevices() async {
    if (Platform.isAndroid) {
      if ((await FlutterBluePlus.isOn) == false) {
        state = state.copyWith(status: BleStatus.notHavePermission);
        return [];
      }
    }

    await FlutterBluePlus.adapterState
        .where((val) => val == BluetoothAdapterState.on)
        .first;

    StreamSubscription<List<ScanResult>>? scanSubscription;
    List<BlueInfo> devices = [];
    state = state.copyWith(status: BleStatus.scanning);
    try {
      if ((await FlutterBluePlus.isSupported) == false) return [];

      //  Start scanning if not already scanning

      // Listen to scan results
      scanSubscription = FlutterBluePlus.scanResults.listen(
        (results) {
          List<ScanResult> scannedDevices = results
              .where(
                (r) =>
                    r.device.platformName.isNotEmpty &&
                    r.advertisementData.manufacturerData[449] != null,
              )
              .toList();
          scannedDevices.sort((a, b) => b.rssi.compareTo(a.rssi));

          devices = scannedDevices.map((deviceResult) {
            return BlueInfo(
              name: deviceResult.device.platformName,
              mac: deviceResult.device.remoteId.str,
              rssi: deviceResult.rssi,
              result: deviceResult,
            );
          }).toList();
          print(devices);
        },
        onError: (e) {
          print('bleFindDevices error: $e');
        },
      );

      if (!FlutterBluePlus.isScanningNow) {
        FlutterBluePlus.startScan(
          timeout: const Duration(seconds: 5),
          withServices: [], // Add specific service UUIDs if needed
        );
      }
      // Wait for the scan to complete
      await Future.delayed(const Duration(seconds: 5));

      // Stop scanning
      await FlutterBluePlus.stopScan();
    } finally {
      // Cancel subscription to avoid memory leaks
      await scanSubscription?.cancel();
    }
    return devices;
  }

  void _getInfo() {
    List<String> cmdArr = List.filled(2, '');
    final String macAddress;
    if (Platform.isIOS) {
      final idMac = _ble?.result?.advertisementData.manufacturerData.values
          .toList()
          .first;
      final abssc = bytesToHexString(idMac!, idMac.length);
      final macBytes = abssc.split(" ").sublist(0, 4).join();
      macAddress = 'C101$macBytes';
    } else {
      macAddress = _ble!.mac!.replaceAll(":", "");
    }
    print(macAddress);
    String cmdStr = "01";
    // 滚动码
    String gdStr = "00";
    // 数据长度
    String dataLen = "000d";

    String hexTimeStamp = getHexTime();
    String strAllData = cmdStr + gdStr + dataLen + macAddress + hexTimeStamp;
    // 检验和

    String chkIdx = getHexAddition(strAllData);
    String sendCmdStrEncBefore = strAllData + chkIdx;
    // print(sendCmdStrEncBefore);
    cmdArr[0] = sendCmdStrEncBefore;
    cmdArr[1] = sendCmdStrEncBefore;
    print(cmdArr[1]);
    writeData(hexStringToBytes(cmdArr[1]));

    // abc.writeData(hexStringToBytes("532025B9EC0A4C807AAF554665B25338"));
  }

  void openLock() {
    _getInfo();
    List<String> cmdArr = List.filled(2, '');
    final String macAddress;
    if (Platform.isIOS) {
      final idMac = _ble?.result?.advertisementData.manufacturerData.values
          .toList()
          .first;
      final abssc = bytesToHexString(idMac!, idMac.length);
      final macBytes = abssc.split(" ").sublist(0, 4).join();
      macAddress = 'C101$macBytes';
    } else {
      macAddress = _ble!.mac!.replaceAll(":", "");
    }
    print(macAddress);
    // 'C1010101BB3B';
    String cmdStr = "E0";
    // 滚动码
    String gdStr = "00";
    // 数据长度
    String dataLen = "000d";
    // 时间戳
    String hexTimeStamp = getHexTime();
    String strAllData = cmdStr + gdStr + dataLen + macAddress + hexTimeStamp;
    // 检验和
    String chkIdx = getHexAddition(strAllData);

    String sendCmdStrEncBefore = strAllData + chkIdx;
    cmdArr[0] = sendCmdStrEncBefore;

    // 加密：
    String sendDataEncStr = cmdTransferEncipher(
      getKeyLongs(),
      macAddress + hexTimeStamp + chkIdx,
    );

    String sendCmdStrEncAfter = cmdStr + gdStr + dataLen + sendDataEncStr;
    cmdArr[1] = sendCmdStrEncAfter;
    print(sendCmdStrEncAfter);

    writeData(hexStringToBytes(sendCmdStrEncAfter));
  }

  void writeData(List<int> data) {
    if (bluetoothDevice != null) {
      blueCharacteristicWrite?.write(data, withoutResponse: false);
    } else {}
  }

  String cmdTransferEncipher(List<int> keyLongs, String cmdStr) {
    String encCmdStr = "";

    // Convert Long[] to Short[] (16-bit integers)
    List<int> key = TEA.longToShort(keyLongs);

    // Split the command string into 16-character (8-byte) segments
    List<String> list = strCutToList(cmdStr, 16);

    // Encrypt each segment
    for (String str in list) {
      List<int> shorts = convertFromString(
        str,
      ); // Convert hex string to Short[]
      List<int> secretInfo = TEA.encipher(shorts, key); // Encrypt with TEA

      // Convert encrypted Short[] to hex string
      for (int value in secretInfo) {
        encCmdStr += value
            .toRadixString(16)
            .padLeft(2, '0'); // Ensure 2-digit hex format
      }
    }

    return encCmdStr;
  }
}
