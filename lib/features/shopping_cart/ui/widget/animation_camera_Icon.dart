import 'package:flutter/material.dart';

class ShakingCameraIcon extends StatefulWidget {
  const ShakingCameraIcon({super.key});

  @override
  State<ShakingCameraIcon> createState() => _ShakingCameraIconState();
}

class _ShakingCameraIconState extends State<ShakingCameraIcon>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..repeat(reverse: true); // Lặp vô hạn

    _shakeAnimation = Tween<double>(begin: -4, end: 4).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticIn),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: child,
        );
      },
      child: const Icon(Icons.camera_alt),
    );
  }
}
