
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/animation/simple_animated_icon.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/image_picker/common_image_picker.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/shopping_cart/provider/cart_provider.dart';
import 'package:kitemite_app/features/shopping_cart/provider/check_cart/check_cart_provider.dart';
import 'package:kitemite_app/features/shopping_cart/widget/dialog_open_lock_widget.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/request/order/create_order_request.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:open_settings_plus/core/open_settings_plus.dart';
import 'package:permission_handler/permission_handler.dart';

class ShoppingCartItem extends ConsumerWidget {
  final String image;
  final String title;
  final String price;
  final int quantity;
  final String productId;
  final String shelfCode;
  final String itemId;
  final bool isCabinetUnlocked;

  ShoppingCartItem({
    super.key,
    required this.image,
    required this.title,
    required this.price,
    required this.quantity,
    required this.productId,
    required this.shelfCode,
    required this.itemId,
    this.isCabinetUnlocked = false,
  });

  final CommonImagePicker _imagePicker = CommonImagePicker(
    maxSizeInBytes: 50 * 1024 * 1024,
    allowedFormats: ['jpg', 'jpeg', 'png', 'gif'],
  );

  Future<void> _pickImage(BuildContext context, ImageSource source,
      WidgetRef ref, String productId, String itemId) async {
    try {
      final XFile? pickedFile =
          await _imagePicker.pickImage(context, source: source);
      if (pickedFile != null) {
        final base64Image =
            await _imagePicker.convertToBase64(File(pickedFile.path));
        await ref
            .read(cartNotifierProvider.notifier)
            .upFileImage(base64Image, productId, itemId);

        // Show error if upload failed
        final state = ref.read(cartNotifierProvider);
        if (state.upFileImageFailure != null) {
          context.showErrorSnackBar(state.upFileImageFailure!);
        }
      }
    } catch (e) {
      checkCameraPermission(context);
      // context.showErrorSnackBar('Failed to pick image: $e');
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(cartNotifierProvider);
    final productImage = state.productImages.firstWhere(
      (element) =>
          element.id.toString() == productId && element.itemId == itemId,
      orElse: () =>
          OrderProduct(id: int.parse(productId), img: "", itemId: itemId),
    );
    final isUploading = state.isUploadingImage;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          BaseCachedNetworkImage(
            imageUrl: image,
            width: 80.w,
            height: 80.h,
            fit: BoxFit.cover,
            borderRadius: BorderRadius.circular(16),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: SizedBox(
              height: 80.h,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(title,
                            style: AppTextStyles.bold(14.sp,
                                color: AppColors.textPrimary)),
                      ),
                      if (isCabinetUnlocked)
                        GestureDetector(
                          onTap: () {
                            _pickImage(context, ImageSource.camera, ref,
                                productId, itemId);
                          },
                          child: productImage.img != ""
                              ? BaseCachedNetworkImage(
                                  imageUrl: productImage.img,
                                  width: 40.w,
                                  height: 40.h,
                                  fit: BoxFit.cover,
                                  borderRadius: BorderRadius.circular(8),
                                )
                              : const Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: SimpleAnimatedIcon(
                                      childWidget: Icon(Icons.camera_alt)),
                                ),
                        ),
                    ],
                  ),
                  const Spacer(),
                  Center(
                    child: Row(
                      children: [
                        Text(
                          "No.$shelfCode",
                          style: AppTextStyles.regular(12.sp,
                              color: AppColors.textLightSecondary),
                        ),
                        SizedBox(width: 32.w),
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                  text: "合計: ",
                                  style: AppTextStyles.regular(12.sp,
                                      color: AppColors.textLightSecondary)),
                              TextSpan(
                                text: price,
                                style: AppTextStyles.regular(12.sp,
                                    color: AppColors.primary),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

Future<void> checkCameraPermission(BuildContext context) async {
  var status = await Permission.camera.status;

  if (status.isDenied || status.isPermanentlyDenied) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.camera_alt,
                  size: 32.w,
                  color: AppColors.primary,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                'カメラの使用許可が必要です。',
                style: AppTextStyles.bold(18.sp, color: AppColors.textPrimary),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text(
                'カメラの使用許可を付与してください。',
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        side: const BorderSide(color: AppColors.border),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'キャンセル',
                        style: AppTextStyles.regular(14.sp,
                            color: AppColors.textLightSecondary),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        Navigator.pop(context);
                        await openAppSettings();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        '設定へ',
                        style: AppTextStyles.bold(14.sp, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  } else if (status.isGranted) {
    // Quyền đã được cấp, tiếp tục xử lý
  } else {
    // Thử yêu cầu quyền lại
    var result = await Permission.camera.request();
    if (result.isGranted) {
      // Quyền được cấp sau khi yêu cầu
    }
  }
}
