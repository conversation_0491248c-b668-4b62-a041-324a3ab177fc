import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/webview/custom_webview.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/login/provider/auth_notifier.dart';
import 'package:kitemite_app/features/login/provider/payment_notifier.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/features/shopping_cart/provider/cart_provider.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class TotalPaymentScreen extends ConsumerStatefulWidget {
  const TotalPaymentScreen({super.key, required this.shopName});
  final String shopName;

  @override
  ConsumerState<TotalPaymentScreen> createState() => _TotalPaymentScreenState();
}

class _TotalPaymentScreenState extends ConsumerState<TotalPaymentScreen> {
  final TextEditingController _pointsController = TextEditingController();
  bool _shouldShowWebView = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cartNotifierProvider.notifier).createDraftOrder();

      var userId = ref.watch(authNotifierProvider).value?.userInfoModel?.id;
      ref
          .read(paymentNotifierProvider.notifier)
          .getPaymentInformation(userId ?? 0);
    });
  }

  @override
  void dispose() {
    _pointsController.dispose();
    super.dispose();
  }

  void _showAddCardDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.credit_card,
                  size: 32.w,
                  color: AppColors.primary,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                'クレジットカードがまだ登録されていません。',
                style: AppTextStyles.bold(18.sp, color: AppColors.textPrimary),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text(
                'お買い物を完了するにはクレジットカード情報の登録が必要です。追加してショッピングを続けてくださいね。',
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        side: const BorderSide(color: AppColors.border),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        '買い物をやめる',
                        style: AppTextStyles.regular(14.sp,
                            color: AppColors.textLightSecondary),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        context
                            .push(RouterPaths.registerPayment, extra: true)
                            .then(
                          (value) {
                            var userId = ref
                                .watch(authNotifierProvider)
                                .value
                                ?.userInfoModel
                                ?.id;
                            ref
                                .read(paymentNotifierProvider.notifier)
                                .getPaymentInformation(userId ?? 0);
                          },
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'カードを追加',
                        style: AppTextStyles.bold(14.sp, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final paymentCard = ref.watch(paymentNotifierProvider).paymentInfos;
    final userPoint =
        ref.watch(profileProviderProvider).value?.profile?.point ?? 0;
    final profile = ref.watch(profileProviderProvider).value?.profile;
    final cartState = ref.watch(cartNotifierProvider);
    final draftOrder = cartState.draftOrder;
    final totalAmount = (draftOrder?.totalPaymentAmount ?? 0) -
        (int.tryParse(_pointsController.text) ?? 0);
    final isTotalNegative = totalAmount < 0;
    final totalAmountString = totalAmount.priceString;
    final discountAmount = int.tryParse(_pointsController.text) ?? 0;
    final discountAmountString = "${discountAmount.priceString}円";
    final isPointsExceeded = discountAmount > userPoint;

    // Watch for error changes
    ref.listen<String?>(
      cartNotifierProvider.select((state) => state.error),
      (previous, next) {
        if (next != null && next.isNotEmpty) {
          context.showErrorSnackBar(next);
        }
      },
    );

    // Watch for order success
    ref.listen<bool>(
      cartNotifierProvider.select((state) => state.isOrderSuccess),
      (previous, next) {
        if (next) {
          context.push(RouterPaths.resultPayment);
        }
      },
    );
    ref.listen<String?>(
      cartNotifierProvider.select((state) => state.redirectUrl),
      (previous, next) {
        if (previous == null && next != null && _shouldShowWebView) {
          if (next.isNotEmpty) {
            ref.read(cartNotifierProvider.notifier).setLoading();
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => Scaffold(
                  body: Stack(
                    children: [
                      CustomWebView(
                        url: next,
                        title: '決済',
                        onPageFinished: (url) {},
                      ),
                      SafeArea(
                        top: true,
                        child: Align(
                          alignment: Alignment.topRight,
                          child: GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: Container(
                              margin: EdgeInsets.all(8.w),
                              padding: EdgeInsets.all(8.w),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.5),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 24.w,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ).then((value) async {
              // Check order status and wait for the result
              ref.read(cartNotifierProvider.notifier).setLoading();
              final isSuccess = await ref
                  .read(cartNotifierProvider.notifier)
                  .checkOrderStatus();

              if (mounted) {
                if (isSuccess) {
                  context.push(RouterPaths.resultPayment);
                } else {
                  context.push(RouterPaths.resultPaymentFail);
                }
              }
            });
          } else {
            context.showErrorSnackBar('Invalid payment URL');
          }
        }
      },
    );

    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: cartState.isLoading ? '' : 'お支払い方法',
        onTap: () => context.pop(),
      ),
      body: cartState.isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : SafeArea(
              top: false,
              child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                          child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 16.h),
                            Container(
                              padding: EdgeInsets.all(16.w),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.shopName,
                                    style: AppTextStyles.bold(18.sp,
                                        color: AppColors.textPrimary),
                                  ),
                                  SizedBox(height: 16.h),
                                  Text(
                                    'お会計',
                                    style: AppTextStyles.bold(18.sp,
                                        color: AppColors.textPrimary),
                                  ),
                                  SizedBox(height: 16.h),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'ポイント',
                                            style: AppTextStyles.regular(14.sp,
                                                color: AppColors
                                                    .textLightSecondary),
                                          ),
                                          Text(
                                            '(所有ポイント: $userPoint)',
                                            style: AppTextStyles.regular(12.sp,
                                                fontStyle: FontStyle.italic,
                                                color: AppColors
                                                    .textLightSecondary),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        width: 100.w,
                                        child: TextField(
                                          enabled: true,
                                          controller: _pointsController,
                                          decoration: InputDecoration(
                                            hintText: '0',
                                            hintStyle: AppTextStyles.regular(
                                                12.sp,
                                                color: AppColors
                                                    .textLightSecondary),
                                            isDense: true,
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                              horizontal: 8.w,
                                              vertical: 8.h,
                                            ),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8.r),
                                              borderSide: BorderSide(
                                                  color: isPointsExceeded
                                                      ? Colors.red
                                                      : AppColors.border),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8.r),
                                              borderSide: BorderSide(
                                                  color: isPointsExceeded
                                                      ? Colors.red
                                                      : AppColors.border),
                                            ),
                                          ),
                                          keyboardType: TextInputType.number,
                                          textAlign: TextAlign.right,
                                          onChanged: (value) {
                                            final enteredPoints =
                                                int.tryParse(value) ?? 0;
                                            if (enteredPoints > userPoint) {
                                              context.showErrorSnackBar(
                                                  '利用可能なポイントを超えることはできません。 ($userPoint)');
                                            }
                                            setState(() {});
                                          },
                                          style: AppTextStyles.regular(14.sp,
                                              color: AppColors.textPrimary),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16.h),
                                  rowItem(
                                    '小計',
                                    '${draftOrder?.totalPaymentAmount?.priceString ?? ''}円',
                                    valueStyle: AppTextStyles.regular(14.sp,
                                        color: AppColors.textPrimary),
                                  ),
                                  SizedBox(height: 12.h),
                                  rowItem(
                                    '割引',
                                    _pointsController.text.isEmpty ||
                                            _pointsController.text == '0'
                                        ? '0円'
                                        : '-$discountAmountString',
                                    valueStyle: AppTextStyles.regular(14.sp,
                                        color: AppColors.textPrimary),
                                  ),
                                  Divider(
                                      height: 32.h, color: AppColors.border),
                                  rowItem('合計', '$totalAmountString円',
                                      valueStyle: AppTextStyles.bold(16.sp,
                                          color: Colors.red),
                                      titleStyle: AppTextStyles.bold(16.sp,
                                          color: AppColors.textPrimary)),
                                  SizedBox(height: 8.h),
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: Text(
                                      '(税込)',
                                      style: AppTextStyles.regular(12.sp,
                                          color: AppColors.textPrimary,
                                          fontStyle: FontStyle.italic),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              '※決済情報の入力は、信頼性の高いGMOペイメントゲートウェイの専用ページにて行われます。通信はSSL暗号化により保護されており、安心してご利用いただけます。',
                              style: AppTextStyles.regular(14.sp,
                                  color: AppColors.textPrimary),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              '※当アプリではクレジットカード情報などの決済情報は保持いたしません。',
                              style: AppTextStyles.regular(14.sp,
                                  color: AppColors.textPrimary),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              '※決済完了後、自動的に当アプリへ戻ります。',
                              style: AppTextStyles.regular(14.sp,
                                  color: AppColors.textPrimary),
                            ),
                          ],
                        ),
                      )),
                      SizedBox(height: 12.h),
                      CommonButton(
                        text: 'お支払いに進む',
                        onPressed: isTotalNegative || isPointsExceeded
                            ? () {
                                if (isTotalNegative) {
                                  context.showErrorSnackBar(
                                      '合計金額をマイナスにすることはできません。');
                                } else if (isPointsExceeded) {
                                  context.showErrorSnackBar(
                                      '利用可能なポイントを超えることはできません。 ($userPoint)');
                                }
                              }
                            : () {
                                if (paymentCard?.isEmpty ?? true) {
                                  _showAddCardDialog();
                                  return;
                                }
                                setState(() {
                                  _shouldShowWebView = true;
                                });
                                ref
                                    .read(cartNotifierProvider.notifier)
                                    .updateDraftOrderWithPoints(discountAmount);
                                ref
                                    .read(cartNotifierProvider.notifier)
                                    .createOrder();
                              },
                      ),
                      SizedBox(height: 16.h),
                      Center(
                        child: GestureDetector(
                          onTap: () => context.pop(),
                          child: Text(
                            'キャンセル',
                            style: AppTextStyles.bold(15.sp,
                                color: AppColors.textLightSecondary),
                          ),
                        ),
                      ),
                      SizedBox(height: 16.h),
                    ],
                  )),
            ),
    );
  }

  Widget rowItem(String label, String value,
      {required TextStyle valueStyle, TextStyle? titleStyle}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: titleStyle ??
              AppTextStyles.regular(14.sp, color: AppColors.textLightSecondary),
        ),
        Text(value, style: valueStyle),
      ],
    );
  }
}
