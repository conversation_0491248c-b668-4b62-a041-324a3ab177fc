import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/features/home/<USER>/home_buyer/home_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/features/shopping_cart/provider/cart_provider.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class ResultPaymentFail extends ConsumerStatefulWidget {
  const ResultPaymentFail({super.key});

  @override
  ConsumerState<ResultPaymentFail> createState() => _ResultPaymentFailState();
}

class _ResultPaymentFailState extends ConsumerState<ResultPaymentFail> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // Listen for cart state changes
    ref.listen(cartNotifierProvider, (previous, next) {});

    return PopScope(
        canPop: false,
        child: Scaffold(
            body: SafeArea(
          top: false,
          child: Container(
            color: AppColors.white,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 38.w),
              child: Column(
                children: [
                  SizedBox(height: MediaQuery.of(context).size.height * 0.15),
                  Assets.kitemite1920x10802Png
                      .image(width: 137.w, height: 116.h),
                  SizedBox(height: 32.h),
                  Text("決済が失敗しました。ご確認の上、再度ご購入ください。",
                      textAlign: TextAlign.center,
                      style: AppTextStyles.bold(20.sp,
                          color: AppColors.textProfile)),
                  const Spacer(),
                  CommonButton(
                    onPressed: () {
                      ref.read(profileProviderProvider.notifier).loadProfile();
                      ref.read(homeNotifierProvider.notifier).loadProducts(
                            isRefresh: true,
                          );
                      context.go(RouterPaths.home, extra: true);
                    },
                    customTitle: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text("ホームへ",
                            style: AppTextStyles.bold(14.sp,
                                color: AppColors.textPrimary)),
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h),
                ],
              ),
            ),
          ),
        )));
  }
}
