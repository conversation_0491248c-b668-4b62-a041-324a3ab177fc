import 'package:flutter/cupertino.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

///蓝牙设备
///Bluetooth devices
enum RssiType { excellent, good, poor }

class BlueInfo {
  String? name;
  int? rssi;
  String? mac;
  ScanResult? result;

  BlueInfo(
      {@required this.name,
      @required this.rssi,
      @required this.mac,
      this.result});

  @override
  String toString() {
    return 'BlueInfo(name: $name, rssi: $rssi, mac: $mac, result: $result)';
  }


}

