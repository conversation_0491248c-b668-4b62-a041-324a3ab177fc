// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_cart_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkCartNotifierHash() => r'06bb94f92f68512138b57c690d9d99ec000e64af';

/// See also [CheckCartNotifier].
@ProviderFor(CheckCartNotifier)
final checkCartNotifierProvider =
    NotifierProvider<CheckCartNotifier, CheckCartState>.internal(
  CheckCartNotifier.new,
  name: r'checkCartNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$checkCartNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckCartNotifier = Notifier<CheckCartState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
