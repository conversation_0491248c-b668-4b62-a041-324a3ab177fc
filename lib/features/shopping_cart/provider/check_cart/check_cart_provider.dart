import 'package:flutter/material.dart';
import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/product/product_repository.dart';
import 'package:kitemite_app/features/shopping_cart/provider/check_cart/check_cart_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'check_cart_provider.g.dart';

@Riverpod(keepAlive: true)
class CheckCartNotifier extends _$CheckCartNotifier {
  late final ProductRepository _productRepository;

  @override
  CheckCartState build() {
    _productRepository = ref.read(getProductRepositoryProvider);
    return const CheckCartState();
  }

  Future<void> checkCart(int productId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await _productRepository.checkCart(productId);
      state = state.copyWith(
        cartItems: response.data ?? [],
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }
}
