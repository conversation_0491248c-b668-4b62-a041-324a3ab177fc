// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'check_cart_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CheckCartState {
  List<CheckCartModel> get cartItems => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of CheckCartState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckCartStateCopyWith<CheckCartState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckCartStateCopyWith<$Res> {
  factory $CheckCartStateCopyWith(
          CheckCartState value, $Res Function(CheckCartState) then) =
      _$CheckCartStateCopyWithImpl<$Res, CheckCartState>;
  @useResult
  $Res call({List<CheckCartModel> cartItems, bool isLoading, String? error});
}

/// @nodoc
class _$CheckCartStateCopyWithImpl<$Res, $Val extends CheckCartState>
    implements $CheckCartStateCopyWith<$Res> {
  _$CheckCartStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckCartState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cartItems = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      cartItems: null == cartItems
          ? _value.cartItems
          : cartItems // ignore: cast_nullable_to_non_nullable
              as List<CheckCartModel>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CheckCartStateImplCopyWith<$Res>
    implements $CheckCartStateCopyWith<$Res> {
  factory _$$CheckCartStateImplCopyWith(_$CheckCartStateImpl value,
          $Res Function(_$CheckCartStateImpl) then) =
      __$$CheckCartStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CheckCartModel> cartItems, bool isLoading, String? error});
}

/// @nodoc
class __$$CheckCartStateImplCopyWithImpl<$Res>
    extends _$CheckCartStateCopyWithImpl<$Res, _$CheckCartStateImpl>
    implements _$$CheckCartStateImplCopyWith<$Res> {
  __$$CheckCartStateImplCopyWithImpl(
      _$CheckCartStateImpl _value, $Res Function(_$CheckCartStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckCartState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cartItems = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_$CheckCartStateImpl(
      cartItems: null == cartItems
          ? _value._cartItems
          : cartItems // ignore: cast_nullable_to_non_nullable
              as List<CheckCartModel>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$CheckCartStateImpl implements _CheckCartState {
  const _$CheckCartStateImpl(
      {final List<CheckCartModel> cartItems = const [],
      this.isLoading = false,
      this.error})
      : _cartItems = cartItems;

  final List<CheckCartModel> _cartItems;
  @override
  @JsonKey()
  List<CheckCartModel> get cartItems {
    if (_cartItems is EqualUnmodifiableListView) return _cartItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cartItems);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;

  @override
  String toString() {
    return 'CheckCartState(cartItems: $cartItems, isLoading: $isLoading, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckCartStateImpl &&
            const DeepCollectionEquality()
                .equals(other._cartItems, _cartItems) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_cartItems), isLoading, error);

  /// Create a copy of CheckCartState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckCartStateImplCopyWith<_$CheckCartStateImpl> get copyWith =>
      __$$CheckCartStateImplCopyWithImpl<_$CheckCartStateImpl>(
          this, _$identity);
}

abstract class _CheckCartState implements CheckCartState {
  const factory _CheckCartState(
      {final List<CheckCartModel> cartItems,
      final bool isLoading,
      final String? error}) = _$CheckCartStateImpl;

  @override
  List<CheckCartModel> get cartItems;
  @override
  bool get isLoading;
  @override
  String? get error;

  /// Create a copy of CheckCartState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckCartStateImplCopyWith<_$CheckCartStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
