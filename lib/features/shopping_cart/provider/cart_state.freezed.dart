// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cart_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CartState {
  List<Map<String, dynamic>> get cartItems =>
      throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  DraftOrderResponse? get draftOrder => throw _privateConstructorUsedError;
  bool get isUploadingImage => throw _privateConstructorUsedError;
  String? get upFileImageFailure => throw _privateConstructorUsedError;
  List<OrderProduct> get productImages => throw _privateConstructorUsedError;
  CreateOrderRequest? get createOrderRequest =>
      throw _privateConstructorUsedError;
  String? get redirectUrl => throw _privateConstructorUsedError;
  List<Map<String, dynamic>> get cabinets => throw _privateConstructorUsedError;
  Map<String, bool> get cabinetLockStatus => throw _privateConstructorUsedError;
  bool get isOrderSuccess => throw _privateConstructorUsedError;
  bool get isCheckOrderSuccessPayment => throw _privateConstructorUsedError;
  int? get orderId => throw _privateConstructorUsedError;

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CartStateCopyWith<CartState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CartStateCopyWith<$Res> {
  factory $CartStateCopyWith(CartState value, $Res Function(CartState) then) =
      _$CartStateCopyWithImpl<$Res, CartState>;
  @useResult
  $Res call(
      {List<Map<String, dynamic>> cartItems,
      bool isLoading,
      String? error,
      DraftOrderResponse? draftOrder,
      bool isUploadingImage,
      String? upFileImageFailure,
      List<OrderProduct> productImages,
      CreateOrderRequest? createOrderRequest,
      String? redirectUrl,
      List<Map<String, dynamic>> cabinets,
      Map<String, bool> cabinetLockStatus,
      bool isOrderSuccess,
      bool isCheckOrderSuccessPayment,
      int? orderId});

  $CreateOrderRequestCopyWith<$Res>? get createOrderRequest;
}

/// @nodoc
class _$CartStateCopyWithImpl<$Res, $Val extends CartState>
    implements $CartStateCopyWith<$Res> {
  _$CartStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cartItems = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? draftOrder = freezed,
    Object? isUploadingImage = null,
    Object? upFileImageFailure = freezed,
    Object? productImages = null,
    Object? createOrderRequest = freezed,
    Object? redirectUrl = freezed,
    Object? cabinets = null,
    Object? cabinetLockStatus = null,
    Object? isOrderSuccess = null,
    Object? isCheckOrderSuccessPayment = null,
    Object? orderId = freezed,
  }) {
    return _then(_value.copyWith(
      cartItems: null == cartItems
          ? _value.cartItems
          : cartItems // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      draftOrder: freezed == draftOrder
          ? _value.draftOrder
          : draftOrder // ignore: cast_nullable_to_non_nullable
              as DraftOrderResponse?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      productImages: null == productImages
          ? _value.productImages
          : productImages // ignore: cast_nullable_to_non_nullable
              as List<OrderProduct>,
      createOrderRequest: freezed == createOrderRequest
          ? _value.createOrderRequest
          : createOrderRequest // ignore: cast_nullable_to_non_nullable
              as CreateOrderRequest?,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      cabinets: null == cabinets
          ? _value.cabinets
          : cabinets // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      cabinetLockStatus: null == cabinetLockStatus
          ? _value.cabinetLockStatus
          : cabinetLockStatus // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      isOrderSuccess: null == isOrderSuccess
          ? _value.isOrderSuccess
          : isOrderSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckOrderSuccessPayment: null == isCheckOrderSuccessPayment
          ? _value.isCheckOrderSuccessPayment
          : isCheckOrderSuccessPayment // ignore: cast_nullable_to_non_nullable
              as bool,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CreateOrderRequestCopyWith<$Res>? get createOrderRequest {
    if (_value.createOrderRequest == null) {
      return null;
    }

    return $CreateOrderRequestCopyWith<$Res>(_value.createOrderRequest!,
        (value) {
      return _then(_value.copyWith(createOrderRequest: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CartStateImplCopyWith<$Res>
    implements $CartStateCopyWith<$Res> {
  factory _$$CartStateImplCopyWith(
          _$CartStateImpl value, $Res Function(_$CartStateImpl) then) =
      __$$CartStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Map<String, dynamic>> cartItems,
      bool isLoading,
      String? error,
      DraftOrderResponse? draftOrder,
      bool isUploadingImage,
      String? upFileImageFailure,
      List<OrderProduct> productImages,
      CreateOrderRequest? createOrderRequest,
      String? redirectUrl,
      List<Map<String, dynamic>> cabinets,
      Map<String, bool> cabinetLockStatus,
      bool isOrderSuccess,
      bool isCheckOrderSuccessPayment,
      int? orderId});

  @override
  $CreateOrderRequestCopyWith<$Res>? get createOrderRequest;
}

/// @nodoc
class __$$CartStateImplCopyWithImpl<$Res>
    extends _$CartStateCopyWithImpl<$Res, _$CartStateImpl>
    implements _$$CartStateImplCopyWith<$Res> {
  __$$CartStateImplCopyWithImpl(
      _$CartStateImpl _value, $Res Function(_$CartStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cartItems = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? draftOrder = freezed,
    Object? isUploadingImage = null,
    Object? upFileImageFailure = freezed,
    Object? productImages = null,
    Object? createOrderRequest = freezed,
    Object? redirectUrl = freezed,
    Object? cabinets = null,
    Object? cabinetLockStatus = null,
    Object? isOrderSuccess = null,
    Object? isCheckOrderSuccessPayment = null,
    Object? orderId = freezed,
  }) {
    return _then(_$CartStateImpl(
      cartItems: null == cartItems
          ? _value._cartItems
          : cartItems // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      draftOrder: freezed == draftOrder
          ? _value.draftOrder
          : draftOrder // ignore: cast_nullable_to_non_nullable
              as DraftOrderResponse?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      productImages: null == productImages
          ? _value._productImages
          : productImages // ignore: cast_nullable_to_non_nullable
              as List<OrderProduct>,
      createOrderRequest: freezed == createOrderRequest
          ? _value.createOrderRequest
          : createOrderRequest // ignore: cast_nullable_to_non_nullable
              as CreateOrderRequest?,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      cabinets: null == cabinets
          ? _value._cabinets
          : cabinets // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      cabinetLockStatus: null == cabinetLockStatus
          ? _value._cabinetLockStatus
          : cabinetLockStatus // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      isOrderSuccess: null == isOrderSuccess
          ? _value.isOrderSuccess
          : isOrderSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckOrderSuccessPayment: null == isCheckOrderSuccessPayment
          ? _value.isCheckOrderSuccessPayment
          : isCheckOrderSuccessPayment // ignore: cast_nullable_to_non_nullable
              as bool,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$CartStateImpl implements _CartState {
  const _$CartStateImpl(
      {final List<Map<String, dynamic>> cartItems = const [],
      this.isLoading = false,
      this.error = null,
      this.draftOrder = null,
      this.isUploadingImage = false,
      this.upFileImageFailure,
      final List<OrderProduct> productImages = const [],
      this.createOrderRequest = null,
      this.redirectUrl = null,
      final List<Map<String, dynamic>> cabinets = const [],
      final Map<String, bool> cabinetLockStatus = const {},
      this.isOrderSuccess = false,
      this.isCheckOrderSuccessPayment = false,
      this.orderId = null})
      : _cartItems = cartItems,
        _productImages = productImages,
        _cabinets = cabinets,
        _cabinetLockStatus = cabinetLockStatus;

  final List<Map<String, dynamic>> _cartItems;
  @override
  @JsonKey()
  List<Map<String, dynamic>> get cartItems {
    if (_cartItems is EqualUnmodifiableListView) return _cartItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cartItems);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final String? error;
  @override
  @JsonKey()
  final DraftOrderResponse? draftOrder;
  @override
  @JsonKey()
  final bool isUploadingImage;
  @override
  final String? upFileImageFailure;
  final List<OrderProduct> _productImages;
  @override
  @JsonKey()
  List<OrderProduct> get productImages {
    if (_productImages is EqualUnmodifiableListView) return _productImages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_productImages);
  }

  @override
  @JsonKey()
  final CreateOrderRequest? createOrderRequest;
  @override
  @JsonKey()
  final String? redirectUrl;
  final List<Map<String, dynamic>> _cabinets;
  @override
  @JsonKey()
  List<Map<String, dynamic>> get cabinets {
    if (_cabinets is EqualUnmodifiableListView) return _cabinets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cabinets);
  }

  final Map<String, bool> _cabinetLockStatus;
  @override
  @JsonKey()
  Map<String, bool> get cabinetLockStatus {
    if (_cabinetLockStatus is EqualUnmodifiableMapView)
      return _cabinetLockStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_cabinetLockStatus);
  }

  @override
  @JsonKey()
  final bool isOrderSuccess;
  @override
  @JsonKey()
  final bool isCheckOrderSuccessPayment;
  @override
  @JsonKey()
  final int? orderId;

  @override
  String toString() {
    return 'CartState(cartItems: $cartItems, isLoading: $isLoading, error: $error, draftOrder: $draftOrder, isUploadingImage: $isUploadingImage, upFileImageFailure: $upFileImageFailure, productImages: $productImages, createOrderRequest: $createOrderRequest, redirectUrl: $redirectUrl, cabinets: $cabinets, cabinetLockStatus: $cabinetLockStatus, isOrderSuccess: $isOrderSuccess, isCheckOrderSuccessPayment: $isCheckOrderSuccessPayment, orderId: $orderId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartStateImpl &&
            const DeepCollectionEquality()
                .equals(other._cartItems, _cartItems) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.draftOrder, draftOrder) ||
                other.draftOrder == draftOrder) &&
            (identical(other.isUploadingImage, isUploadingImage) ||
                other.isUploadingImage == isUploadingImage) &&
            (identical(other.upFileImageFailure, upFileImageFailure) ||
                other.upFileImageFailure == upFileImageFailure) &&
            const DeepCollectionEquality()
                .equals(other._productImages, _productImages) &&
            (identical(other.createOrderRequest, createOrderRequest) ||
                other.createOrderRequest == createOrderRequest) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            const DeepCollectionEquality().equals(other._cabinets, _cabinets) &&
            const DeepCollectionEquality()
                .equals(other._cabinetLockStatus, _cabinetLockStatus) &&
            (identical(other.isOrderSuccess, isOrderSuccess) ||
                other.isOrderSuccess == isOrderSuccess) &&
            (identical(other.isCheckOrderSuccessPayment,
                    isCheckOrderSuccessPayment) ||
                other.isCheckOrderSuccessPayment ==
                    isCheckOrderSuccessPayment) &&
            (identical(other.orderId, orderId) || other.orderId == orderId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_cartItems),
      isLoading,
      error,
      draftOrder,
      isUploadingImage,
      upFileImageFailure,
      const DeepCollectionEquality().hash(_productImages),
      createOrderRequest,
      redirectUrl,
      const DeepCollectionEquality().hash(_cabinets),
      const DeepCollectionEquality().hash(_cabinetLockStatus),
      isOrderSuccess,
      isCheckOrderSuccessPayment,
      orderId);

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CartStateImplCopyWith<_$CartStateImpl> get copyWith =>
      __$$CartStateImplCopyWithImpl<_$CartStateImpl>(this, _$identity);
}

abstract class _CartState implements CartState {
  const factory _CartState(
      {final List<Map<String, dynamic>> cartItems,
      final bool isLoading,
      final String? error,
      final DraftOrderResponse? draftOrder,
      final bool isUploadingImage,
      final String? upFileImageFailure,
      final List<OrderProduct> productImages,
      final CreateOrderRequest? createOrderRequest,
      final String? redirectUrl,
      final List<Map<String, dynamic>> cabinets,
      final Map<String, bool> cabinetLockStatus,
      final bool isOrderSuccess,
      final bool isCheckOrderSuccessPayment,
      final int? orderId}) = _$CartStateImpl;

  @override
  List<Map<String, dynamic>> get cartItems;
  @override
  bool get isLoading;
  @override
  String? get error;
  @override
  DraftOrderResponse? get draftOrder;
  @override
  bool get isUploadingImage;
  @override
  String? get upFileImageFailure;
  @override
  List<OrderProduct> get productImages;
  @override
  CreateOrderRequest? get createOrderRequest;
  @override
  String? get redirectUrl;
  @override
  List<Map<String, dynamic>> get cabinets;
  @override
  Map<String, bool> get cabinetLockStatus;
  @override
  bool get isOrderSuccess;
  @override
  bool get isCheckOrderSuccessPayment;
  @override
  int? get orderId;

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CartStateImplCopyWith<_$CartStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
