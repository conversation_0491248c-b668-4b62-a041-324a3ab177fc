import 'package:hive_ce/hive.dart';
import 'package:kitemite_app/core/data_base/hive_box_names.dart';
import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/auth/auth_repository.dart';
import 'package:kitemite_app/core/repository/product/product_repository.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/features/shopping_cart/provider/cart_state.dart';
import 'package:kitemite_app/model/request/order/create_order_request.dart';
import 'package:kitemite_app/model/response/order/create_order_response.dart';
import 'package:kitemite_app/model/response/order/draft_order_response.dart';
import 'package:kitemite_app/model/response/product/product_hive_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'cart_provider.g.dart';

@Riverpod(keepAlive: true)
class CartNotifier extends _$CartNotifier {
  @override
  CartState build() {
    return const CartState();
  }

  Future<void> loadCartItems() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      print("------------------- load cart items -------------------");
      final box = await Hive.openBox(HiveBoxNames.products);
      final cartItemsList = box.get('cart_items') as List<dynamic>? ?? [];

      print(
          "------------------- cart items ${cartItemsList.length} -------------------");

      // Get current user ID from profile provider
      final currentUserId =
          ref.watch(profileProviderProvider).value?.profile?.id ?? 0;

      print(
          "------------------- current user id $currentUserId -------------------");

      // Filter cart items by current user
      final userCartItems = cartItemsList.where((item) {
        if (item is! Map) return false;
        final userId = item['user_id'];
        print("------------------- item user id $userId -------------------");
        return userId != null && userId == currentUserId;
      }).toList();

      print(
          "------------------- user cart items ${userCartItems.length} -------------------");
      final cabinets = <Map<String, dynamic>>[];
      for (final item in userCartItems) {
        final product = item['product'] as ProductHiveModel;
        final quantity = item['quantity'] as int;
        final addedAt = DateTime.parse(item['added_at'] as String);
        final cabinetCode = product.cabinet?.cabinetCode ?? 'Unknown';
        final storeId = item['id_store'];
        final storeName = item['name_store'];
        final shelfCode = item['shelf_code'];
        // Find or create cabinet
        var cabinet = cabinets.firstWhere(
          (cab) => cab['cabinet_code'] == cabinetCode,
          orElse: () => {
            'cabinet_code': cabinetCode,
            'store_id': storeId,
            'store_name': storeName,
            'items': <Map<String, dynamic>>[],
            'is_locked': true,
            'shelf_code': shelfCode,
          },
        );

        if (!cabinets.contains(cabinet)) {
          cabinets.add(cabinet);
        }

        // Add item to cabinet
        (cabinet['items'] as List).add({
          'id': product.id,
          'image': product.image,
          'title': product.name,
          'price': '${product.salePrice?.priceString()}円',
          'quantity': quantity,
          'added_at': addedAt,
          'shelf_code': shelfCode
        });
      }

      // Sort items by added time within each cabinet
      for (final cabinet in cabinets) {
        (cabinet['items'] as List).sort((a, b) =>
            (b['added_at'] as DateTime).compareTo(a['added_at'] as DateTime));
      }

      state = state.copyWith(
        cabinets: cabinets,
        productImages: [],
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  void updateCabinetLockStatus(String cabinetCode, bool isLocked) {
    final updatedCabinets = state.cabinets.map((cabinet) {
      if (cabinet['cabinet_code'] == cabinetCode) {
        return {
          ...cabinet,
          'is_locked': !isLocked,
        };
      }
      return cabinet;
    }).toList();

    state = state.copyWith(cabinets: updatedCabinets);
  }

  Future<void> removeItem(String itemId) async {
    try {
      final box = await Hive.openBox(HiveBoxNames.products);
      final cartItems = box.get('cart_items') as List<dynamic>? ?? [];
      final cabinetLockStatus =
          (box.get('cabinet_lock_status') as Map<dynamic, dynamic>?)?.map(
                (key, value) => MapEntry(key.toString(), value),
              ) ??
              {};

      // Get current user ID
      final currentUserId =
          ref.watch(profileProviderProvider).value?.profile?.id ?? 0;

      // Parse itemId to get productId and index
      final parts = itemId.split('_');
      final productId = parts[0];
      // final itemIndex = int.parse(parts[1]);

      // Find the cart item with matching product ID and user ID
      final cartItemIndex = cartItems.indexWhere((item) {
        if (item is! Map) return false;
        final product = item['product'] as ProductHiveModel?;
        final userId = item['user_id'];
        return product?.id.toString() == productId && userId == currentUserId;
      });

      if (cartItemIndex != -1) {
        final item = cartItems[cartItemIndex] as Map;
        final quantity = item['quantity'] as int;
        final cabinetCode =
            (item['product'] as ProductHiveModel).cabinet?.cabinetCode;
        final storeId = item['id_store'];
        final storeName = item['name_store'];
        final shelfCode = item['shelf_code'];

        if (quantity > 1) {
          // If quantity > 1, just decrease the quantity
          cartItems[cartItemIndex] = {
            ...item,
            'quantity': quantity - 1,
          };
        } else {
          // If quantity = 1, remove the entire item
          cartItems.removeAt(cartItemIndex);
        }

        await box.put('cart_items', cartItems);

        // Remove only the specific itemId from productImages
        final updatedProductImages = state.productImages.where((product) {
          return product.itemId != itemId;
        }).toList();

        // Rebuild cabinets similar to loadCartItems
        final cabinets = <Map<String, dynamic>>[];
        for (final cartItem in cartItems) {
          if (cartItem is! Map) continue;
          final userId = cartItem['user_id'];
          if (userId != currentUserId) continue;

          final product = cartItem['product'] as ProductHiveModel;
          final quantity = cartItem['quantity'] as int;
          final addedAt = DateTime.parse(cartItem['added_at'] as String);
          final itemCabinetCode = product.cabinet?.cabinetCode ?? 'Unknown';
          final itemStoreId = cartItem['id_store'];
          final itemStoreName = cartItem['name_store'];
          final itemShelfCode = cartItem['shelf_code'];

          // Find or create cabinet
          var cabinet = cabinets.firstWhere(
            (cab) => cab['cabinet_code'] == itemCabinetCode,
            orElse: () => {
              'cabinet_code': itemCabinetCode,
              'store_id': itemStoreId,
              'store_name': itemStoreName,
              'items': <Map<String, dynamic>>[],
              'is_locked': cabinetLockStatus[itemCabinetCode] ?? true,
              'shelf_code': itemShelfCode,
            },
          );

          if (!cabinets.contains(cabinet)) {
            cabinets.add(cabinet);
          }

          // Add item to cabinet
          (cabinet['items'] as List).add({
            'id': product.id,
            'image': product.image,
            'title': product.name,
            'price': '${product.salePrice?.priceString()}円',
            'quantity': quantity,
            'added_at': addedAt,
            'shelf_code': itemShelfCode
          });
        }

        // Sort items by added time within each cabinet
        for (final cabinet in cabinets) {
          (cabinet['items'] as List).sort((a, b) =>
              (b['added_at'] as DateTime).compareTo(a['added_at'] as DateTime));
        }

        state = state.copyWith(
          productImages: updatedProductImages,
          cabinets: cabinets,
        );
      }
    } catch (e) {
      print('Error in removeItem: $e');
      state = state.copyWith(
        error: e.toString(),
      );
    }
  }

  Future<void> createDraftOrder() async {
    state = state.copyWith(isLoading: true);
    try {
      // Get all product IDs without removing duplicates
      final productIds =
          state.productImages.map((product) => product.id).toList();

      final draftOrder = await ref
          .read(getProductRepositoryProvider)
          .createDraftOrder(productIds);
      state = state.copyWith(
        draftOrder: draftOrder.data,
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isLoading: false,
      );
    }
  }

  Future<void> createOrder() async {
    if (state.draftOrder == null) {
      state = state.copyWith(
        error: "Please create draft order first",
        isLoading: false,
      );
      return;
    }

    state = state.copyWith(
      isLoading: true,
      error: null,
      redirectUrl: null,
      isOrderSuccess: false,
    );
    try {
      final request = CreateOrderRequest(
        products: state.productImages,
        draftOrder: DraftOrder(
          userId: state.draftOrder!.userId ?? 0,
          accessId: state.draftOrder!.accessId,
          transactionId: state.draftOrder!.transactionId,
          totalPaymentAmount:
              state.draftOrder!.totalPaymentAmount?.toDouble() ?? 0.0,
          status: state.draftOrder!.status ?? "",
          orderAt: state.draftOrder!.orderAt ?? "",
          totalUsagePoint: state.draftOrder!.totalUsagePoint ?? 0,
          actualPaymentAmount: state.draftOrder!.actualPaymentAmount ?? 0,
        ),
      );

      final response = await ref
          .read(getProductRepositoryProvider)
          .createOrder(state.draftOrder!.userId ?? 0, request);

      if (response.data?.isSuccess ?? false) {
        state = state.copyWith(
          createOrderRequest: request,
          isOrderSuccess: true,
          orderId: response.data?.id,
          error: null,
          isLoading: false,
        );
      } else if (response.data?.redirectUrl != null) {
        state = state.copyWith(
          createOrderRequest: request,
          redirectUrl: response.data?.redirectUrl,
          orderId: response.data?.id,
          error: null,
          isLoading: true,
        );
      } else {
        state = state.copyWith(
          error: "No redirect URL received",
          isLoading: false,
        );
      }
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isLoading: false,
      );
    }
  }

  Future<void> upFileImage(
      String imageBase64, String productId, String itemId) async {
    state = state.copyWith(
      upFileImageFailure: null,
      isUploadingImage: true,
    );

    try {
      final authRepo = ref.read(getAuthRepositoryProvider);
      final result = await authRepo.upLoadFile(base64: imageBase64);
      final imageUrl = result.links?.first ?? "";

      // Update or add the product image with itemId
      final updatedImages = List<OrderProduct>.from(state.productImages);
      final index = updatedImages.indexWhere(
        (p) => p.id.toString() == productId && p.itemId == itemId,
      );

      if (index != -1) {
        updatedImages[index] = OrderProduct(
          id: int.parse(productId),
          img: imageUrl,
          itemId: itemId,
        );
      } else {
        updatedImages.add(OrderProduct(
          id: int.parse(productId),
          img: imageUrl,
          itemId: itemId,
        ));
      }

      state = state.copyWith(
        isLoading: false,
        isUploadingImage: false,
        productImages: updatedImages,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        isUploadingImage: false,
        upFileImageFailure: e.message,
      );
    }
  }

  Future<void> clearCart() async {
    // Only clear items for current user

    print(
        "-------------------- cleared cart items ${state.productImages.length} -------------------");

    final futures = <Future<void>>[];

    for (int i = 0; i < state.productImages.length; i++) {
      final item = state.productImages[i];
      futures.add(removeItem('${item.id}_$i').then((_) {
        print(
            "-------------------- removed item ${item.id} -------------------");
      }));
    }

    await Future.wait(futures);

    print("-------------------- clear cart items -------------------");

    // state = state.copyWith(
    //   productImages: [],
    //   createOrderRequest: null,
    //   isLoading: false,
    // );
  }

  void updateDraftOrderWithPoints(int points) {
    if (state.draftOrder != null) {
      final updatedDraftOrder = DraftOrderResponse(
        userId: state.draftOrder!.userId,
        accessId: state.draftOrder!.accessId,
        transactionId: state.draftOrder!.transactionId,
        totalPaymentAmount: state.draftOrder!.totalPaymentAmount,
        status: state.draftOrder!.status,
        orderAt: state.draftOrder!.orderAt,
        totalUsagePoint: points,
        actualPaymentAmount:
            (state.draftOrder!.totalPaymentAmount ?? 0) - points,
      );
      state = state.copyWith(draftOrder: updatedDraftOrder);
    }
  }

  Future<bool> checkOrderStatus() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final orderDetail = await ref
          .read(getProductRepositoryProvider)
          .getOrderDetail(state.draftOrder!.userId ?? 0, state.orderId ?? 0);
      var isSuccess = orderDetail.data?.isSuccess ?? false;
      state = state.copyWith(
        isCheckOrderSuccessPayment: isSuccess,
      );
      return isSuccess;
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
      return false;
    }
  }

  void setLoading() {
    state = state.copyWith(isLoading: true);
  }
}
