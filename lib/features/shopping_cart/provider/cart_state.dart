import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/request/order/create_order_request.dart';
import 'package:kitemite_app/model/response/order/draft_order_response.dart';

part 'cart_state.freezed.dart';

@freezed
class CartState with _$CartState {
  const factory CartState({
    @Default([]) List<Map<String, dynamic>> cartItems,
    @Default(false) bool isLoading,
    @Default(null) String? error,
    @Default(null) DraftOrderResponse? draftOrder,
    @Default(false) bool isUploadingImage,
    String? upFileImageFailure,
    @Default([]) List<OrderProduct> productImages,
    @Default(null) CreateOrderRequest? createOrderRequest,
    @Default(null) String? redirectUrl,
    @Default([]) List<Map<String, dynamic>> cabinets,
    @Default({}) Map<String, bool> cabinetLockStatus,
    @Default(false) bool isOrderSuccess,
    @Default(false) bool isCheckOrderSuccessPayment,
    @Default(null) int? orderId,
  }) = _CartState;
}
