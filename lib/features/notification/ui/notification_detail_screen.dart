import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/features/notification/provider/notification_provider.dart';

class NotificationDetailScreen extends ConsumerStatefulWidget {
  final int notificationID;

  const NotificationDetailScreen({
    super.key,
    required this.notificationID,
  });

  @override
  ConsumerState<NotificationDetailScreen> createState() =>
      _NotificationDetailScreenState();
}

class _NotificationDetailScreenState
    extends ConsumerState<NotificationDetailScreen> {
  @override
  void initState() {
    super.initState();
    Future.microtask(() => ref
        .read(notificationNotifierProvider.notifier)
        .fetchNotificationDetail(widget.notificationID));
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(notificationNotifierProvider);

    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: state.detail?.title ?? '',
        onTap: () {
          context.pop();
        },
      ),
      body: _buildBody(state),
    );
  }

  Widget _buildBody(state) {
    if (state.isLoadingDetail) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: ErrorCommonWidget(
          error: state.error!,
          onPressed: () {
            ref
                .read(notificationNotifierProvider.notifier)
                .fetchNotificationDetail(widget.notificationID);
          },
        ),
      );
    }

    final notification = state.detail;

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            notification?.message ?? '',
            style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }
}
