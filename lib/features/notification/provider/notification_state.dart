import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/notification/notification_model.dart';

part 'notification_state.freezed.dart';

@freezed
class NotificationState with _$NotificationState {
  const factory NotificationState({
    @Default(false) bool isLoading,
    @Default([]) List<NotificationModel> notifications,
    @Default(null) NotificationModel? detail,
    @Default(false) bool isLoadingDetail,
    @Default(0) int unseenCount,
    String? error,
  }) = _NotificationState;
}
