import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/event/event_comment_model.dart';

class EventCommentItem extends StatefulWidget {
  final EventCommentModel comment;

  const EventCommentItem({
    super.key,
    required this.comment,
  });

  @override
  State<EventCommentItem> createState() => _EventCommentItemState();
}

class _EventCommentItemState extends State<EventCommentItem> {
  final bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // setState(() {
        //   _isExpanded = !_isExpanded;
        // });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BaseCachedNetworkImage(
              imageUrl: widget.comment.user?.img ?? '',
              width: 40.r,
              height: 40.r,
              borderRadius: BorderRadius.circular(20.r),
              placeholder: CircleAvatar(
                radius: 20.r,
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(20.r),
                    child: Assets.avatarDefault.image()),
              ),
              errorWidget: CircleAvatar(
                radius: 20.r,
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(20.r),
                    child: Assets.avatarDefault.image()),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.comment.user?.username ?? '',
                    style: AppTextStyles.regular(14.sp,
                        color: AppColors.textPrimary),
                  ),
                  const SizedBox(height: 4),
                  Text(widget.comment.content ?? '',
                      // maxLines: _isExpanded ? null : 2,
                      // overflow: _isExpanded ? null : TextOverflow.ellipsis,
                      style: AppTextStyles.regular(14.sp,
                          color: AppColors.textLightSecondary)),
                  const SizedBox(height: 4),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
