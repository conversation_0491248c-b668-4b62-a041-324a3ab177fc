import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/service/location_service.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';
import 'package:url_launcher/url_launcher_string.dart';

class EventDetailMap extends StatefulWidget {
  final EventDetailModel event;

  const EventDetailMap({
    super.key,
    required this.event,
  });

  @override
  State<EventDetailMap> createState() => _EventDetailMapState();
}

class _EventDetailMapState extends State<EventDetailMap> {
  BitmapDescriptor? _markerIcon;

  @override
  void initState() {
    super.initState();
    _loadMarkerIcon();
  }

  Future<void> _loadMarkerIcon() async {
    _markerIcon = await BitmapDescriptor.fromAssetImage(
      const ImageConfiguration(size: Size(48, 48)),
     Platform.isIOS ? Assets.markerIos.path :  Assets.markerStoreNew.path,
    );
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    // Parse latitude and longitude with validation
    final lat = double.tryParse(widget.event.latitude ?? '0') ?? 0;
    final lng = double.tryParse(widget.event.longitude ?? '0') ?? 0;
    final position = LatLng(lat, lng);

    return Stack(
      children: [
        Container(
          height: 144,
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: position,
                zoom: 16,
              ),
              zoomControlsEnabled: false,
              myLocationButtonEnabled: false,
              mapToolbarEnabled: false,
              markers: {
                Marker(
                  markerId: MarkerId(widget.event.id.toString()),
                  position: position,
                  icon: _markerIcon ?? BitmapDescriptor.defaultMarker,
                  // anchor: const Offset(0.5, 0.5),
                  infoWindow: InfoWindow(
                    title: widget.event.name,
                    snippet: widget.event.address,
                  ),
                ),
              },
              onMapCreated: (GoogleMapController controller) {
                controller.animateCamera(
                  CameraUpdate.newCameraPosition(
                    CameraPosition(
                      target: position,
                      zoom: 16,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        Positioned(
          right: 24,
          top: 24,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () {
              _onDirections(position);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.fork_right,
                    size: 24, color: AppColors.textPrimary),
                SizedBox(width: 8.w),
                Text("ルートを確認",
                    style: AppTextStyles.medium(14.sp,
                        color: AppColors.textPrimary)),
                SizedBox(width: 8.w),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _onDirections(LatLng locationStation) async {
    final currentLocation = await LocationService.getCurrentLocation();
    final String url = AppConstants.urlGoogleMap(
        currentLocation?.latitude ?? 0,
        currentLocation?.longitude ?? 0,
        locationStation.latitude,
        locationStation.longitude);
    await canLaunchUrlString(url)
        ? await launchUrlString(url, mode: LaunchMode.externalApplication)
        : throw 'Could not launch $url';
  }
}
