import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';

class EventDetailHeader extends StatelessWidget {
  final EventDetailModel event;

  const EventDetailHeader({
    super.key,
    required this.event,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 250.h,
      width: double.infinity,
      child: BaseCachedNetworkImage(
        imageUrl: event.img ?? "",
        fit: BoxFit.cover,
        borderRadius: BorderRadius.circular(0),
      ),
    );
  }
}
