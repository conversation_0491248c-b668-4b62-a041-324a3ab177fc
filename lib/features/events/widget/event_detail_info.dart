import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/utils/date_time_formatter.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';

class EventDetailInfo extends StatelessWidget {
  final EventDetailModel event;

  const EventDetailInfo({
    super.key,
    required this.event,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16.h),
          Text(
            event.name ?? "",
            style: AppTextStyles.bold(20.sp, color: AppColors.textPrimary),
          ),
          const SizedBox(height: 8),
          Text(
            event.content ?? "",
            style: AppTextStyles.regular(12.sp, color: AppColors.textPrimary),
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            Icons.access_time,
            DateTimeFormatter.formatDateRange(
              DateTime.parse(event.startDate ?? ''),
              DateTime.parse(event.endDate ?? ''),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            event.address ?? "",
            style: AppTextStyles.regular(12.sp,
                color: AppColors.textLightSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 20, color: AppColors.textLightSecondary),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.regular(12.sp,
                color: AppColors.textLightSecondary),
          ),
        ),
      ],
    );
  }
}
