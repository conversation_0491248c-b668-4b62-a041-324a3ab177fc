import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/events/provider/event_detail/event_detail_provider.dart';
import 'package:kitemite_app/features/events/widget/event_comment_item.dart';
import 'package:kitemite_app/features/events/widget/event_detail_header.dart';
import 'package:kitemite_app/features/events/widget/event_detail_info.dart';
import 'package:kitemite_app/features/events/widget/event_detail_map.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class EventDetailScreen extends ConsumerStatefulWidget {
  final int eventId;

  const EventDetailScreen({
    super.key,
    required this.eventId,
  });

  @override
  ConsumerState<EventDetailScreen> createState() => _EventDetailScreenState();
}

class _EventDetailScreenState extends ConsumerState<EventDetailScreen> {
  final _commentController = TextEditingController();
  final _scrollController = ScrollController();
  bool _showEmojiPicker = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(eventDetailNotifierProvider.notifier)
          .loadEventDetail(widget.eventId);
    });

    // Add listener to comment controller to detect changes
    _commentController.addListener(() {
      setState(() {
        // This will rebuild the widget when text changes to show/hide send button
      });
    });

    // Add scroll listener for load more functionality
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        final state = ref.read(eventDetailNotifierProvider);
        if (!state.isLoadingMore && state.currentPage < state.lastPage) {
          ref.read(eventDetailNotifierProvider.notifier).loadMoreComments();
        }
      }
    });
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _toggleEmojiPicker() {
    setState(() {
      _showEmojiPicker = !_showEmojiPicker;
    });
    if (_showEmojiPicker) {
      // Hide keyboard when emoji picker is shown
      FocusScope.of(context).unfocus();
    }
  }

  void _onEmojiSelected(Emoji emoji) {
    final text = _commentController.text;
    final selection = _commentController.selection;
    final newText = text.replaceRange(
      selection.start >= 0 ? selection.start : 0,
      selection.end >= 0 ? selection.end : 0,
      emoji.emoji,
    );
    _commentController.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(
        offset:
            (selection.start >= 0 ? selection.start : 0) + emoji.emoji.length,
      ),
    );
  }

  Future<void> _submitComment(String content) async {
    var isGuest = ref.watch(modeNotifierProvider).mode == ModeAccount.guest;
    if (isGuest) {
      showLoginRequiredDialog(context);
      return;
    }
    if (content.isEmpty) return;
    if (content.length > 100) {
      if (mounted) {
        context.showErrorSnackBar('100文字以内で入力してください。');
      }
      return;
    }
    final state = ref.read(eventDetailNotifierProvider);
    if (state.isCommentLoading) return;

    try {
      await ref.read(eventDetailNotifierProvider.notifier).addComment(content);
      _commentController.clear();
      // Scroll to bottom to show new comment
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    } on AppFailure catch (e) {
      if (mounted) {
        context.showErrorSnackBar(e.message);
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar(e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(eventDetailNotifierProvider);
    final bottomPadding = MediaQuery.of(context).padding.bottom + 48.0;
    final profile = ref.watch(profileProviderProvider).value?.profile;
    return BaseScaffold(
        resizeToAvoidBottomInset: true,
        appBar: CustomAppbar.basic(
          title: state.event?.name ?? '',
          onTap: () => context.pop(),
          actions: [
            if (state.event?.user?.id == profile?.id)
              IconButton(
                icon: const Icon(Icons.create_rounded),
                onPressed: () {
                  context
                      .push(RouterPaths.createEventScreen, extra: state.event)
                      .then(
                    (value) {
                      if (value == true) {
                        ref
                            .read(eventDetailNotifierProvider.notifier)
                            .loadEventDetail(widget.eventId);
                      }
                    },
                  );
                },
              ),
          ],
        ),
        body: SafeArea(
          top: false,
          child: GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              child: Column(
                children: [
                  Expanded(
                    child: state.isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : state.error != null
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      state.error!,
                                      style: const TextStyle(color: Colors.red),
                                    ),
                                    const SizedBox(height: 16),
                                    ElevatedButton(
                                      onPressed: () {
                                        ref
                                            .read(eventDetailNotifierProvider
                                                .notifier)
                                            .loadEventDetail(widget.eventId);
                                      },
                                      child: const Text('再実行'),
                                    ),
                                  ],
                                ),
                              )
                            : SingleChildScrollView(
                                controller: _scrollController,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    EventDetailHeader(event: state.event!),
                                    EventDetailInfo(event: state.event!),
                                    EventDetailMap(event: state.event!),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16.0),
                                      child: Text(
                                        'コメント (${state.totalComments})',
                                        style: AppTextStyles.bold(16.sp,
                                            color: AppColors.textPrimary),
                                      ),
                                    ),
                                    if (state.comments?.isEmpty ?? true)
                                      const Padding(
                                        padding: EdgeInsets.all(16.0),
                                        child: Center(
                                          child: Text('まだコメントはありません。'),
                                        ),
                                      )
                                    else
                                      ListView.builder(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        padding: EdgeInsets.only(
                                            bottom: bottomPadding),
                                        itemCount: (state.comments?.length ??
                                                0) +
                                            (state.currentPage < state.lastPage
                                                ? 1
                                                : 0),
                                        itemBuilder: (context, index) {
                                          if (index == state.comments!.length) {
                                            return Center(
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: state.isLoadingMore
                                                    ? const CircularProgressIndicator()
                                                    : const SizedBox.shrink(),
                                              ),
                                            );
                                          }
                                          final comment =
                                              state.comments![index];
                                          return EventCommentItem(
                                              comment: comment);
                                        },
                                      ),
                                  ],
                                ),
                              ),
                  ),
                  _buildCommentInput(),
                ],
              )),
        ));
  }

  Widget _buildCommentInput() {
    final state = ref.watch(eventDetailNotifierProvider);
    final profile = ref.watch(profileProviderProvider).value?.profile;
    var isGuest = ref.watch(modeNotifierProvider).mode == ModeAccount.guest;
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          child: Row(
            children: [
              isGuest
                  ? CircleAvatar(
                      radius: 20.r,
                      child: ClipRRect(
                          borderRadius: BorderRadius.circular(20.r),
                          child: Assets.avatarDefault.image()),
                    )
                  : BaseCachedNetworkImage(
                      imageUrl: profile?.img ?? '',
                      width: 40.r,
                      height: 40.r,
                      borderRadius: BorderRadius.circular(20.r),
                      placeholder: CircleAvatar(
                        radius: 20.r,
                        child: ClipRRect(
                            borderRadius: BorderRadius.circular(20.r),
                            child: Assets.avatarDefault.image()),
                      ),
                      errorWidget: CircleAvatar(
                        radius: 20.r,
                        child: ClipRRect(
                            borderRadius: BorderRadius.circular(20.r),
                            child: Assets.avatarDefault.image()),
                      ),
                    ),
              const SizedBox(width: 12),
              Expanded(
                child: InputTextField(
                  textController: _commentController,
                  hintText: 'コメントを入力...',
                  maxLine: 1,
                  onFieldSubmitted: (value) => _submitComment(value.trim()),
                  // suffixIcon: state.isCommentLoading
                  //     ? const Column(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           SizedBox(
                  //             width: 24,
                  //             height: 24,
                  //             child: CircularProgressIndicator(),
                  //           ),
                  //         ],
                  //       )
                  //     : IconButton(
                  //         onPressed: _toggleEmojiPicker,
                  //         icon: const Icon(Icons.emoji_emotions_outlined),
                  //       ),
                  ontap: () {
                    // Hide emoji picker when text field is focused
                    if (_showEmojiPicker) {
                      setState(() {
                        _showEmojiPicker = false;
                      });
                    }
                  },
                ),
              ),
              if (_commentController.text.isNotEmpty)
                state.isCommentLoading
                    ? const CircularProgressIndicator()
                    : IconButton(
                        onPressed: () {
                          _submitComment(_commentController.text.trim());
                        },
                        icon: const Icon(Icons.send),
                      ),
            ],
          ),
        ),
        if (_showEmojiPicker)
          SizedBox(
            height: 250,
            child: EmojiPicker(
              onEmojiSelected: (category, emoji) {
                _onEmojiSelected(emoji);
              },
              onBackspacePressed: () {
                // Do something when the user taps the backspace button (optional)
                // Set it to null to hide the Backspace-Button
                _commentController.clear();
              },
              config: const Config(
                height: 250,
                checkPlatformCompatibility: true,
                emojiViewConfig: EmojiViewConfig(
                  emojiSizeMax: 32.0,
                  verticalSpacing: 0,
                  horizontalSpacing: 0,
                  gridPadding: EdgeInsets.zero,
                  backgroundColor: Color(0xFFF2F2F2),
                  buttonMode: ButtonMode.MATERIAL,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
