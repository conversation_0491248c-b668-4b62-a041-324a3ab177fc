import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/custom_choose_avatar/dotted_circle_painter.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/image_picker/common_image_picker.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/tags/string_tag.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/service/location_service.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/events/provider/event_create/create_event_provider.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/model/request/event/create_event_request.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class CreateEventScreen extends ConsumerStatefulWidget {
  const CreateEventScreen({super.key, this.event});
  final EventDetailModel? event;
  @override
  ConsumerState<CreateEventScreen> createState() => _CreateEventScreenState();
}

class _CreateEventScreenState extends ConsumerState<CreateEventScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _startDateController = TextEditingController();
  final _endDateController = TextEditingController();
  final _locationController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();
  File? _imageFile;
  String _latitude = '';
  String _longitude = '';
  LatLng? _currentLocation;
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    _initializeLocation();

    // Check if event is not null (update mode)
    if (widget.event != null) {
      _titleController.text = widget.event!.name ?? '';
      _startDateController.text = widget.event!.startDate?.split('T')[0] ?? '';
      _endDateController.text = widget.event!.endDate?.split('T')[0] ?? '';
      _locationController.text = widget.event!.address ?? '';
      _descriptionController.text = widget.event!.content ?? '';

      // Initialize tags from event
      if (widget.event!.tags != null && widget.event!.tags!.isNotEmpty) {
        List<String> tagStrings =
            widget.event!.tags!.map((tag) => tag.toString()).toList();
        _tagsController.text = tagStrings.join(',');
      }

      if (widget.event!.latitude != null && widget.event!.longitude != null) {
        _latitude = widget.event!.latitude!;
        _longitude = widget.event!.longitude!;
        _currentLocation = LatLng(double.parse(widget.event!.latitude!),
            double.parse(widget.event!.longitude!));
      }

      // If event has image, set the URL in provider state
      // We'll do this after the widget is built using Future.microtask to avoid the Riverpod error
      if (widget.event!.img != null && widget.event!.img!.isNotEmpty) {
        Future.microtask(() {
          ref
              .read(createEventNotifierProvider.notifier)
              .setEventImage(widget.event!.img!);
        });
      }
    }
  }

  Future<void> _initializeLocation() async {
    final currentLocation = await LocationService.getCurrentLocation();
    if (currentLocation != null) {
      setState(() {
        _currentLocation = currentLocation;
        _latitude = currentLocation.latitude.toString();
        _longitude = currentLocation.longitude.toString();
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    _locationController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  Future<void> _selectDate(
      BuildContext context, TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    if (picked != null) {
      controller.text = picked.toIso8601String().split('T')[0];
    }
  }

  void _handleCreateEvent() {
    if (_formKey.currentState!.validate()) {
      if (_startDateController.text.isNotEmpty &&
          _endDateController.text.isNotEmpty) {
        final startDate = DateTime.parse(_startDateController.text);
        final endDate = DateTime.parse(_endDateController.text);

        if (endDate.isBefore(startDate)) {
          context.showErrorSnackBar('完了日は開始日より後である必要があります');
          return;
        }
      }

      // Check if tags are empty or only contain spaces
      final tags = _tagsController.text
          .split(',')
          .map((e) => e.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();
      if (tags.isEmpty) {
        context.showErrorSnackBar('タグを入力してください。');
        return;
      }

      // Check if any tag is longer than 8 characters
      for (final tag in tags) {
        if (tag.length > 8) {
          context.showErrorSnackBar('タグは8文字以内で入力してください。');
          return;
        }
      }

      final request = CreateEventRequest(
          title: _titleController.text,
          description: _descriptionController.text,
          startDate: DateTime.parse(_startDateController.text),
          endDate: DateTime.parse(_endDateController.text),
          location: _locationController.text,
          imageUrl: _imageFile?.path,
          latitude: _latitude,
          longitude: _longitude,
          tags: tags);

      if (widget.event != null) {
        // Update existing event
        ref
            .read(createEventNotifierProvider.notifier)
            .updateEvent(request, widget.event!.id!);
      } else {
        // Create new event
        ref.read(createEventNotifierProvider.notifier).createEvent(request);
      }
    }
  }

  Future<void> _moveCameraToLocation(LatLng position) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(position, 15),
      );
    }
  }

  void _updateMapMarker(LatLng position) {
    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('selected_location'),
          position: position,
        ),
      };
    });
  }

  void _updateLocationData(Map<String, String> result) {
    final newLat = double.tryParse(result['latitude'] ?? '');
    final newLng = double.tryParse(result['longitude'] ?? '');
    if (newLat != null && newLng != null) {
      final newPosition = LatLng(newLat, newLng);
      setState(() {
        _currentLocation = newPosition;
        _latitude = result['latitude'] ?? '';
        _longitude = result['longitude'] ?? '';
        // _locationController.text = result['address'] ?? '';
      });
      _updateMapMarker(newPosition);
      _moveCameraToLocation(newPosition);
    }
  }

  Future<void> _handleMapTap(LatLng position) async {
    final result = await context.push<Map<String, String>>(
      RouterPaths.selectLocation,
      extra: position,
    );
    if (result != null) {
      _updateLocationData(result);
    }
  }

  void _handleMapCreated(GoogleMapController controller) {
    _mapController = controller;
    if (_currentLocation != null) {
      _updateMapMarker(_currentLocation!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createEventNotifierProvider);

    ref.listen(createEventNotifierProvider, (previous, next) {
      if (next.isSuccess) {
        context.showSuccessSnackBar("イベントの作成に成功しました");
        context.pop(true);
      }
      if (next.isUpdateSuccess) {
        context.showSuccessSnackBar("イベントを更新しました。");
        context.pop(true);
      }
      if (next.errorMessage != null) {
        context.showErrorSnackBar(next.errorMessage!);
      }
      if (next.showImageSelectionDialog &&
          (previous?.showImageSelectionDialog ?? false) == false) {
        context.showErrorSnackBar("画像を選択してください");
      }
    });

    return BaseScaffold(
      resizeToAvoidBottomInset: true,
      appBar: CustomAppbar.basic(
        title: widget.event != null ? "イベント編集" : "イベント作成",
        onTap: () {
          context.pop();
        },
      ),
      body: SafeArea(
        top: false,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  InputTextField(
                    textController: _titleController,
                    label: "イベント名",
                    hintText: "イベント名",
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'イベント名を入力してください。';
                      }
                      if (value.length > 20) {
                        return '20文字以内で入力してください。';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16.h),
                  Row(
                    children: [
                      Expanded(
                        child: InputTextField(
                          ontap: () {
                            _selectDate(context, _startDateController);
                          },
                          textController: _startDateController,
                          label: "開始日",
                          hintText: "開始日",
                          enabled: true,
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.calendar_today),
                            onPressed: () =>
                                _selectDate(context, _startDateController),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return '開始日を入力してください。';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: InputTextField(
                          ontap: () {
                            _selectDate(context, _endDateController);
                          },
                          textController: _endDateController,
                          label: "完了日",
                          hintText: "完了日",
                          enabled: true,
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.calendar_today),
                            onPressed: () =>
                                _selectDate(context, _endDateController),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return '完了日を入力してください。';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),
                  InputTextField(
                    textController: _locationController,
                    label: "お場所",
                    hintText: "お場所",
                    suffixIcon: const Icon(Icons.map),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '場所を入力してください。';
                      }
                      if (value.length > 255) {
                        return '255文字以内で入力してください。';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16.h),
                  Container(
                    height: 200.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.mono40.withOpacity(0.5),
                        width: 1,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: _currentLocation == null
                          ? const Center(child: CircularProgressIndicator())
                          : GoogleMap(
                              onMapCreated: _handleMapCreated,
                              onTap: _handleMapTap,
                              markers: _markers,
                              initialCameraPosition: CameraPosition(
                                target: _currentLocation!,
                                zoom: 15,
                              ),
                              myLocationEnabled: true,
                              myLocationButtonEnabled: true,
                              zoomControlsEnabled: false,
                            ),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  InputTextField(
                    textController: _descriptionController,
                    label: "説明",
                    hintText: "説明",
                    maxLine: 4,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '説明を入力してください';
                      }
                      if (value.length > 1000) {
                        return '1000文字以内で入力してください。';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16.h),
                  StringTags(
                    label: "タグ",
                    hintText: "タグ",
                    initialTags: widget.event?.tags
                            ?.map((tag) => tag.toString())
                            .toList() ??
                        [],
                    onTagsChanged: (tags) {
                      _tagsController.text = tags.join(',');
                    },
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'タグを入力してください。';
                      }
                      if (value.length > 8) {
                        return '8文字以内で入力してください。';
                      }
                      return null;
                    },
                    tagColor: const Color(0xFFF5F6F9),
                    tagTextColor: AppColors.textPrimary,
                  ),
                  SizedBox(height: 16.h),
                  AddImageEvent(
                    urlImage: widget.event?.img,
                    onImageSelected: (file) {
                      setState(() {
                        _imageFile = file;
                      });
                    },
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    "画像のアップロード",
                    style:
                        AppTextStyles.bold(14.sp, color: AppColors.textPrimary),
                  ),
                  Text(
                    "最大50MBで、600x400px",
                    style: AppTextStyles.medium(12.sp,
                        color: AppColors.textLightSecondary),
                  ),
                  Text(
                    "*.jpeg, *.jpg, *.png.",
                    style: AppTextStyles.medium(12.sp,
                        color: AppColors.textLightSecondary),
                  ),
                  SizedBox(height: 16.h),
                  (state.isLoading)
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : CommonButton(
                          onPressed: _handleCreateEvent,
                          text: widget.event != null ? "更新" : "登録",
                        )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class AddImageEvent extends ConsumerStatefulWidget {
  const AddImageEvent({
    super.key,
    this.urlImage,
    required this.onImageSelected,
  });
  final String? urlImage;
  final Function(File) onImageSelected;

  @override
  _AddImageEventState createState() => _AddImageEventState();
}

class _AddImageEventState extends ConsumerState<AddImageEvent> {
  File? _selectedImage;
  final CommonImagePicker _imagePicker = CommonImagePicker(
    maxSizeInBytes: 50 * 1024 * 1024, // Giới hạn 10MB
    allowedFormats: ['jpg', 'jpeg', 'png'],
  );

  @override
  void initState() {
    super.initState();
    // Remove the provider modification from here
  }

  void _showImagePickerBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (_) => SafeArea(child: _buildImagePickerOptions(context)),
    );
  }

  Future<void> _pickImage(BuildContext context, ImageSource source) async {
    context.pop();
    final XFile? pickedFile =
        await _imagePicker.pickImage(context, source: source);
    if (pickedFile != null) {
      File imageFile = File(pickedFile.path);
      setState(() {
        _selectedImage = imageFile;
      });
      widget.onImageSelected(imageFile);
    }
    await _uploadImage(_selectedImage!);
  }

  Future<void> _uploadImage(File image) async {
    String base64Image = await _imagePicker.convertToBase64(image);

    await ref
        .read(createEventNotifierProvider.notifier)
        .upFileImage(base64Image);
  }

  Widget _buildImagePickerOptions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.camera_alt),
            title: Text(S.current.takePhoto),
            onTap: () => _pickImage(context, ImageSource.camera),
          ),
          ListTile(
            leading: const Icon(Icons.photo_library),
            title: Text(S.current.chooseFromLibrary),
            onTap: () => _pickImage(context, ImageSource.gallery),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createEventNotifierProvider);
    double height = 150;

    return GestureDetector(
      onTap: () => _showImagePickerBottomSheet(context),
      child: Stack(
        alignment: Alignment.center,
        children: [
          CustomPaint(
            size: Size(double.infinity, height),
            painter: DottedRoundedRectPainter(),
          ),
          Container(
            width: double.infinity,
            height: height,
            margin:
                const EdgeInsets.only(bottom: 16, left: 8, right: 8, top: 16),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: state.isUploadingImage
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : _selectedImage != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _selectedImage!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : (widget.urlImage != null &&
                            widget.urlImage!.isNotEmpty &&
                            state.urlImage != null)
                        ? BaseCachedNetworkImage(
                            imageUrl: state.urlImage ?? widget.urlImage!,
                            fit: BoxFit.cover,
                            borderRadius: BorderRadius.circular(8),
                          )
                        : null,
          ),
          if (!state.isUploadingImage &&
              (_selectedImage == null &&
                  (widget.urlImage == null ||
                      widget.urlImage!.isEmpty ||
                      state.urlImage == null)))
            Column(
              children: [
                Icon(
                  Icons.add_a_photo,
                  color: AppColors.textPrimary,
                  size: 24.sp,
                ),
                SizedBox(height: 8.h),
                Text(
                  "写真をアップロード",
                  style: AppTextStyles.regular(12.sp,
                      color: AppColors.textPrimary),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
