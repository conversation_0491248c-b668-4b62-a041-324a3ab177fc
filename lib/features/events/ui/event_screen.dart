import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/ui/base_search_widget.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/utils/date_time_formatter.dart';
import 'package:kitemite_app/features/events/provider/event_list/event_provider.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:skeletonizer/skeletonizer.dart';

class EventScreen extends ConsumerStatefulWidget {
  const EventScreen({super.key});

  @override
  ConsumerState<EventScreen> createState() => _EventScreenState();
}

class _EventScreenState extends ConsumerState<EventScreen> {
  final List<Color> categoryColors = [
    const Color(0xFFD6E4FF),
    const Color(0xFFD8FBDE),
    const Color(0xFFCAFDF5),
    const Color(0xFFFFF5CC),
    const Color(0xFFFFE9D5),
  ];

  final ScrollController _scrollController = ScrollController();
  final Map<String, Color> _tagColors = {};
  String? _selectedTag;

  @override
  void initState() {
    super.initState();
    Future.microtask(
        () => ref.read(eventNotifierProvider.notifier).loadEvents(false));

    // Add scroll listener for loadmore
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // Load more when user scrolls to the bottom
      ref.read(eventNotifierProvider.notifier).loadMore();
    }
  }

  Color _getRandomColor() {
    return categoryColors[
        DateTime.now().millisecondsSinceEpoch % categoryColors.length];
  }

  Color _getTagColor(String tag) {
    if (!_tagColors.containsKey(tag)) {
      _tagColors[tag] = _getRandomColor();
    }
    return _tagColors[tag]!;
  }

  @override
  Widget build(BuildContext context) {
    final eventState = ref.watch(eventNotifierProvider);

    return SafeArea(
      top: false,
      maintainBottomViewPadding: false,
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: BaseSearchWidget(
                  onSearch: (keyword) {
                    ref
                        .read(eventNotifierProvider.notifier)
                        .searchEvents(keyword);
                  },
                ),
              ),
              const SizedBox(height: 10),
              _listTag(),
              const SizedBox(height: 10),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await ref
                        .read(eventNotifierProvider.notifier)
                        .loadEvents(true);
                  },
                  child: eventState.isLoading
                      ? Skeletonizer(
                          enabled: true,
                          child: ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: 5,
                            itemBuilder: (context, index) {
                              return Padding(
                                padding:
                                    const EdgeInsets.only(bottom: 12, top: 12),
                                child: SizedBox(
                                  height: 90,
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 90,
                                        height: 90,
                                        decoration: BoxDecoration(
                                          color: Colors.grey[300],
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              height: 16,
                                              width: double.infinity,
                                              color: Colors.grey[300],
                                            ),
                                            const SizedBox(height: 8),
                                            Container(
                                              height: 16,
                                              width: double.infinity,
                                              color: Colors.grey[300],
                                            ),
                                            const Spacer(),
                                            Container(
                                              height: 12,
                                              width: 200,
                                              color: Colors.grey[300],
                                            ),
                                            const SizedBox(height: 4),
                                            Container(
                                              height: 12,
                                              width: 150,
                                              color: Colors.grey[300],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        )
                      : eventState.error != null
                          ? ErrorCommonWidget(
                              error: eventState.error!,
                              onPressed: () {
                                ref
                                    .read(eventNotifierProvider.notifier)
                                    .loadEvents(true);
                              },
                            )
                          : eventState.events.isEmpty
                              ? _buildEmptyState()
                              : ListView.builder(
                                  controller: _scrollController,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  itemCount: eventState.events.length +
                                      (eventState.isLoadingMore ? 1 : 0),
                                  itemBuilder: (context, index) {
                                    if (index == eventState.events.length) {
                                      return Skeletonizer(
                                        enabled: true,
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: _buildEventItem(
                                              eventState.events.last),
                                        ),
                                      );
                                    }

                                    final event = eventState.events[index];
                                    return _buildEventItem(event);
                                  },
                                ),
                ),
              ),
            ],
          ),
          Positioned(
            right: 16,
            bottom: 0,
            child: GestureDetector(
              onTap: () {
                var isGuest =
                    ref.read(modeNotifierProvider).mode == ModeAccount.guest;

                if (isGuest) {
                  showLoginRequiredDialog(context);
                } else {
                  context.push(RouterPaths.createEventScreen).then(
                    (value) {
                      if (value == true) {
                        ref
                            .read(eventNotifierProvider.notifier)
                            .loadEvents(true);
                      }
                    },
                  );
                }
              },
              child: SvgPicture.asset(
                Assets.floatingAdd,
                width: 80,
                height: 80,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventItem(dynamic event) {
    return InkWell(
      onTap: () {
          context.push(RouterPaths.detailEventScreen, extra: event.id);
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 12, top: 12),
        child: SizedBox(
          height: 90,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              BaseCachedNetworkImage(
                imageUrl: event.img ?? '',
                width: 90,
                height: 90,
                fit: BoxFit.cover,
                borderRadius: BorderRadius.circular(8),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      event.name ?? '',
                      style: AppTextStyles.bold(14.sp,
                          color: AppColors.textPrimary),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    Text(
                      event.address ?? '',
                      style: AppTextStyles.medium(12.sp,
                          color: AppColors.textLightSecondary),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      DateTimeFormatter.formatDateRange(
                        DateTime.parse(event.startDate ?? ''),
                        DateTime.parse(event.endDate ?? ''),
                      ),
                      style: AppTextStyles.medium(12.sp,
                          color: AppColors.textLightSecondary),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            Assets.shopGeneric,
            width: 120,
            height: 120,
          ),
          SizedBox(height: 16.h),
          Text(
            'イベントがありません',
            style: AppTextStyles.bold(16.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _listTag() {
    final eventState = ref.watch(eventNotifierProvider);
    final tags = eventState.tags;

    if (tags.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: List.generate(tags.length, (index) {
            final tag = tags[index];
            final isSelected = _selectedTag == tag;
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedTag = isSelected ? null : tag;
                  });
                  // Callback to filter events by tag
                  ref
                      .read(eventNotifierProvider.notifier)
                      .filterEventsByTag(_selectedTag);
                },
                child: Chip(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                  label: Text(
                    tag,
                    style: AppTextStyles.medium(
                      12.sp,
                      color: isSelected ? Colors.white : AppColors.textPrimary,
                    ),
                  ),
                  backgroundColor:
                      isSelected ? AppColors.primary : _getTagColor(tag),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                    side: BorderSide(
                      color:
                          isSelected ? _getTagColor(tag) : Colors.transparent,
                      width: 1,
                    ),
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }
}
