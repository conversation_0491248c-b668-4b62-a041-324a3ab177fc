import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';

part 'event_state.freezed.dart';

@freezed
class EventState with _$EventState {
  const factory EventState({
    @Default([]) List<EventDetailModel> events,
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingMore,
    @Default(1) int currentPage,
    @Default(1) int lastPage,
    @Default(10) int perPage,
    @Default([]) List<String> tags,
    String? error,
    String? searchKeyword,
    String? selectedTag,
  }) = _EventState;
}
