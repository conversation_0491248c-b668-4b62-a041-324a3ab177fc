import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/event/event_repository.dart';
import 'package:kitemite_app/features/events/provider/event_list/event_state.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'event_provider.g.dart';

@riverpod
class EventNotifier extends _$EventNotifier {
  late final EventRepository _repo;

  @override
  EventState build() {
    _repo = ref.read(getEventRepositoryProvider);
    return const EventState();
  }

  void _updateTopTags(List<EventDetailModel> events) {
    // Create a map to count tag occurrences
    final Map<String, int> tagCount = {};

    // Count occurrences of each tag
    for (var event in events) {
      if (event.tags != null && event.tags!.isNotEmpty) {
        // Use Set to remove duplicate tags within the same event
        final uniqueTags = event.tags!
            .where((tag) => tag != null && tag.toString().isNotEmpty)
            .map((tag) => tag.toString())
            .toSet();

        // Count each unique tag once per event
        for (var tag in uniqueTags) {
          tagCount[tag] = (tagCount[tag] ?? 0) + 1;
        }
      }
    }

    // Sort tags by count and get top 10
    final sortedTags = tagCount.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final topTags = sortedTags.take(10).map((e) => e.key).toList();

    // Get current tags from state
    final currentTags = state.tags ?? [];

    // Merge current tags with new top tags, removing duplicates
    final mergedTags = [...currentTags];

    for (var tag in topTags) {
      if (!mergedTags.contains(tag)) {
        mergedTags.add(tag);
      }
    }

    // Limit to maximum 10 tags
    final finalTags = mergedTags.take(10).toList();

    // Update state with merged tags
    state = state.copyWith(tags: finalTags);
  }

  Future<void> loadEvents(bool isRefresh) async {
    state = state.copyWith(isLoading: isRefresh ? false : true, error: null);
    try {
      final response = await _repo.getListEvents(
        1,
        10,
        state.searchKeyword,
        state.selectedTag,
      );
      final events = response.data ?? [];
      state = state.copyWith(
        events: events,
        currentPage: response.currentPage ?? 1,
        lastPage: response.lastPage ?? 1,
        perPage: response.perPage ?? 10,
        isLoading: false,
      );
      _updateTopTags(events);
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isLoading: false,
      );
    }
  }

  Future<void> loadMore() async {
    if (state.isLoadingMore || state.currentPage >= state.lastPage) return;

    state = state.copyWith(isLoadingMore: true, error: null);
    try {
      final response = await _repo.getListEvents(
        state.currentPage + 1,
        state.perPage,
        state.searchKeyword,
        state.selectedTag,
      );
      final newEvents = response.data ?? [];
      final allEvents = [...state.events, ...newEvents];
      state = state.copyWith(
        events: allEvents,
        currentPage: response.currentPage ?? state.currentPage + 1,
        lastPage: response.lastPage ?? state.lastPage,
        perPage: response.perPage ?? state.perPage,
        isLoadingMore: false,
      );
      _updateTopTags(allEvents);
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isLoadingMore: false,
      );
    }
  }

  Future<void> searchEvents(String keyword) async {
    state = state.copyWith(
      isLoading: true,
      searchKeyword: keyword.trim().isEmpty ? null : keyword.trim(),
    );
    try {
      final response = await _repo.getListEvents(
        1,
        10,
        state.searchKeyword,
        state.selectedTag,
      );
      final events = response.data ?? [];
      state = state.copyWith(
        events: events,
        currentPage: response.currentPage ?? 1,
        lastPage: response.lastPage ?? 1,
        perPage: response.perPage ?? 10,
        isLoading: false,
      );
      _updateTopTags(events);
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isLoading: false,
      );
    }
  }

  void filterEventsByTag(String? tag) {
    state = state.copyWith(selectedTag: tag);
    loadEvents(true);
  }
}
