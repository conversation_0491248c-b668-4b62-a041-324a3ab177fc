// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$eventNotifierHash() => r'ccea640ae6acbb083f08e2b68daa7d4782d19a98';

/// See also [EventNotifier].
@ProviderFor(EventNotifier)
final eventNotifierProvider =
    AutoDisposeNotifierProvider<EventNotifier, EventState>.internal(
  EventNotifier.new,
  name: r'eventNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$eventNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EventNotifier = AutoDisposeNotifier<EventState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
