// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_event_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateEventState _$CreateEventStateFromJson(Map<String, dynamic> json) {
  return _CreateEventState.fromJson(json);
}

/// @nodoc
mixin _$CreateEventState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isSuccess => throw _privateConstructorUsedError;
  CreateEventRequest? get request => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  String? get urlImage => throw _privateConstructorUsedError;
  bool get isUploadingImage => throw _privateConstructorUsedError;
  String? get upFileImageFailure => throw _privateConstructorUsedError;
  bool get showImageSelectionDialog => throw _privateConstructorUsedError;
  CreateEventRequest? get requestUpdate => throw _privateConstructorUsedError;
  bool get isUpdateSuccess => throw _privateConstructorUsedError;

  /// Serializes this CreateEventState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateEventState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateEventStateCopyWith<CreateEventState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateEventStateCopyWith<$Res> {
  factory $CreateEventStateCopyWith(
          CreateEventState value, $Res Function(CreateEventState) then) =
      _$CreateEventStateCopyWithImpl<$Res, CreateEventState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      CreateEventRequest? request,
      String? errorMessage,
      String? urlImage,
      bool isUploadingImage,
      String? upFileImageFailure,
      bool showImageSelectionDialog,
      CreateEventRequest? requestUpdate,
      bool isUpdateSuccess});

  $CreateEventRequestCopyWith<$Res>? get request;
  $CreateEventRequestCopyWith<$Res>? get requestUpdate;
}

/// @nodoc
class _$CreateEventStateCopyWithImpl<$Res, $Val extends CreateEventState>
    implements $CreateEventStateCopyWith<$Res> {
  _$CreateEventStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateEventState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? request = freezed,
    Object? errorMessage = freezed,
    Object? urlImage = freezed,
    Object? isUploadingImage = null,
    Object? upFileImageFailure = freezed,
    Object? showImageSelectionDialog = null,
    Object? requestUpdate = freezed,
    Object? isUpdateSuccess = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      request: freezed == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as CreateEventRequest?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      showImageSelectionDialog: null == showImageSelectionDialog
          ? _value.showImageSelectionDialog
          : showImageSelectionDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      requestUpdate: freezed == requestUpdate
          ? _value.requestUpdate
          : requestUpdate // ignore: cast_nullable_to_non_nullable
              as CreateEventRequest?,
      isUpdateSuccess: null == isUpdateSuccess
          ? _value.isUpdateSuccess
          : isUpdateSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of CreateEventState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CreateEventRequestCopyWith<$Res>? get request {
    if (_value.request == null) {
      return null;
    }

    return $CreateEventRequestCopyWith<$Res>(_value.request!, (value) {
      return _then(_value.copyWith(request: value) as $Val);
    });
  }

  /// Create a copy of CreateEventState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CreateEventRequestCopyWith<$Res>? get requestUpdate {
    if (_value.requestUpdate == null) {
      return null;
    }

    return $CreateEventRequestCopyWith<$Res>(_value.requestUpdate!, (value) {
      return _then(_value.copyWith(requestUpdate: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreateEventStateImplCopyWith<$Res>
    implements $CreateEventStateCopyWith<$Res> {
  factory _$$CreateEventStateImplCopyWith(_$CreateEventStateImpl value,
          $Res Function(_$CreateEventStateImpl) then) =
      __$$CreateEventStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      CreateEventRequest? request,
      String? errorMessage,
      String? urlImage,
      bool isUploadingImage,
      String? upFileImageFailure,
      bool showImageSelectionDialog,
      CreateEventRequest? requestUpdate,
      bool isUpdateSuccess});

  @override
  $CreateEventRequestCopyWith<$Res>? get request;
  @override
  $CreateEventRequestCopyWith<$Res>? get requestUpdate;
}

/// @nodoc
class __$$CreateEventStateImplCopyWithImpl<$Res>
    extends _$CreateEventStateCopyWithImpl<$Res, _$CreateEventStateImpl>
    implements _$$CreateEventStateImplCopyWith<$Res> {
  __$$CreateEventStateImplCopyWithImpl(_$CreateEventStateImpl _value,
      $Res Function(_$CreateEventStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateEventState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? request = freezed,
    Object? errorMessage = freezed,
    Object? urlImage = freezed,
    Object? isUploadingImage = null,
    Object? upFileImageFailure = freezed,
    Object? showImageSelectionDialog = null,
    Object? requestUpdate = freezed,
    Object? isUpdateSuccess = null,
  }) {
    return _then(_$CreateEventStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      request: freezed == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as CreateEventRequest?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      urlImage: freezed == urlImage
          ? _value.urlImage
          : urlImage // ignore: cast_nullable_to_non_nullable
              as String?,
      isUploadingImage: null == isUploadingImage
          ? _value.isUploadingImage
          : isUploadingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      upFileImageFailure: freezed == upFileImageFailure
          ? _value.upFileImageFailure
          : upFileImageFailure // ignore: cast_nullable_to_non_nullable
              as String?,
      showImageSelectionDialog: null == showImageSelectionDialog
          ? _value.showImageSelectionDialog
          : showImageSelectionDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      requestUpdate: freezed == requestUpdate
          ? _value.requestUpdate
          : requestUpdate // ignore: cast_nullable_to_non_nullable
              as CreateEventRequest?,
      isUpdateSuccess: null == isUpdateSuccess
          ? _value.isUpdateSuccess
          : isUpdateSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateEventStateImpl implements _CreateEventState {
  const _$CreateEventStateImpl(
      {this.isLoading = false,
      this.isSuccess = false,
      this.request,
      this.errorMessage,
      this.urlImage,
      this.isUploadingImage = false,
      this.upFileImageFailure,
      this.showImageSelectionDialog = false,
      this.requestUpdate,
      this.isUpdateSuccess = false});

  factory _$CreateEventStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateEventStateImplFromJson(json);

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSuccess;
  @override
  final CreateEventRequest? request;
  @override
  final String? errorMessage;
  @override
  final String? urlImage;
  @override
  @JsonKey()
  final bool isUploadingImage;
  @override
  final String? upFileImageFailure;
  @override
  @JsonKey()
  final bool showImageSelectionDialog;
  @override
  final CreateEventRequest? requestUpdate;
  @override
  @JsonKey()
  final bool isUpdateSuccess;

  @override
  String toString() {
    return 'CreateEventState(isLoading: $isLoading, isSuccess: $isSuccess, request: $request, errorMessage: $errorMessage, urlImage: $urlImage, isUploadingImage: $isUploadingImage, upFileImageFailure: $upFileImageFailure, showImageSelectionDialog: $showImageSelectionDialog, requestUpdate: $requestUpdate, isUpdateSuccess: $isUpdateSuccess)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateEventStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSuccess, isSuccess) ||
                other.isSuccess == isSuccess) &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.urlImage, urlImage) ||
                other.urlImage == urlImage) &&
            (identical(other.isUploadingImage, isUploadingImage) ||
                other.isUploadingImage == isUploadingImage) &&
            (identical(other.upFileImageFailure, upFileImageFailure) ||
                other.upFileImageFailure == upFileImageFailure) &&
            (identical(
                    other.showImageSelectionDialog, showImageSelectionDialog) ||
                other.showImageSelectionDialog == showImageSelectionDialog) &&
            (identical(other.requestUpdate, requestUpdate) ||
                other.requestUpdate == requestUpdate) &&
            (identical(other.isUpdateSuccess, isUpdateSuccess) ||
                other.isUpdateSuccess == isUpdateSuccess));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isSuccess,
      request,
      errorMessage,
      urlImage,
      isUploadingImage,
      upFileImageFailure,
      showImageSelectionDialog,
      requestUpdate,
      isUpdateSuccess);

  /// Create a copy of CreateEventState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateEventStateImplCopyWith<_$CreateEventStateImpl> get copyWith =>
      __$$CreateEventStateImplCopyWithImpl<_$CreateEventStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateEventStateImplToJson(
      this,
    );
  }
}

abstract class _CreateEventState implements CreateEventState {
  const factory _CreateEventState(
      {final bool isLoading,
      final bool isSuccess,
      final CreateEventRequest? request,
      final String? errorMessage,
      final String? urlImage,
      final bool isUploadingImage,
      final String? upFileImageFailure,
      final bool showImageSelectionDialog,
      final CreateEventRequest? requestUpdate,
      final bool isUpdateSuccess}) = _$CreateEventStateImpl;

  factory _CreateEventState.fromJson(Map<String, dynamic> json) =
      _$CreateEventStateImpl.fromJson;

  @override
  bool get isLoading;
  @override
  bool get isSuccess;
  @override
  CreateEventRequest? get request;
  @override
  String? get errorMessage;
  @override
  String? get urlImage;
  @override
  bool get isUploadingImage;
  @override
  String? get upFileImageFailure;
  @override
  bool get showImageSelectionDialog;
  @override
  CreateEventRequest? get requestUpdate;
  @override
  bool get isUpdateSuccess;

  /// Create a copy of CreateEventState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateEventStateImplCopyWith<_$CreateEventStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
