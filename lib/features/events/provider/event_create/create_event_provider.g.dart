// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_event_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$createEventNotifierHash() =>
    r'f9de7eae5d9ffa71146111623abe7c8464164f59';

/// See also [CreateEventNotifier].
@ProviderFor(CreateEventNotifier)
final createEventNotifierProvider =
    AutoDisposeNotifierProvider<CreateEventNotifier, CreateEventState>.internal(
  CreateEventNotifier.new,
  name: r'createEventNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$createEventNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CreateEventNotifier = AutoDisposeNotifier<CreateEventState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
