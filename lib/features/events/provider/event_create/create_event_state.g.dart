// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_event_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreateEventStateImpl _$$CreateEventStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateEventStateImpl(
      isLoading: json['isLoading'] as bool? ?? false,
      isSuccess: json['isSuccess'] as bool? ?? false,
      request: json['request'] == null
          ? null
          : CreateEventRequest.fromJson(
              json['request'] as Map<String, dynamic>),
      errorMessage: json['errorMessage'] as String?,
      urlImage: json['urlImage'] as String?,
      isUploadingImage: json['isUploadingImage'] as bool? ?? false,
      upFileImageFailure: json['upFileImageFailure'] as String?,
      showImageSelectionDialog:
          json['showImageSelectionDialog'] as bool? ?? false,
      requestUpdate: json['requestUpdate'] == null
          ? null
          : CreateEventRequest.fromJson(
              json['requestUpdate'] as Map<String, dynamic>),
      isUpdateSuccess: json['isUpdateSuccess'] as bool? ?? false,
    );

Map<String, dynamic> _$$CreateEventStateImplToJson(
        _$CreateEventStateImpl instance) =>
    <String, dynamic>{
      'isLoading': instance.isLoading,
      'isSuccess': instance.isSuccess,
      'request': instance.request,
      'errorMessage': instance.errorMessage,
      'urlImage': instance.urlImage,
      'isUploadingImage': instance.isUploadingImage,
      'upFileImageFailure': instance.upFileImageFailure,
      'showImageSelectionDialog': instance.showImageSelectionDialog,
      'requestUpdate': instance.requestUpdate,
      'isUpdateSuccess': instance.isUpdateSuccess,
    };
