import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/auth/auth_repository.dart';
import 'package:kitemite_app/core/repository/event/event_repository.dart';
import 'package:kitemite_app/features/events/provider/event_create/create_event_state.dart';
import 'package:kitemite_app/model/request/event/create_event_request.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'create_event_provider.g.dart';

@riverpod
class CreateEventNotifier extends _$CreateEventNotifier {
  @override
  CreateEventState build() => const CreateEventState();

  Future<void> createEvent(CreateEventRequest request) async {
    // Check if image is required but not provided
    if (state.urlImage == null) {
      state = state.copyWith(showImageSelectionDialog: true);
      return;
    }

    var request0 = request.copyWith(
      imageUrl: state.urlImage,
    );
    state = state.copyWith(request: request0);
    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
      showImageSelectionDialog: false,
    );

    try {
      await ref
          .read(getEventRepositoryProvider)
          .createEvent(request: state.request!);
      state = state.copyWith(isSuccess: true, isLoading: false);
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.message,
        showImageSelectionDialog: false,
      );
    }
  }

  void resetState() {
    state = const CreateEventState();
  }

  Future<void> upFileImage(String imageBase64) async {
    state = state.copyWith(
      upFileImageFailure: null,
      isUploadingImage: true,
    );
    try {
      final authRepo = ref.read(getAuthRepositoryProvider);

      final result = await authRepo.upLoadFile(base64: imageBase64);
      state = state.copyWith(
        urlImage: result.links?.first ?? "",
        isLoading: false,
        isUploadingImage: false,
        showImageSelectionDialog: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        isUploadingImage: false,
        upFileImageFailure: e.message,
      );
    }
  }

  void resetImageSelectionDialog() {
    state = state.copyWith(showImageSelectionDialog: false);
  }

  Future<void> updateEvent(CreateEventRequest request, int id) async {
    var request1 = request;
    if (state.urlImage != null) {
      request1 = request.copyWith(
        imageUrl: state.urlImage,
      );
    }
    state = state.copyWith(
      requestUpdate: request1,
      isUpdateSuccess: false,
      isLoading: true,
      errorMessage: null,
      showImageSelectionDialog: false,
    );
    try {
      await ref
          .read(getEventRepositoryProvider)
          .updateEvent(id: id, request: state.requestUpdate!);
      state = state.copyWith(isUpdateSuccess: true, isLoading: false);
    } on AppFailure catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.message);
    }
  }

  void setEventImage(String imageUrl) {
    state = state.copyWith(urlImage: imageUrl);
  }
}
