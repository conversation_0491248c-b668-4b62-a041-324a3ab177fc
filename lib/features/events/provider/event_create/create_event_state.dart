import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../model/request/event/create_event_request.dart';

part 'create_event_state.freezed.dart';
part 'create_event_state.g.dart';

@freezed
class CreateEventState with _$CreateEventState {
  const factory CreateEventState({
    @Default(false) bool isLoading,
    @Default(false) bool isSuccess,
    CreateEventRequest? request,
    String? errorMessage,
    String? urlImage,
    @Default(false) bool isUploadingImage,
    String? upFileImageFailure,
    @Default(false) bool showImageSelectionDialog,
    CreateEventRequest? requestUpdate,
    @Default(false) bool isUpdateSuccess,
  }) = _CreateEventState;

  factory CreateEventState.fromJson(Map<String, dynamic> json) =>
      _$CreateEventStateFromJson(json);
}
