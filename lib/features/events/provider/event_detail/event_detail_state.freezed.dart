// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'event_detail_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EventDetailState {
  EventDetailModel? get event => throw _privateConstructorUsedError;
  List<EventCommentModel>? get comments => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isCommentLoading => throw _privateConstructorUsedError;
  bool get isLoadingMore => throw _privateConstructorUsedError;
  int get totalComments => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  int get lastPage => throw _privateConstructorUsedError;
  int get perPage => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of EventDetailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EventDetailStateCopyWith<EventDetailState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EventDetailStateCopyWith<$Res> {
  factory $EventDetailStateCopyWith(
          EventDetailState value, $Res Function(EventDetailState) then) =
      _$EventDetailStateCopyWithImpl<$Res, EventDetailState>;
  @useResult
  $Res call(
      {EventDetailModel? event,
      List<EventCommentModel>? comments,
      bool isLoading,
      bool isCommentLoading,
      bool isLoadingMore,
      int totalComments,
      int currentPage,
      int lastPage,
      int perPage,
      String? error});

  $EventDetailModelCopyWith<$Res>? get event;
}

/// @nodoc
class _$EventDetailStateCopyWithImpl<$Res, $Val extends EventDetailState>
    implements $EventDetailStateCopyWith<$Res> {
  _$EventDetailStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EventDetailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? event = freezed,
    Object? comments = freezed,
    Object? isLoading = null,
    Object? isCommentLoading = null,
    Object? isLoadingMore = null,
    Object? totalComments = null,
    Object? currentPage = null,
    Object? lastPage = null,
    Object? perPage = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      event: freezed == event
          ? _value.event
          : event // ignore: cast_nullable_to_non_nullable
              as EventDetailModel?,
      comments: freezed == comments
          ? _value.comments
          : comments // ignore: cast_nullable_to_non_nullable
              as List<EventCommentModel>?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCommentLoading: null == isCommentLoading
          ? _value.isCommentLoading
          : isCommentLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      totalComments: null == totalComments
          ? _value.totalComments
          : totalComments // ignore: cast_nullable_to_non_nullable
              as int,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      lastPage: null == lastPage
          ? _value.lastPage
          : lastPage // ignore: cast_nullable_to_non_nullable
              as int,
      perPage: null == perPage
          ? _value.perPage
          : perPage // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of EventDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EventDetailModelCopyWith<$Res>? get event {
    if (_value.event == null) {
      return null;
    }

    return $EventDetailModelCopyWith<$Res>(_value.event!, (value) {
      return _then(_value.copyWith(event: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$EventDetailStateImplCopyWith<$Res>
    implements $EventDetailStateCopyWith<$Res> {
  factory _$$EventDetailStateImplCopyWith(_$EventDetailStateImpl value,
          $Res Function(_$EventDetailStateImpl) then) =
      __$$EventDetailStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {EventDetailModel? event,
      List<EventCommentModel>? comments,
      bool isLoading,
      bool isCommentLoading,
      bool isLoadingMore,
      int totalComments,
      int currentPage,
      int lastPage,
      int perPage,
      String? error});

  @override
  $EventDetailModelCopyWith<$Res>? get event;
}

/// @nodoc
class __$$EventDetailStateImplCopyWithImpl<$Res>
    extends _$EventDetailStateCopyWithImpl<$Res, _$EventDetailStateImpl>
    implements _$$EventDetailStateImplCopyWith<$Res> {
  __$$EventDetailStateImplCopyWithImpl(_$EventDetailStateImpl _value,
      $Res Function(_$EventDetailStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of EventDetailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? event = freezed,
    Object? comments = freezed,
    Object? isLoading = null,
    Object? isCommentLoading = null,
    Object? isLoadingMore = null,
    Object? totalComments = null,
    Object? currentPage = null,
    Object? lastPage = null,
    Object? perPage = null,
    Object? error = freezed,
  }) {
    return _then(_$EventDetailStateImpl(
      event: freezed == event
          ? _value.event
          : event // ignore: cast_nullable_to_non_nullable
              as EventDetailModel?,
      comments: freezed == comments
          ? _value._comments
          : comments // ignore: cast_nullable_to_non_nullable
              as List<EventCommentModel>?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCommentLoading: null == isCommentLoading
          ? _value.isCommentLoading
          : isCommentLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      totalComments: null == totalComments
          ? _value.totalComments
          : totalComments // ignore: cast_nullable_to_non_nullable
              as int,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      lastPage: null == lastPage
          ? _value.lastPage
          : lastPage // ignore: cast_nullable_to_non_nullable
              as int,
      perPage: null == perPage
          ? _value.perPage
          : perPage // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$EventDetailStateImpl implements _EventDetailState {
  const _$EventDetailStateImpl(
      {this.event,
      final List<EventCommentModel>? comments,
      this.isLoading = false,
      this.isCommentLoading = false,
      this.isLoadingMore = false,
      this.totalComments = 0,
      this.currentPage = 1,
      this.lastPage = 1,
      this.perPage = 10,
      this.error})
      : _comments = comments;

  @override
  final EventDetailModel? event;
  final List<EventCommentModel>? _comments;
  @override
  List<EventCommentModel>? get comments {
    final value = _comments;
    if (value == null) return null;
    if (_comments is EqualUnmodifiableListView) return _comments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isCommentLoading;
  @override
  @JsonKey()
  final bool isLoadingMore;
  @override
  @JsonKey()
  final int totalComments;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int lastPage;
  @override
  @JsonKey()
  final int perPage;
  @override
  final String? error;

  @override
  String toString() {
    return 'EventDetailState(event: $event, comments: $comments, isLoading: $isLoading, isCommentLoading: $isCommentLoading, isLoadingMore: $isLoadingMore, totalComments: $totalComments, currentPage: $currentPage, lastPage: $lastPage, perPage: $perPage, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EventDetailStateImpl &&
            (identical(other.event, event) || other.event == event) &&
            const DeepCollectionEquality().equals(other._comments, _comments) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isCommentLoading, isCommentLoading) ||
                other.isCommentLoading == isCommentLoading) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.totalComments, totalComments) ||
                other.totalComments == totalComments) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.lastPage, lastPage) ||
                other.lastPage == lastPage) &&
            (identical(other.perPage, perPage) || other.perPage == perPage) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      event,
      const DeepCollectionEquality().hash(_comments),
      isLoading,
      isCommentLoading,
      isLoadingMore,
      totalComments,
      currentPage,
      lastPage,
      perPage,
      error);

  /// Create a copy of EventDetailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EventDetailStateImplCopyWith<_$EventDetailStateImpl> get copyWith =>
      __$$EventDetailStateImplCopyWithImpl<_$EventDetailStateImpl>(
          this, _$identity);
}

abstract class _EventDetailState implements EventDetailState {
  const factory _EventDetailState(
      {final EventDetailModel? event,
      final List<EventCommentModel>? comments,
      final bool isLoading,
      final bool isCommentLoading,
      final bool isLoadingMore,
      final int totalComments,
      final int currentPage,
      final int lastPage,
      final int perPage,
      final String? error}) = _$EventDetailStateImpl;

  @override
  EventDetailModel? get event;
  @override
  List<EventCommentModel>? get comments;
  @override
  bool get isLoading;
  @override
  bool get isCommentLoading;
  @override
  bool get isLoadingMore;
  @override
  int get totalComments;
  @override
  int get currentPage;
  @override
  int get lastPage;
  @override
  int get perPage;
  @override
  String? get error;

  /// Create a copy of EventDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EventDetailStateImplCopyWith<_$EventDetailStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
