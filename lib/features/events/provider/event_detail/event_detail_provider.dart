import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/event/event_repository.dart';
import 'package:kitemite_app/features/events/provider/event_detail/event_detail_state.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/model/base/base_reponse_model.dart';
import 'package:kitemite_app/model/request/event/create_comment_request.dart';
import 'package:kitemite_app/model/response/event/event_comment_model.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'event_detail_provider.g.dart';

@riverpod
class EventDetailNotifier extends _$EventDetailNotifier {
  @override
  EventDetailState build() {
    return const EventDetailState();
  }

  Future<void> loadEventDetail(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Call APIs in parallel
      var isGuest = ref.watch(modeNotifierProvider).mode == ModeAccount.guest;
      final results = isGuest
          ? await Future.wait<dynamic>([
              ref.read(getEventRepositoryProvider).getEventDetail(id: id),
            ])
          : await Future.wait<dynamic>([
              ref.read(getEventRepositoryProvider).getEventDetail(id: id),
              ref.read(getEventRepositoryProvider).getEventComments(
                    eventId: id,
                    page: 1,
                    perPage: state.perPage,
                  ),
            ]);

      final event = (results[0] as BaseResponse<EventDetailModel>).data;

      final comments = isGuest
          ? []
          : (results[1] as BaseListResponse<EventCommentModel>).data;
      final totalComments = isGuest
          ? 0
          : (results[1] as BaseListResponse<EventCommentModel>).total;
      final lastPage = isGuest
          ? 1
          : (results[1] as BaseListResponse<EventCommentModel>).lastPage;

      state = state.copyWith(
        event: event,
        comments: isGuest ? [] : comments as List<EventCommentModel>,
        totalComments: totalComments ?? 0,
        currentPage: 1,
        lastPage: lastPage ?? 1,
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }

  Future<void> loadMoreComments() async {
    if (state.isLoadingMore || state.currentPage >= state.lastPage) return;

    state = state.copyWith(isLoadingMore: true, error: null);
    try {
      final eventId = state.event?.id;
      if (eventId == null) {
        throw const AppFailure(
          name: 'InvalidEventId',
          message: 'Event ID is null',
        );
      }

      final response =
          await ref.read(getEventRepositoryProvider).getEventComments(
                eventId: eventId,
                page: state.currentPage + 1,
                perPage: state.perPage,
              );

      final newComments = response.data ?? [];
      final allComments = [...?state.comments, ...newComments];

      state = state.copyWith(
        comments: allComments,
        currentPage: response.currentPage ?? state.currentPage + 1,
        lastPage: response.lastPage ?? state.lastPage,
        isLoadingMore: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isLoadingMore: false,
      );
    }
  }

  Future<void> addComment(String content) async {
    state = state.copyWith(isCommentLoading: true, error: null);
    try {
      final eventId = state.event?.id;
      if (eventId == null) {
        throw const AppFailure(
          name: 'InvalidEventId',
          message: 'Event ID is null',
        );
      }

      // Create comment request
      final request = CreateCommentRequest(
        eventId: eventId,
        content: content,
      );

      // Call API to create comment
      await ref
          .read(getEventRepositoryProvider)
          .createComment(request: request);

      // Reload comments
      final commentsResponse =
          await ref.read(getEventRepositoryProvider).getEventComments(
                eventId: eventId,
                page: 1,
                perPage: state.perPage,
              );
      final comments = commentsResponse.data;
      final totalComments = commentsResponse.total;
      final lastPage = commentsResponse.lastPage;

      // Update state with new comments
      state = state.copyWith(
        comments: comments,
        totalComments: totalComments ?? 0,
        currentPage: 1,
        lastPage: lastPage ?? 1,
        isCommentLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isCommentLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'An unexpected error occurred',
        isCommentLoading: false,
      );
    }
  }
}
