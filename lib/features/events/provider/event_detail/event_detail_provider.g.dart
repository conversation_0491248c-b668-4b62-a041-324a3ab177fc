// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_detail_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$eventDetailNotifierHash() =>
    r'd40bcdfaec0aab58240c446256158d2077a12abe';

/// See also [EventDetailNotifier].
@ProviderFor(EventDetailNotifier)
final eventDetailNotifierProvider =
    AutoDisposeNotifierProvider<EventDetailNotifier, EventDetailState>.internal(
  EventDetailNotifier.new,
  name: r'eventDetailNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$eventDetailNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EventDetailNotifier = AutoDisposeNotifier<EventDetailState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
