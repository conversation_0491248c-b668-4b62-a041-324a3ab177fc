import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/event/event_comment_model.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';

part 'event_detail_state.freezed.dart';

@freezed
class EventDetailState with _$EventDetailState {
  const factory EventDetailState({
    EventDetailModel? event,
    List<EventCommentModel>? comments,
    @Default(false) bool isLoading,
    @Default(false) bool isCommentLoading,
    @Default(false) bool isLoadingMore,
    @Default(0) int totalComments,
    @Default(1) int currentPage,
    @Default(1) int lastPage,
    @Default(10) int perPage,
    String? error,
  }) = _EventDetailState;
}
