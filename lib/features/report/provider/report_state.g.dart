// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReportStateImpl _$$ReportStateImplFromJson(Map<String, dynamic> json) =>
    _$ReportStateImpl(
      isLoading: json['isLoading'] as bool? ?? false,
      isSuccess: json['isSuccess'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$$ReportStateImplToJson(_$ReportStateImpl instance) =>
    <String, dynamic>{
      'isLoading': instance.isLoading,
      'isSuccess': instance.isSuccess,
      'errorMessage': instance.errorMessage,
    };
