import 'package:freezed_annotation/freezed_annotation.dart';

part 'report_state.freezed.dart';
part 'report_state.g.dart';

@freezed
class ReportState with _$ReportState {
  const factory ReportState({
    @Default(false) bool isLoading,
    @Default(false) bool isSuccess,
    String? errorMessage,
  }) = _ReportState;

  factory ReportState.fromJson(Map<String, dynamic> json) =>
      _$ReportStateFromJson(json);
}
