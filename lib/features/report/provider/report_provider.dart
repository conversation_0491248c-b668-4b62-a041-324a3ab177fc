import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/failure/app_failure.dart';
import '../../../core/repository/product/product_repository.dart';
import 'report_state.dart';

part 'report_provider.g.dart';

@riverpod
class ReportNotifier extends _$ReportNotifier {
  late final ProductRepository _repository;

  @override
  ReportState build() {
    _repository = ref.read(getProductRepositoryProvider);
    return const ReportState();
  }

  Future<void> reportProduct(int productId, String reason) async {
    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
      isSuccess: false,
    );

    try {
      await _repository.reportProduct(productId, reason);
      state = state.copyWith(
        isSuccess: true,
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.message,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: '予期せぬエラーが発生しました。もう一度お試しください。',
      );
    }
  }

  void resetState() {
    state = const ReportState();
  }
}
