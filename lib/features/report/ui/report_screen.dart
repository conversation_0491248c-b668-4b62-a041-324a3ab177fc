import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/common/widgets/text_input/input_text_field.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/report/provider/report_provider.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';

class ReportScreen extends ConsumerStatefulWidget {
  const ReportScreen({super.key, required this.product});
  final ProductModel product;

  @override
  ConsumerState<ReportScreen> createState() => _ReportScreenState();
}

class _ReportScreenState extends ConsumerState<ReportScreen> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  void _submitReport() {
    if (_formKey.currentState!.validate()) {
      final productId = widget.product.id;
      if (productId != null) {
        final trimmedReason = _reasonController.text.trim();
        if (trimmedReason.isEmpty) {
          context.showErrorSnackBar("通報理由を入力してください。");
          return;
        }
        ref.read(reportNotifierProvider.notifier).reportProduct(
              productId,
              trimmedReason,
            );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final reportState = ref.watch(reportNotifierProvider);

    ref.listen(reportNotifierProvider, (previous, next) {
      if (previous?.isLoading == true && !next.isLoading) {
        if (next.isSuccess) {
          context.showSuccessSnackBar("通報が完了しました。");
          // Navigate back after successful submission
          Future.delayed(const Duration(seconds: 1), () {
            if (mounted) {
              context.pop();
            }
          });
        } else if (next.errorMessage != null) {
          context.showErrorSnackBar(next.errorMessage!);
        }
      }
    });

    return BaseScaffold(
        appBar: CustomAppbar.basic(
          title: '違反商品を通報する',
          onTap: () => context.pop(),
        ),
        body: SafeArea(
            top: false,
            child: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16.0, 0, 16.0, 16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              Row(children: [
                                ClipRRect(
                                    borderRadius: BorderRadius.circular(16),
                                    child: BaseCachedNetworkImage(
                                      imageUrl: widget.product.image ?? '',
                                      width: 80.w,
                                      height: 80.h,
                                      fit: BoxFit.cover,
                                      borderRadius: BorderRadius.circular(16),
                                    )),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        widget.product.name ?? '_',
                                        style: AppTextStyles.bold(14.sp,
                                            color: AppColors.textPrimary),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        widget.product.place?.name ?? "_",
                                        style: AppTextStyles.medium(12.sp,
                                            color:
                                                AppColors.textLightSecondary),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        "${widget.product.place?.province ?? ""}, ${widget.product.place?.city ?? ""}, ${widget.product.place?.street ?? ""}",
                                        style: AppTextStyles.medium(12.sp,
                                            color:
                                                AppColors.textLightSecondary),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '冷凍庫-${widget.product.cabinet?.cabinetCode ?? "_"}: ${widget.product.shelf?.shelfCode ?? "_"}',
                                        style: AppTextStyles.medium(12.sp,
                                            color:
                                                AppColors.textLightSecondary),
                                      ),
                                    ],
                                  ),
                                )
                              ]),
                              const SizedBox(height: 16),
                              InputTextField(
                                textController: _reasonController,
                                hintText: '理由を入力してください。',
                                label: '通報の理由',
                                labelStyle: AppTextStyles.regular(14.sp,
                                    color: AppColors.textPrimary),
                                maxLine: 6,
                                maxLength: 200,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return '通報理由を入力してください。';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (reportState.isLoading)
                        const Center(child: CircularProgressIndicator())
                      else
                        CommonButton(
                          text: '送信',
                          onPressed: _submitReport,
                        ),
                    ],
                  ),
                ),
              ),
            )));
  }
}
