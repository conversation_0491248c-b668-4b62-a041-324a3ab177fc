import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/service/push_notification_service.dart';
import 'package:kitemite_app/features/splash/provider/splash_provider.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    PushNotificationService.requestNotificationPermissions();
    FlutterNativeSplash.remove();
    super.initState();
  }

  Future<dynamic> _showUpdateDialog(BuildContext context) async {
    return showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: AppColors.background.withOpacity(0.3),
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Center(
          child: CupertinoAlertDialog(
            title: Text(S.current.notificationTitle,
                style: AppTextStyles.bold(
                  17.sp,
                )),
            content: Text(S.current.updateMessage,
                style: AppTextStyles.medium(
                  13.sp,
                )),
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: CupertinoDialogAction(
                      isDefaultAction: true,
                      child: Text(S.current.updateLater,
                          style: AppTextStyles.regular(17.sp,
                              color: AppColors.blueberry100)),
                      onPressed: () {
                        context.go(RouterPaths.login);
                      },
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 48,
                    color: CupertinoColors.separator,
                  ),
                  Expanded(
                    child: CupertinoDialogAction(
                      isDestructiveAction: true,
                      child: Text(S.current.updateStore,
                          style: AppTextStyles.bold(17.sp,
                              color: AppColors.blueberry100)),
                      onPressed: () {
                        context.go(RouterPaths.login);
                      },
                    ),
                  ),
                ],
              )
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AsyncValue<SplashState>>(splashProvider, (prev, next) {
      next.when(
        data: (state) {
          switch (state) {
            case SplashState.updateRequired:
              _showUpdateDialog(context);
              break;
            case SplashState.navigateToHome:
              context.go(RouterPaths.home, extra: true);
              break;
            case SplashState.navigateToLogin:
              context.go(RouterPaths.login);
              break;
            default:
              break;
          }
        },
        error: (err, stack) => debugPrint('Error: $err'),
        loading: () {},
      );
    });
    return Scaffold(
      body: Container(
        color: AppColors.primary,
        child: Center(
            child: Assets.appIcon2.image(
          height: 320.h,
          fit: BoxFit.contain,
        )),
      ),
    );
  }
}
