import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/repository/auth/auth_repository.dart';
import 'package:kitemite_app/core/utils/app_preferences.dart';
import 'package:kitemite_app/core/utils/app_version_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'splash_provider.g.dart';

enum SplashState {
  checkingVersion,
  updateRequired,
  navigateToHome,
  navigateToLogin
}

@riverpod
class Splash extends _$Splash {
  @override
  Future<SplashState> build() async {
    try {
      // final isLatestVersion = await _checkVersion();
      // if (!isLatestVersion) return SplashState.updateRequired;

      final isLoggedIn = await _checkToken();
      return isLoggedIn
          ? SplashState.navigateToHome
          : SplashState.navigateToLogin;
    } catch (e) {
      throw Exception('error: $e');
    }
  }

  Future<bool> _checkVersion() async {
    var repo = ref.read(getAuthRepositoryProvider);
    final version = await repo.checkVersionApp();
    String currentVersion = AppVersionService().currentVersion;
    return version.version == currentVersion;
  }

  Future<bool> _checkToken() async {
    String? token = AppPreferences.getString(AppConstants.tokenKey);

    // Check if token exists and is not empty
    if (token == null || token.isEmpty) {
      return false;
    }
    return true;
  }
}
