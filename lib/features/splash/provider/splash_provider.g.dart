// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'splash_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$splashHash() => r'8e2712b884842617f89f2da284a56eb41b9867b0';

/// See also [Splash].
@ProviderFor(Splash)
final splashProvider =
    AutoDisposeAsyncNotifierProvider<Splash, SplashState>.internal(
  Splash.new,
  name: r'splashProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$splashHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Splash = AutoDisposeAsyncNotifier<SplashState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
