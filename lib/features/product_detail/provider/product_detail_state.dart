import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';

part 'product_detail_state.freezed.dart';

@freezed
class ProductDetailState with _$ProductDetailState {
  const factory ProductDetailState({
    @Default(null) ProductModel? product,
    @Default(false) bool isLoading,
    String? error,
    int? storedQuantity,
  }) = _ProductDetailState;
}
