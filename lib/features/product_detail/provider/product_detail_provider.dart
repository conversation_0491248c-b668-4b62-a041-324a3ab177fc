import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/product/product_repository.dart';
import 'package:kitemite_app/features/product_detail/provider/product_detail_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'product_detail_provider.g.dart';

@riverpod
class ProductDetailNotifier extends _$ProductDetailNotifier {
  @override
  ProductDetailState build() {
    return const ProductDetailState();
  }

  Future<void> loadProductDetail(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response =
          await ref.read(getProductRepositoryProvider).getProductDetail(id);

      state = state.copyWith(
        product: response.data,
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }
}
