import 'package:flutter/material.dart';
import 'package:flutter_emoji/flutter_emoji.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/common/widgets/custom_coupon/coupon_painter.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/data_base/hive_service.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/date_time_formatter.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/map_store/provider/register_product/register_product_provider.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/register_product_screen.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/take_photo_product.dart';
import 'package:kitemite_app/features/product_detail/provider/product_detail_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/features/shopping_cart/provider/cart_provider.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/model/request/product/register_product_request.dart';
import 'package:kitemite_app/model/response/product/product_hive_model.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:vibration/vibration.dart';

class ProductDetailScreenArg {
  final int productId;
  final bool isLoginWithAccount;
  final bool isUpdateProduct;
  final bool? isProductActive;
  ProductDetailScreenArg(
      {required this.productId,
      required this.isLoginWithAccount,
      required this.isUpdateProduct,
      this.isProductActive});
}

class ProductDetailScreen extends ConsumerStatefulWidget {
  const ProductDetailScreen({super.key, required this.productDetailScreenArg});
  final ProductDetailScreenArg productDetailScreenArg;

  @override
  ConsumerState<ProductDetailScreen> createState() =>
      _ProductDetailScreenState();
}

class _ProductDetailScreenState extends ConsumerState<ProductDetailScreen> {
  final List<Color> categoryColors = [
    const Color(0xFFD6E4FF),
    const Color(0xFFD8FBDE),
    const Color(0xFFCAFDF5),
    const Color(0xFFFFF5CC),
    const Color(0xFFFFE9D5),
  ];

  int? selectedImageIndex;
  int quantity = 1;
  int storedQuantity = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref
          .read(productDetailNotifierProvider.notifier)
          .loadProductDetail(widget.productDetailScreenArg.productId);

      if (widget.productDetailScreenArg.isLoginWithAccount) {
        final currentUserId =
            ref.watch(profileProviderProvider).value?.profile?.id ?? 0;
        // Get cart items
        final cartItems = await HiveService.getCartItems();
        // Find current product in cart
        final userCartItems = cartItems
            .where((item) =>
                (item as Map<String, dynamic>)['user_id'] == currentUserId &&
                (item['product'] as ProductHiveModel).id ==
                    widget.productDetailScreenArg.productId)
            .toList();

        if (mounted) {
          setState(() {
            // Sum up quantities if product exists in multiple cart items
            storedQuantity = userCartItems.fold<int>(
                0, (sum, item) => sum + (item['quantity'] as int));

            print("------------------- storedQuantity: $storedQuantity");
          });
        }
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _incrementQuantity() {
    setState(() {
      quantity++;
    });
  }

  void _decrementQuantity() {
    if (quantity > 1) {
      setState(() {
        quantity--;
      });
    }
  }

  Widget _buildHeader() {
    final product = ref.watch(productDetailNotifierProvider).product;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          product?.place?.name ?? "_", // tên cửa hàng
          style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
        ),
        Text(
          "${product?.place?.province ?? ""}, ${product?.place?.city ?? ""}, ${product?.place?.street ?? ""}", // địa chỉ
          style:
              AppTextStyles.regular(14.sp, color: AppColors.textLightSecondary),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildMainImage() {
    final product = ref.watch(productDetailNotifierProvider).product;
    return BaseCachedNetworkImage(
      imageUrl: selectedImageIndex != null &&
              product?.images != null &&
              selectedImageIndex! < product!.images!.length
          ? product.images![selectedImageIndex!].path ?? ''
          : product?.image ?? '',
      width: double.infinity,
      height: 375.h,
      fit: BoxFit.cover,
      borderRadius: BorderRadius.circular(16),
    );
  }

  Widget _buildImageGallery() {
    final product = ref.watch(productDetailNotifierProvider).product;
    return SizedBox(
      height: 55.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: product?.images?.length ?? 0,
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) => Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: GestureDetector(
            onTap: () {
              setState(() {
                selectedImageIndex = index;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                border: selectedImageIndex == index
                    ? Border.all(color: AppColors.primary, width: 2)
                    : null,
                borderRadius: BorderRadius.circular(8),
              ),
              child: BaseCachedNetworkImage(
                imageUrl: product?.images?[index].path ?? '',
                width: 55.w,
                height: 55.h,
                fit: BoxFit.cover,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDiscountBanner() {
    final product = ref.watch(productDetailNotifierProvider).product;
    if (product?.comment == null || product?.comment == '')
      return const SizedBox.shrink();
    return CustomPaint(
      size: const Size(double.infinity, 40),
      painter: BannerPainterCustom(),
      child: SizedBox(
        width: double.infinity,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                product?.comment ?? '_',
                style: AppTextStyles.bold(15.sp, color: AppColors.white),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceSection() {
    final product = ref.watch(productDetailNotifierProvider).product;
    return Row(
      children: [
        Text(
          '販売価格',
          style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
        ),
        const Spacer(),
        if (product?.price != product?.salePrice) ...[
          Text(
            '${product?.price?.priceString()}円',
            style: AppTextStyles.regular(14.sp,
                color: AppColors.textPrimary,
                decoration: TextDecoration.lineThrough),
          ),
          const SizedBox(width: 8),
          Text(
            '${product?.salePrice?.priceString()}円',
            style: AppTextStyles.bold(20.sp, color: AppColors.primary),
          ),
        ],
        if (product?.price == product?.salePrice) ...[
          Text(
            '${product?.salePrice?.priceString()}円',
            style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
          ),
        ],
      ],
    );
  }

  Widget _buildProductInfo() {
    final product = ref.watch(productDetailNotifierProvider).product;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '冷凍庫-${product?.cabinet?.cabinetCode ?? ''}: No.${product?.shelf?.shelfCode ?? ''}',
              style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
            ),
            const Spacer(),
            Text(' 残り: ${product?.quantity}',
                style: AppTextStyles.regular(
                  14.sp,
                  color: AppColors.textPrimary,
                )),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Text(
              '賞味期限 ',
              style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
            ),
            const Spacer(),
            Text(
                DateTimeFormatter.formatDate(product?.expirationDate != null
                    ? DateTime.parse(product!.expirationDate!)
                    : DateTime.now()),
                style: AppTextStyles.regular(
                  14.sp,
                  color: AppColors.textPrimary,
                )),
          ],
        ),
      ],
    );
  }

  Widget _buildStockWarning() {
    final product = ref.watch(productDetailNotifierProvider).product;
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.mono20,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        product?.note ?? '_',
        style:
            AppTextStyles.regular(12.sp, color: AppColors.textLightSecondary),
      ),
    );
  }

  Widget _buildProductDescription() {
    final parser = EmojiParser();
    final product = ref.watch(productDetailNotifierProvider).product;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          'アピールポイント',
          style: AppTextStyles.bold(16.sp, color: AppColors.textPrimary),
        ),
        const SizedBox(height: 4),
        Text(parser.emojify(product?.description ?? ''),
            style:
                AppTextStyles.regular(14.sp, color: const Color(0xFF5F5F5F))),
      ],
    );
  }

  void showCartUpdateBottomSheet(
      BuildContext context,
      String image,
      String title,
      String shelfCode,
      String price,
      Function()? onPressedDelete) {
    showModalBottomSheet( 
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SafeArea(
        bottom: true,
        top: false,
        left: false,
        right: false,
        child: CartUpdateBottomSheet(
          image: image,
          title: title,
          shelfCode: shelfCode,
          price: price,
          onPressedDelete: onPressedDelete,
        ),
      ),
    );
  }

  Future<void> _handleCartUpdate(
      ProductModel product, ProductHiveModel hiveProduct, int userId) async {
    try {
      // Get all cart items
      final cartItems = await HiveService.getCartItems();

      // Remove only current user's items
      final updatedCartItems = cartItems
          .where((item) => (item as Map<String, dynamic>)['user_id'] != userId)
          .toList();

      // Add new item
      final newItem = {
        "id_store": product.place?.id,
        "name_store": product.place?.name,
        "cabinet_code": product.cabinet?.cabinetCode,
        "shelf_code": product.shelf?.shelfCode,
        'product': hiveProduct,
        'quantity': quantity,
        'added_at': DateTime.now().toIso8601String(),
        'user_id': userId,
      };

      // Update cart with remaining items and new item
      await HiveService.updateCart([...updatedCartItems, newItem]);

      // Show success message and navigate
      if (mounted) {
        final successMessage = '${product.name}をカートに追加しました';
        // Rung thiết bị 0.2s
        Vibration.hasVibrator().then((hasVibrator) {
          if (hasVibrator ?? false) {
            Vibration.vibrate(duration: 200);
          }
        });
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.showSuccessSnackBar(successMessage);
        });
        // First navigate
        context.pop(); // Close bottom sheet
        context.pop(); // Close product detail screen

        // Then show snackbar on the new screen after navigation is complete
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            // Reload cart items
            ref.read(cartNotifierProvider.notifier).loadCartItems();
          }
        });
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar(
          'カートの更新に失敗しました。もう一度お試しください。',
        );
      }
    }
  }

  Future<void> _handleAddToCart() async {
    if (!widget.productDetailScreenArg.isLoginWithAccount) {
      showLoginRequiredDialog(context);
      return;
    }

    // Get product
    final product = ref.read(productDetailNotifierProvider).product;
    if (product != null) {
      // Check stock
      if (product.quantity != null && product.quantity! < quantity) {
        if (mounted) {
          context.showErrorSnackBar(
            '在庫が不足しています。残り: ${product.quantity}',
          );
        }
        return;
      }

      try {
        // // Save quantity
        // await _saveQuantity();

        // Convert to Hive model
        final hiveProduct = ProductHiveModel.fromProductModel(product);

        // Get current cart items
        final cartItems = await HiveService.getCartItems();

        // Get current user ID from your auth service/state
        final currentUserId =
            ref.watch(profileProviderProvider).value?.profile?.id ?? 0;

        // Filter cart items by current user
        final userCartItems = cartItems
            .where((item) =>
                (item as Map<String, dynamic>)['user_id'] == currentUserId)
            .toList();

        // Check if cart is not empty and product is from different store
        if (userCartItems.isNotEmpty) {
          final firstItem = userCartItems.first as Map<String, dynamic>;
          final storedStoreId = firstItem['id_store'];
          if (storedStoreId != product.place?.id) {
            if (mounted) {
              showCartUpdateBottomSheet(
                context,
                product.image ?? '',
                product.name ?? '',
                product.shelf?.shelfCode ?? '',
                '${product.salePrice?.priceString()}円',
                () => _handleCartUpdate(product, hiveProduct, currentUserId),
              );
            }
            return;
          }
        }

        // Check if product already exists in user's cart
        final existingIndex = userCartItems.indexWhere(
            (item) => (item['product'] as ProductHiveModel).id == product.id);

        if (existingIndex != -1) {
          // Update existing item
          final existingItem =
              userCartItems[existingIndex] as Map<String, dynamic>;
          userCartItems[existingIndex] = {
            "id_store": product.place?.id,
            "name_store": product.place?.name,
            "cabinet_code": product.cabinet?.cabinetCode,
            "shelf_code": product.shelf?.shelfCode,
            'product': hiveProduct,
            'quantity': (existingItem['quantity'] as int) + quantity,
            'added_at': DateTime.now().toIso8601String(),
            'user_id': currentUserId,
          };

          // Update only user's cart items
          final updatedCartItems = cartItems.map((item) {
            if ((item as Map<String, dynamic>)['user_id'] == currentUserId) {
              final index = userCartItems.indexWhere((userItem) =>
                  (userItem['product'] as ProductHiveModel).id ==
                  (item['product'] as ProductHiveModel).id);
              return index != -1 ? userCartItems[index] : item;
            }
            return item;
          }).toList();

          await HiveService.updateCart(updatedCartItems);
        } else {
          // Add new item
          final newItem = {
            "id_store": product.place?.id,
            "name_store": product.place?.name,
            "cabinet_code": product.cabinet?.cabinetCode,
            "shelf_code": product.shelf?.shelfCode,
            'product': hiveProduct,
            'quantity': quantity,
            'added_at': DateTime.now().toIso8601String(),
            'user_id': currentUserId,
          };
          await HiveService.addToCart(newItem);
        }

        // First update the cart
        await ref.read(cartNotifierProvider.notifier).loadCartItems();

        // Store success message
        final successMessage = '${product.name}をカートに追加しました';
        // Rung thiết bị 0.2s
        Vibration.hasVibrator().then((hasVibrator) {
          if (hasVibrator ?? false) {
            Vibration.vibrate(duration: 200);
          }
        });
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.showSuccessSnackBar(successMessage);
        });
        // First navigate
        context.pop();
      } catch (e, stackTrace) {
        print('Error adding to cart: $e');
        print('Stack trace: $stackTrace');
        if (mounted) {
          context.showErrorSnackBar(
            'カートに追加できませんでした。もう一度お試しください。',
          );
        }
      }
    }
  }

  Widget _buildQuantityAndCartButton() {
    final product = ref.watch(productDetailNotifierProvider).product;

    final availableQuantity = (product?.quantity ?? 0) - (storedQuantity);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const SizedBox(width: 16),
        IconButton(
          icon: SvgPicture.asset(
            Assets.iconRemove,
            color: quantity <= 1 ? AppColors.mono40 : null,
          ),
          onPressed: () {
            if (!widget.productDetailScreenArg.isLoginWithAccount) {
              showLoginRequiredDialog(context);
              return;
            }
            if (quantity > 1) {
              _decrementQuantity();
            }
          },
        ),
        Text(
          quantity.toString(),
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        IconButton(
          icon: SvgPicture.asset(
            Assets.iconAdd,
            color: quantity >= availableQuantity ? AppColors.mono40 : null,
          ),
          onPressed: () {
            if (!widget.productDetailScreenArg.isLoginWithAccount) {
              showLoginRequiredDialog(context);
              return;
            }
            if (quantity < availableQuantity) {
              _incrementQuantity();
            }
          },
        ),
        const SizedBox(width: 16),
        Expanded(
            child: Opacity(
          opacity: (quantity >= 1) ? 1.0 : 0.5,
          child: CommonButton(
            text: '取り置き',
            onPressed: () {
              if (quantity >= 1) {
                if (availableQuantity >= 1) {
                  _handleAddToCart();
                } else {
                  context.showErrorSnackBar(
                    '商品は現在在庫切れとなっております。',
                  );
                }
              }
            },
          ),
        )),
        const SizedBox(width: 16),
      ],
    );
  }

  Widget _buildReportButton(BuildContext context) {
    return InkWell(
      onTap: () {
        final product = ref.watch(productDetailNotifierProvider).product;
        context.push(RouterPaths.report, extra: product);
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.flag, color: AppColors.primary),
          Text(S.current.productDetailReport,
              style: AppTextStyles.regular(12.sp, color: AppColors.primary)),
        ],
      ),
    );
  }

  Widget _listTag() {
    final product = ref.watch(productDetailNotifierProvider).product;
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      alignment: WrapAlignment.start,
      crossAxisAlignment: WrapCrossAlignment.start,
      children: List.generate(
        product?.tag?.length ?? 0,
        (index) {
          final colorIndex = index % categoryColors.length;
          final color = categoryColors[colorIndex];

          return Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              product?.tag?[index] ?? '',
              style: AppTextStyles.regular(12.sp, color: AppColors.textPrimary),
            ),
          );
        },
      ),
    );
  }

  _buildUpdateButton() {
    final product = ref.read(productDetailNotifierProvider).product;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(children: [
        CommonButton(
          text: S.current.productDetailEditProduct,
          onPressed: () {
            final product = ref.read(productDetailNotifierProvider).product;
            context.push(RouterPaths.registerProductScreen,
                extra: RegisterProductArg(
                  shelfId: product?.shelf?.id ?? 0,
                  shelfCode: product?.shelf?.shelfCode ?? '',
                  cabinetCode: product?.cabinet?.cabinetCode ?? '',
                  product: product,
                  isProductActive:
                      widget.productDetailScreenArg.isProductActive,
                  sizeBox:
                      "${product?.shelf?.width ?? '0'}*${product?.shelf?.depth ?? '0'}*${product?.shelf?.height ?? '0'}",
                ));
          },
        ),
        TextButton(
            onPressed: () {
              final request = RegisterProductRequest(
                shelfId: product?.shelf?.id ?? 0,
                name: product?.name ?? '',
                description: product?.description ?? '',
                comment: product?.comment ?? '',
                note: product?.note ?? '',
                quantity: product?.quantity ?? 0,
                expirationDate: product?.expirationDate ?? '',
                capacity: product?.capacity ?? '',
                price: double.parse(product?.price ?? '0'),
                salePrice: double.parse(product?.salePrice ?? '0'),
                img: product?.image ?? '',
                tag: product?.tag ?? [],
                images: product?.images
                        ?.map((e) => ProductImageRegister(path: e.path ?? ''))
                        .toList() ??
                    [],
              );
              ref
                  .read(registerProductNotifierProvider.notifier)
                  .updateRegisterProductRequestUpdate(request);
              ref
                  .read(registerProductNotifierProvider.notifier)
                  .updatePreviewProductModel(product ?? const ProductModel());

              context.push(RouterPaths.takePhotoProduct,
                  extra: TakePhotoProductArg(
                    product: product,
                    isProductActive:
                        widget.productDetailScreenArg.isProductActive,
                  ));
            },
            child: Text(
              S.current.productDetailRetakePhotos,
              style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
            )),
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(productDetailNotifierProvider);
    final product = ref.watch(productDetailNotifierProvider).product;
    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: product?.name ?? '',
        onTap: () => context.pop(),
        actions: [
          PopupMenuButton<String>(
            color: Colors.white,
            surfaceTintColor: Colors.white,
            padding: const EdgeInsets.only(top: 8),
            offset: const Offset(0, 40),
            elevation: 2,
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              PopupMenuItem<String>(
                value: '1',
                height: 12,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                onTap: () {
                  final product =
                      ref.watch(productDetailNotifierProvider).product;
                  context.push(RouterPaths.report, extra: product);
                },
                child: SizedBox(
                  height: 30,
                  child: Center(
                    child: Text(
                      '違反商品を通報する',
                      style: AppTextStyles.regular(14.sp,
                          color: AppColors.textPrimary),
                    ),
                  ),
                ),
              ),
            ],
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: state.isLoading
          ? const Center(child: CircularProgressIndicator())
          : state.error != null
              ? Center(
                  child: ErrorCommonWidget(
                  error: state.error!,
                  onPressed: () {
                    ref
                        .read(productDetailNotifierProvider.notifier)
                        .loadProductDetail(
                            widget.productDetailScreenArg.productId);
                  },
                ))
              : SafeArea(
                  top: false,
                  child: Column(children: [
                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.zero,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16.0, vertical: 0),
                              child: _buildHeader(),
                            ),
                            SizedBox(height: 12.h),
                            _buildMainImage(),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  SizedBox(height: 12.h),
                                  _buildImageGallery(),
                                  SizedBox(height: 12.h),
                                  _buildDiscountBanner(),
                                  SizedBox(height: 12.h),
                                  _buildPriceSection(),
                                  SizedBox(height: 12.h),
                                  _buildProductInfo(),
                                  if (product?.note?.isNotEmpty == true) ...[
                                    SizedBox(height: 16.h),
                                    _buildStockWarning(),
                                  ],
                                  SizedBox(height: 12.h),
                                  _buildProductDescription(),
                                  SizedBox(height: 12.h),
                                  const Divider(
                                    color: AppColors.mono20,
                                    thickness: 1,
                                  ),
                                  SizedBox(height: 12.h),
                                  _listTag(),
                                  SizedBox(height: 12.h),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (!widget.productDetailScreenArg.isUpdateProduct) ...[
                      const Divider(
                        color: AppColors.mono20,
                        thickness: 1,
                      ),
                      SizedBox(height: 8.h),
                      _buildQuantityAndCartButton(),
                      SizedBox(height: 8.h),
                      // _buildReportButton(context),
                      // SizedBox(height: 8.h),
                    ] else ...[
                      const Divider(
                        color: AppColors.mono20,
                        thickness: 1,
                      ),
                      SizedBox(height: 8.h),
                      _buildUpdateButton(),
                      SizedBox(height: 8.h),
                    ]
                  ])),
    );
  }
}

class CartUpdateBottomSheet extends StatelessWidget {
  const CartUpdateBottomSheet(
      {super.key,
      required this.image,
      required this.title,
      required this.shelfCode,
      required this.price,
      required this.onPressedDelete});
  final String image;
  final String title;
  final String shelfCode;
  final String price;
  final Function()? onPressedDelete;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 24,
        bottom: MediaQuery.of(context).viewInsets.bottom + 24,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              S.current.productDetailCartUpdate,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Center(
            child: Text(
              S.current.productDetailCartUpdateMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.black54),
            ),
          ),
          const SizedBox(height: 24),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              BaseCachedNetworkImage(
                imageUrl: image,
                width: 50.w,
                height: 50.h,
                fit: BoxFit.cover,
                borderRadius: BorderRadius.circular(12),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(title,
                        style: AppTextStyles.bold(14.sp,
                            color: AppColors.textPrimary)),
                    const SizedBox(height: 4),
                    Center(
                      child: Row(
                        children: [
                          Text(
                            "No.$shelfCode",
                            style: AppTextStyles.regular(12.sp,
                                color: AppColors.textLightSecondary),
                          ),
                          SizedBox(width: 32.w),
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                    text: "合計: ",
                                    style: AppTextStyles.regular(12.sp,
                                        color: AppColors.textLightSecondary)),
                                TextSpan(
                                  text: price,
                                  style: AppTextStyles.regular(12.sp,
                                      color: AppColors.primary),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              OutlinedButton(
                onPressed: () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppColors.mono20),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  minimumSize: const Size(0, 44),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  S.current.productDetailBack,
                  style:
                      AppTextStyles.bold(14.sp, color: AppColors.textPrimary),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CommonButton(
                  text: S.current.productDetailDeleteAndUpdate,
                  onPressed: () => onPressedDelete?.call(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

void showLoginRequiredDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('ログインが必要です'),
      content: const Text('カートに商品を追加するにはログインが必要です。'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'キャンセル',
            style: AppTextStyles.regular(14.sp,
                color: AppColors.textLightSecondary),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            context.push(RouterPaths.login);
          },
          child: const Text('ログイン'),
        ),
      ],
    ),
  );
}
