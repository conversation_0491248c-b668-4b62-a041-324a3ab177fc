import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';

class StoreProductCardSeller extends StatelessWidget {
  final WarehouseModel store;
  final VoidCallback onTap;

  StoreProductCardSeller({
    super.key,
    required this.store,
    required this.onTap,
  });
  final List<Color> tagColors = [
    Colors.blue,
    Colors.cyan,
    Colors.green,
    Colors.orange,
    Colors.red
  ];

  Color getRandomColor(String tag) {
    // Use the tag's hash code to get a consistent color for the same tag
    final hash = tag.hashCode;
    return tagColors[hash.abs() % tagColors.length];
  }

  @override
  Widget build(BuildContext context) {
    final tags = store.tags;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: store.listImage.map((img) {
              // Tìm productId tương ứng với img
              final product = store.products.firstWhere(
                (p) => p.img == img,
                orElse: () => const WarehouseProductModel(),
              );
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: GestureDetector(
                  onTap: product.id != null
                      ? () {
                          context.push(
                            RouterPaths.productDetail,
                            extra: ProductDetailScreenArg(
                              productId: product.id!,
                              isLoginWithAccount: true,
                              isUpdateProduct: true,
                            ),
                          );
                        }
                      : null,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: BaseCachedNetworkImage(
                      imageUrl: img,
                      width: 84,
                      height: 84,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          child: Column(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        store.name ?? "",
                        style: AppTextStyles.bold(16.sp,
                            color: AppColors.textPrimary),
                      ),
                      const Spacer(),
                      Container(
                        decoration: BoxDecoration(
                          color: store.isWaiting
                              ? const Color(0xFFFFF5CC)
                              : store.isActive
                                  ? const Color(0xFFD8FBDE)
                                  : AppColors.success.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(
                            vertical: 4, horizontal: 8),
                        child: Text(
                          store.isWaiting ? '未納品あり' : '全て格納済',
                          style: AppTextStyles.regular(12.sp,
                              color: store.isWaiting
                                  ? const Color(0xFFFFB92A)
                                  : store.isActive
                                      ? const Color(0xFF007B55)
                                      : AppColors.success),
                        ),
                      )
                    ],
                  ),
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.textLightSecondary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(
                            vertical: 4, horizontal: 8),
                        child: Text(
                          '商品: ${store.productsCount}',
                          style: AppTextStyles.regular(12.sp,
                              color: AppColors.textLightSecondary),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.textLightSecondary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(
                            vertical: 4, horizontal: 8),
                        child: Text(
                          '商品数: ${store.productsQuantity}',
                          style: AppTextStyles.regular(12.sp,
                              color: AppColors.textLightSecondary),
                        ),
                      )
                    ],
                  ),
                  Text(
                    "${store.province ?? ""}, ${store.city ?? ""}, ${store.street ?? ""}",
                    style: AppTextStyles.regular(12.sp,
                        color: AppColors.textLightSecondary),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  // Tags
                  ...List.generate(tags.length, (index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 6),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: getRandomColor(tags[index]),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        tags[index],
                        style:
                            const TextStyle(color: Colors.white, fontSize: 14),
                      ),
                    );
                  }),
                ],
              ),
            ],
          ),
        )
      ],
    );
  }
}
