import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';

class ProductItem extends StatelessWidget {
  final String imagePath;
  final String title;
  final String price;
  final String remainingStock;

  const ProductItem({
    super.key,
    required this.imagePath,
    required this.title,
    required this.price,
    required this.remainingStock,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16.r),
            child: BaseCachedNetworkImage(
              imageUrl: imagePath,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(16.r),
            ),
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          title,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyles.bold(
            12.sp,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 4.h),
        _buildPriceRow(),
      ],
    );
  }

  Widget _buildPriceRow() {
    return Row(
      children: [
        Expanded(
          child: Text(
            price,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: AppTextStyles.medium(12.sp, color: AppColors.primary),
          ),
        ),
        Text(
          "残り:",
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style:
              AppTextStyles.medium(12.sp, color: AppColors.textLightSecondary),
        ),
        SizedBox(width: 4.w),
        Text(
          remainingStock,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyles.medium(12.sp, color: AppColors.textPrimary),
        ),
      ],
    );
  }
}
