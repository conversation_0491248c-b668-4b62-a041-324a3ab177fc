// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_store_seller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$detailStoreSellerNotifierHash() =>
    r'4b3a6eafa80d988be902cb3509616ed54acbabeb';

/// See also [DetailStoreSellerNotifier].
@ProviderFor(DetailStoreSellerNotifier)
final detailStoreSellerNotifierProvider = AutoDisposeNotifierProvider<
    DetailStoreSellerNotifier, DetailStoreSellerState>.internal(
  DetailStoreSellerNotifier.new,
  name: r'detailStoreSellerNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$detailStoreSellerNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DetailStoreSellerNotifier
    = AutoDisposeNotifier<DetailStoreSellerState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
