import 'dart:convert';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:badges/badges.dart' as badges;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/ui/base_search_widget.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/service/location_service.dart';
import 'package:kitemite_app/core/service/push_notification_service.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/home/<USER>/home_buyer/home_provider.dart';
import 'package:kitemite_app/features/home/<USER>/widget/product_item.dart';
import 'package:kitemite_app/features/notification/provider/notification_provider.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_mode/mode_state.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:skeletonizer/skeletonizer.dart';

import 'seller_home_screen.dart';

class HomeModeScreen extends ConsumerStatefulWidget {
  const HomeModeScreen({super.key, required this.isLoginWithAccount});
  final bool isLoginWithAccount;

  @override
  ConsumerState<HomeModeScreen> createState() => _HomeModeScreenState();
}

class _HomeModeScreenState extends ConsumerState<HomeModeScreen> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(modeNotifierProvider.notifier).loadModeFromPrefs();
    });
  }

  @override
  Widget build(BuildContext context) {
    final modeState = ref.watch(modeNotifierProvider);

    if (widget.isLoginWithAccount == false ||
        modeState.mode == ModeAccount.guest) {
      return const HomeScreen(isLoginWithAccount: false);
    }

    return modeState.mode == ModeAccount.personal
        ? const HomeScreen(isLoginWithAccount: true)
        : const SellerHomeScreen();
  }
}

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key, required this.isLoginWithAccount});
  final bool isLoginWithAccount;

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      //get location
      ref.read(homeNotifierProvider.notifier).setLoading();
      final currentLocation = await LocationService.getCurrentLocation();
      ref.read(homeNotifierProvider.notifier).loadProducts(
            isRefresh: false,
            latitude: currentLocation?.latitude,
            longitude: currentLocation?.longitude,
          );
      if (widget.isLoginWithAccount) {
        ref.read(profileProviderProvider.notifier).loadProfile();
        ref.read(notificationNotifierProvider.notifier).init();
      }

      // Check for initial notification after UI is initialized
      checkForInitialNotification(context);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      ref.read(homeNotifierProvider.notifier).loadMore();
    }
  }

  Future<void> _navigatorToProfileScreen(BuildContext context) async {
    context.push(RouterPaths.profileScreen);
  }

  @override
  Widget build(BuildContext context) {
    final profile = widget.isLoginWithAccount
        ? ref.watch(profileProviderProvider).value?.profile
        : null;

    final homeState = ref.watch(homeNotifierProvider);

    final unseenCount = widget.isLoginWithAccount
        ? ref.watch(notificationNotifierProvider).unseenCount
        : 0;

    if (widget.isLoginWithAccount) {
      ref.listen(profileProviderProvider, (previous, next) {
        if (next is AsyncError) {
          context.showErrorSnackBar(next.error.toString());
        }
      });
    }

    return SafeArea(
      top: false,
      maintainBottomViewPadding: false,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(15, 8, 15, 16),
            child: Row(
              children: [
                if (widget.isLoginWithAccount)
                  if (profile?.accountHasRoleSeller == true)
                    SizedBox(
                      width: 60.r,
                      height: 40.r,
                      child: Stack(
                        clipBehavior: Clip.none,
                        children: [
                          Positioned(
                            left: 0,
                            child: GestureDetector(
                              onTap: () => _navigatorToProfileScreen(context),
                              child: BaseCachedNetworkImage(
                                imageUrl: profile?.store?.img ?? '',
                                width: 40.r,
                                height: 40.r,
                                borderRadius: BorderRadius.circular(20.r),
                                placeholder: CircleAvatar(
                                  radius: 20.r,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(20.r),
                                    child: Assets.avatarDefault.image(),
                                  ),
                                ),
                                errorWidget: CircleAvatar(
                                  radius: 20.r,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(20.r),
                                    child: Assets.avatarDefault.image(),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            left: 20.r,
                            child: GestureDetector(
                                onTap: () => _navigatorToProfileScreen(context),
                                child: Container(
                                  width: 42.r,
                                  height: 42.r,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white, // Viền trắng
                                      width: 2, // Độ dày viền
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(20.r),
                                    child: BaseCachedNetworkImage(
                                      imageUrl: profile?.img ?? '',
                                      width: 40.r,
                                      height: 40.r,
                                      borderRadius: BorderRadius.circular(20.r),
                                      placeholder: CircleAvatar(
                                        radius: 20.r,
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(20.r),
                                          child: Assets.avatarDefault.image(),
                                        ),
                                      ),
                                      errorWidget: CircleAvatar(
                                        radius: 20.r,
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(20.r),
                                          child: Assets.avatarDefault.image(),
                                        ),
                                      ),
                                    ),
                                  ),
                                )),
                          ),
                        ],
                      ),
                    ),
                if (widget.isLoginWithAccount)
                  if (profile?.accountHasRoleSeller == false)
                    GestureDetector(
                      onTap: () => _navigatorToProfileScreen(context),
                      child: BaseCachedNetworkImage(
                        imageUrl: profile?.img ?? '',
                        width: 40.r,
                        height: 40.r,
                        borderRadius: BorderRadius.circular(20.r),
                        placeholder: CircleAvatar(
                          radius: 20.r,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(20.r),
                            child: Assets.avatarDefault.image(),
                          ),
                        ),
                        errorWidget: CircleAvatar(
                          radius: 20.r,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(20.r),
                            child: Assets.avatarDefault.image(),
                          ),
                        ),
                      ),
                    ),
                SizedBox(width: 8.w),
                Expanded(
                  child: BaseSearchWidget(
                    onSearch: (keyword) {
                      ref
                          .read(homeNotifierProvider.notifier)
                          .searchProducts(keyword);
                    },
                  ),
                ),
                SizedBox(width: 8.w),
                if (widget.isLoginWithAccount)
                  InkWell(
                    onTap: () {
                      context.push(RouterPaths.listNotification);
                    },
                    child: unseenCount > 0
                        ? badges.Badge(
                            position:
                                badges.BadgePosition.topEnd(top: -8, end: -8),
                            badgeAnimation: const badges.BadgeAnimation.slide(),
                            showBadge: true,
                            badgeStyle: const badges.BadgeStyle(
                              badgeColor: AppColors.rambutan100,
                            ),
                            badgeContent: Text(
                              unseenCount > 99 ? '99+' : unseenCount.toString(),
                              style: AppTextStyles.regular(10.sp,
                                  color: Colors.white),
                            ),
                            child: SvgPicture.asset(
                                Assets.notifications24dpE8EAED31),
                          )
                        : SvgPicture.asset(Assets.notifications24dpE8EAED31),
                  )
              ],
            ),
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                if (widget.isLoginWithAccount) {
                  await ref
                      .read(profileProviderProvider.notifier)
                      .loadProfile();
                }
                await ref.read(homeNotifierProvider.notifier).loadProducts(
                      isRefresh: true,
                    );
              },
              child: homeState.isLoading
                  ? GridView.builder(
                      itemCount: 9,
                      padding: EdgeInsets.all(9.sp),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 8.0,
                        mainAxisSpacing: 16.0,
                        childAspectRatio: 0.7,
                      ),
                      itemBuilder: (context, index) {
                        return const Skeletonizer(
                          enabled: true,
                          child: ProductItem(
                            imagePath: "",
                            title: "Loading...",
                            price: "0円",
                            remainingStock: "0",
                          ),
                        );
                      },
                    )
                  : homeState.error != null
                      ? Center(
                          child: ErrorCommonWidget(
                          error: homeState.error!,
                          onPressed: () {
                            ref
                                .read(homeNotifierProvider.notifier)
                                .loadProducts(
                                  isRefresh: true,
                                );
                          },
                        ))
                      : homeState.products.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Assets.iconEmptyProduct.image(),
                                  SizedBox(height: 16.h),
                                  Text(
                                    '商品がありません。',
                                    style: AppTextStyles.regular(16.sp,
                                        color: AppColors.textLightSecondary),
                                  ),
                                ],
                              ),
                            )
                          : Column(
                              children: [
                                SizedBox(height: 8.h),
                                if (widget.isLoginWithAccount &&
                                    (profile?.accountHasRoleSeller ==
                                        true)) ...[
                                  Text(
                                    '${profile?.point} ポイント',
                                    style: AppTextStyles.bold(20.sp,
                                        color: AppColors.primary),
                                  ),
                                  Text(
                                    'ポイントで購入できます。',
                                    style: AppTextStyles.regular(12.sp,
                                        color: AppColors.textLightSecondary),
                                  ),
                                  SizedBox(height: 8.h),
                                ],
                                Expanded(
                                  child: GridView.builder(
                                    controller: _scrollController,
                                    itemCount: homeState.products.length +
                                        (homeState.currentPage <
                                                homeState.lastPage
                                            ? 1
                                            : 0),
                                    padding: EdgeInsets.all(9.sp),
                                    gridDelegate:
                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 3,
                                      crossAxisSpacing: 8.0,
                                      mainAxisSpacing: 16.0,
                                      childAspectRatio: 0.7,
                                    ),
                                    itemBuilder: (contextItem, index) {
                                      if (index == homeState.products.length) {
                                        return homeState.isLoadingMore
                                            ? const Center(
                                                child:
                                                    CircularProgressIndicator())
                                            : const SizedBox.shrink();
                                      }

                                      final product = homeState.products[index];
                                      return InkWell(
                                        onTap: () {
                                          context.push(
                                              RouterPaths.productDetail,
                                              extra: ProductDetailScreenArg(
                                                  productId: product.id ?? 0,
                                                  isLoginWithAccount:
                                                      widget.isLoginWithAccount,
                                                  isUpdateProduct: false));
                                        },
                                        child: ProductItem(
                                          imagePath: product.image ?? "",
                                          title: product.name ?? "",
                                          price:
                                              "${product.salePrice?.priceString()}円",
                                          remainingStock:
                                              product.quantity?.toString() ??
                                                  "0",
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
            ),
          ),
        ],
      ),
    );
  }
}

Future<void> checkForInitialNotification(BuildContext context) async {
  // Check if app was opened from a notification
  final initialAction =
      await AwesomeNotifications().getInitialNotificationAction();
  if (initialAction != null) {
    // Add delay to ensure UI is fully initialized
    await Future.delayed(const Duration(milliseconds: 500));

    // Verify navigator context is still available

    if (!context.mounted) {
      print(
          'WARNING: Navigator context not available for initial notification');
      return;
    }
// Default check: regular notification with id
    final notificationId = initialAction.payload?['id'] != null
        ? int.tryParse(initialAction.payload!['id']!)
        : null;
    // Priority check: sold-histories notification
    if (initialAction.payload?['screen'] == 'sold-histories') {
      final configString = initialAction.payload!['config'] as String;
      final configJson = json.decode(configString) as Map<String, dynamic>;
      final productId = configJson['product_id'].toString();
      final orderId = configJson['order_id'].toString();

      // Use post frame callback to ensure all widgets are built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        PushNotificationService.navigateToProductManagementHistory(
          productId,
          orderId,
          notificationId ?? 0,
        );

      });
      return;
    }

    

    if (notificationId != null) {
      // Use post frame callback to ensure all widgets are built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        PushNotificationService.navigateToNotificationDetail(notificationId);
      });
    }
  }
}
