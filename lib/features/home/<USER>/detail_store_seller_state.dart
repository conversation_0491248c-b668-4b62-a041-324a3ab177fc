import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';

part 'detail_store_seller_state.freezed.dart';

@freezed
class DetailStoreSellerState with _$DetailStoreSellerState {
  const factory DetailStoreSellerState({
    WarehouseModel? warehouse,
    @Default(false) bool isLoading,
    String? error,
    @Default('') String searchKeyword,
  }) = _DetailStoreSellerState;
}
