// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'detail_store_seller_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DetailStoreSellerState {
  WarehouseModel? get warehouse => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String get searchKeyword => throw _privateConstructorUsedError;

  /// Create a copy of DetailStoreSellerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DetailStoreSellerStateCopyWith<DetailStoreSellerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DetailStoreSellerStateCopyWith<$Res> {
  factory $DetailStoreSellerStateCopyWith(DetailStoreSellerState value,
          $Res Function(DetailStoreSellerState) then) =
      _$DetailStoreSellerStateCopyWithImpl<$Res, DetailStoreSellerState>;
  @useResult
  $Res call(
      {WarehouseModel? warehouse,
      bool isLoading,
      String? error,
      String searchKeyword});

  $WarehouseModelCopyWith<$Res>? get warehouse;
}

/// @nodoc
class _$DetailStoreSellerStateCopyWithImpl<$Res,
        $Val extends DetailStoreSellerState>
    implements $DetailStoreSellerStateCopyWith<$Res> {
  _$DetailStoreSellerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DetailStoreSellerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouse = freezed,
    Object? isLoading = null,
    Object? error = freezed,
    Object? searchKeyword = null,
  }) {
    return _then(_value.copyWith(
      warehouse: freezed == warehouse
          ? _value.warehouse
          : warehouse // ignore: cast_nullable_to_non_nullable
              as WarehouseModel?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      searchKeyword: null == searchKeyword
          ? _value.searchKeyword
          : searchKeyword // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of DetailStoreSellerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WarehouseModelCopyWith<$Res>? get warehouse {
    if (_value.warehouse == null) {
      return null;
    }

    return $WarehouseModelCopyWith<$Res>(_value.warehouse!, (value) {
      return _then(_value.copyWith(warehouse: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DetailStoreSellerStateImplCopyWith<$Res>
    implements $DetailStoreSellerStateCopyWith<$Res> {
  factory _$$DetailStoreSellerStateImplCopyWith(
          _$DetailStoreSellerStateImpl value,
          $Res Function(_$DetailStoreSellerStateImpl) then) =
      __$$DetailStoreSellerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {WarehouseModel? warehouse,
      bool isLoading,
      String? error,
      String searchKeyword});

  @override
  $WarehouseModelCopyWith<$Res>? get warehouse;
}

/// @nodoc
class __$$DetailStoreSellerStateImplCopyWithImpl<$Res>
    extends _$DetailStoreSellerStateCopyWithImpl<$Res,
        _$DetailStoreSellerStateImpl>
    implements _$$DetailStoreSellerStateImplCopyWith<$Res> {
  __$$DetailStoreSellerStateImplCopyWithImpl(
      _$DetailStoreSellerStateImpl _value,
      $Res Function(_$DetailStoreSellerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DetailStoreSellerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouse = freezed,
    Object? isLoading = null,
    Object? error = freezed,
    Object? searchKeyword = null,
  }) {
    return _then(_$DetailStoreSellerStateImpl(
      warehouse: freezed == warehouse
          ? _value.warehouse
          : warehouse // ignore: cast_nullable_to_non_nullable
              as WarehouseModel?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      searchKeyword: null == searchKeyword
          ? _value.searchKeyword
          : searchKeyword // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DetailStoreSellerStateImpl implements _DetailStoreSellerState {
  const _$DetailStoreSellerStateImpl(
      {this.warehouse,
      this.isLoading = false,
      this.error,
      this.searchKeyword = ''});

  @override
  final WarehouseModel? warehouse;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;
  @override
  @JsonKey()
  final String searchKeyword;

  @override
  String toString() {
    return 'DetailStoreSellerState(warehouse: $warehouse, isLoading: $isLoading, error: $error, searchKeyword: $searchKeyword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DetailStoreSellerStateImpl &&
            (identical(other.warehouse, warehouse) ||
                other.warehouse == warehouse) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.searchKeyword, searchKeyword) ||
                other.searchKeyword == searchKeyword));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, warehouse, isLoading, error, searchKeyword);

  /// Create a copy of DetailStoreSellerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DetailStoreSellerStateImplCopyWith<_$DetailStoreSellerStateImpl>
      get copyWith => __$$DetailStoreSellerStateImplCopyWithImpl<
          _$DetailStoreSellerStateImpl>(this, _$identity);
}

abstract class _DetailStoreSellerState implements DetailStoreSellerState {
  const factory _DetailStoreSellerState(
      {final WarehouseModel? warehouse,
      final bool isLoading,
      final String? error,
      final String searchKeyword}) = _$DetailStoreSellerStateImpl;

  @override
  WarehouseModel? get warehouse;
  @override
  bool get isLoading;
  @override
  String? get error;
  @override
  String get searchKeyword;

  /// Create a copy of DetailStoreSellerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DetailStoreSellerStateImplCopyWith<_$DetailStoreSellerStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
