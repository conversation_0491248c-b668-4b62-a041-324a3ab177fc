import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/product/product_repository.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/features/home/<USER>/detail_store_seller_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'detail_store_seller_provider.g.dart';

@riverpod
class DetailStoreSellerNotifier extends _$DetailStoreSellerNotifier {
  @override
  DetailStoreSellerState build() {
    return const DetailStoreSellerState();
  }

  Future<void> loadWarehouseDetail(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await ref
          .read(getStoreRepositoryProvider)
          .getStoreDetailWarehouse(id);
      state = state.copyWith(
        warehouse: response.data,
        isLoading: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }

  Future<void> deleteProduct(
      {required int id, required int warehouseId}) async {
    state = state.copyWith(error: null);
    try {
      await ref.read(getProductRepositoryProvider).deleteProduct(id);

      if (state.warehouse != null && state.warehouse!.cabinets != null) {
        final updatedCabinets = state.warehouse!.cabinets!
            .map((cabinet) {
              if (cabinet.products != null) {
                final updatedProducts = cabinet.products!
                    .where((product) => product.id != id)
                    .toList();
                return cabinet.copyWith(products: updatedProducts);
              }
              return cabinet;
            })
            .where((cabinet) => cabinet.products?.isNotEmpty ?? true)
            .toList();

        final updatedWarehouse = state.warehouse!.copyWith(
          cabinets: updatedCabinets,
          productsCount: 0,
        );

        state = state.copyWith(
          warehouse: updatedWarehouse,
          isLoading: false,
          error: null,
        );
      }
    } on AppFailure catch (e) {
      state = state.copyWith(isLoading: false, error: e.message);
    }
  }

  void searchProducts(String keyword) {
    state = state.copyWith(searchKeyword: keyword);
  }
}
