import 'package:badges/badges.dart' as badges;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/home/<USER>/home_seller/seller_home_provider.dart';
import 'package:kitemite_app/features/home/<USER>/home_screen.dart';
import 'package:kitemite_app/features/home/<USER>/widget/store_product_card_seller.dart';
import 'package:kitemite_app/features/map_store/provider/map_store/map_store_provider.dart';
import 'package:kitemite_app/features/notification/provider/notification_provider.dart';
import 'package:kitemite_app/features/profile/provider/profile_user/profile_provider.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SellerHomeScreen extends ConsumerStatefulWidget {
  const SellerHomeScreen({super.key});

  @override
  ConsumerState<SellerHomeScreen> createState() => _SellerHomeScreenState();
}

class _SellerHomeScreenState extends ConsumerState<SellerHomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(sellerHomeNotifierProvider.notifier).loadWarehouses();
      ref.read(profileProviderProvider.notifier).loadProfile();
      ref.read(notificationNotifierProvider.notifier).init();
      checkForInitialNotification(context);
    });
  }

  Future<void> _navigatorToProfileScreen(BuildContext context) async {
    context.push(RouterPaths.profileScreen);
  }

  void _navigateToMapStore(BuildContext context) {
    ref.read(mapStoreNotifierProvider.notifier).reloadMap();
    context.go(RouterPaths.mapStore);
  }

  @override
  Widget build(BuildContext context) {
    final profile = ref.watch(profileProviderProvider).value?.profile;

    final sellerHomeState = ref.watch(sellerHomeNotifierProvider);

    final unseenCount = ref.watch(notificationNotifierProvider).unseenCount;

    ref.listen(profileProviderProvider, (previous, next) {
      if (next is AsyncError) {
        context.showErrorSnackBar(next.error.toString());
      }
    });

    return SafeArea(
        top: false,
        maintainBottomViewPadding: false,
        child: Stack(children: [
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(15, 8, 15, 0),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () => _navigatorToProfileScreen(context),
                      child: BaseCachedNetworkImage(
                          imageUrl: profile?.store?.img ?? '',
                          width: 40.r,
                          height: 40.r,
                          borderRadius: BorderRadius.circular(20.r),
                          errorWidget: CircleAvatar(
                              radius: 20.r,
                              child: ClipRRect(
                                  borderRadius: BorderRadius.circular(20.r),
                                  child: Assets.avatarDefault.image())),
                          placeholder: CircleAvatar(
                              radius: 20.r,
                              child: ClipRRect(
                                  borderRadius: BorderRadius.circular(20.r),
                                  child: Assets.avatarDefault.image()))),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      profile?.store?.name ?? '',
                      style: AppTextStyles.bold(16.sp,
                          color: AppColors.textPrimary),
                    ),
                    const Spacer(),
                    SizedBox(width: 8.w),
                    InkWell(
                        onTap: () {
                          context.push(RouterPaths.listNotification);
                        },
                        child: unseenCount > 0
                            ? badges.Badge(
                                position: badges.BadgePosition.topEnd(
                                    top: -8, end: -8),
                                badgeAnimation:
                                    const badges.BadgeAnimation.slide(),
                                showBadge: true,
                                badgeStyle: const badges.BadgeStyle(
                                  badgeColor: AppColors.rambutan100,
                                ),
                                badgeContent: Text(
                                  unseenCount > 99
                                      ? '99+'
                                      : unseenCount.toString(),
                                  style: AppTextStyles.regular(10.sp,
                                      color: Colors.white),
                                ),
                                child: SvgPicture.asset(
                                    Assets.notifications24dpE8EAED31),
                              )
                            : SvgPicture.asset(
                                Assets.notifications24dpE8EAED31)),
                  ],
                ),
              ),
              Expanded(
                child: sellerHomeState.isLoading
                    ? Skeletonizer(
                        enabled: true,
                        child: ListView.separated(
                          padding: const EdgeInsets.all(16),
                          itemCount: 3,
                          itemBuilder: (context, index) {
                            return StoreProductCardSeller(
                              store: const WarehouseModel(
                                id: 0,
                                name: 'Loading Store',
                                status: 'Loading',
                                productsCount: 0,
                                productsQuantity: 0,
                                province: 'Loading',
                                city: 'Loading',
                                street: 'Loading',
                                cabinets: [],
                              ),
                              onTap: () {},
                            );
                          },
                          separatorBuilder: (context, index) =>
                              SizedBox(height: 32.h),
                        ),
                      )
                    : sellerHomeState.error != null
                        ? Center(
                            child: ErrorCommonWidget(
                            error: sellerHomeState.error!,
                            onPressed: () {
                              ref
                                  .read(sellerHomeNotifierProvider.notifier)
                                  .loadWarehouses();
                            },
                          ))
                        : sellerHomeState.warehouses.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(Assets.shopGeneric),
                                    SizedBox(height: 16.h),
                                    Text.rich(
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                            text:
                                                '店舗がまだ登録されていません。',
                                            style: AppTextStyles.medium(14.sp,
                                                color: AppColors.textPrimary),
                                          ),
                                          WidgetSpan(
                                            child: GestureDetector(
                                              onTap: () =>
                                                  _navigateToMapStore(context),
                                              child: Text(
                                                '「店舗を選択」',
                                                style: AppTextStyles.medium(
                                                  14.sp,
                                                  color: AppColors.primary,
                                                  decoration:
                                                      TextDecoration.underline,
                                                  decorationColor:
                                                      AppColors.primary,
                                                ),
                                              ),
                                            ),
                                          ),
                                          TextSpan(
                                            text: ' して、今すぐご登録ください',
                                            style: AppTextStyles.medium(14.sp,
                                                color: AppColors.textPrimary),
                                          ),
                                        ],
                                      ),
                                      style: AppTextStyles.regular(14.sp),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              )
                            : RefreshIndicator(
                                onRefresh: () async {
                                  await ref
                                      .read(sellerHomeNotifierProvider.notifier)
                                      .loadWarehouses();
                                },
                                child: ListView.separated(
                                  padding: const EdgeInsets.all(16),
                                  itemCount: sellerHomeState.warehouses.length,
                                  itemBuilder: (context, index) {
                                    final warehouse =
                                        sellerHomeState.warehouses[index];
                                    return StoreProductCardSeller(
                                      store: warehouse,
                                      onTap: () {
                                        context
                                            .push(RouterPaths.detailStoreSeller,
                                                extra: warehouse.id)
                                            .then((value) {
                                          ref
                                              .read(sellerHomeNotifierProvider
                                                  .notifier)
                                              .loadWarehouses();
                                        });
                                      },
                                    );
                                  },
                                  separatorBuilder: (context, index) =>
                                      SizedBox(height: 32.h),
                                ),
                              ),
              )
            ],
          ),
          Positioned(
            right: 16,
            bottom: 0,
            child: GestureDetector(
              onTap: () {
                _navigateToMapStore(context);
              },
              child: SvgPicture.asset(
                Assets.floatingAdd,
                width: 80,
                height: 80,
              ),
            ),
          ),
        ]));
  }
}
