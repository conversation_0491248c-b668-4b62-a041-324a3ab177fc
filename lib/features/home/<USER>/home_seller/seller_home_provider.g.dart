// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_home_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerHomeNotifierHash() =>
    r'23b3480697525717212a79a11fed21d67d645dc1';

/// See also [SellerHomeNotifier].
@ProviderFor(SellerHomeNotifier)
final sellerHomeNotifierProvider =
    AutoDisposeNotifierProvider<SellerHomeNotifier, SellerHomeState>.internal(
  SellerHomeNotifier.new,
  name: r'sellerHomeNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sellerHomeNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SellerHomeNotifier = AutoDisposeNotifier<SellerHomeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
