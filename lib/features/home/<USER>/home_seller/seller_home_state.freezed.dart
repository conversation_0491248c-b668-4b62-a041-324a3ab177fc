// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_home_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SellerHomeState {
  List<WarehouseModel> get warehouses => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of SellerHomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SellerHomeStateCopyWith<SellerHomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SellerHomeStateCopyWith<$Res> {
  factory $SellerHomeStateCopyWith(
          SellerHomeState value, $Res Function(SellerHomeState) then) =
      _$SellerHomeStateCopyWithImpl<$Res, SellerHomeState>;
  @useResult
  $Res call({List<WarehouseModel> warehouses, bool isLoading, String? error});
}

/// @nodoc
class _$SellerHomeStateCopyWithImpl<$Res, $Val extends SellerHomeState>
    implements $SellerHomeStateCopyWith<$Res> {
  _$SellerHomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SellerHomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouses = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      warehouses: null == warehouses
          ? _value.warehouses
          : warehouses // ignore: cast_nullable_to_non_nullable
              as List<WarehouseModel>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SellerHomeStateImplCopyWith<$Res>
    implements $SellerHomeStateCopyWith<$Res> {
  factory _$$SellerHomeStateImplCopyWith(_$SellerHomeStateImpl value,
          $Res Function(_$SellerHomeStateImpl) then) =
      __$$SellerHomeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<WarehouseModel> warehouses, bool isLoading, String? error});
}

/// @nodoc
class __$$SellerHomeStateImplCopyWithImpl<$Res>
    extends _$SellerHomeStateCopyWithImpl<$Res, _$SellerHomeStateImpl>
    implements _$$SellerHomeStateImplCopyWith<$Res> {
  __$$SellerHomeStateImplCopyWithImpl(
      _$SellerHomeStateImpl _value, $Res Function(_$SellerHomeStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerHomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouses = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_$SellerHomeStateImpl(
      warehouses: null == warehouses
          ? _value._warehouses
          : warehouses // ignore: cast_nullable_to_non_nullable
              as List<WarehouseModel>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SellerHomeStateImpl implements _SellerHomeState {
  const _$SellerHomeStateImpl(
      {final List<WarehouseModel> warehouses = const [],
      this.isLoading = true,
      this.error})
      : _warehouses = warehouses;

  final List<WarehouseModel> _warehouses;
  @override
  @JsonKey()
  List<WarehouseModel> get warehouses {
    if (_warehouses is EqualUnmodifiableListView) return _warehouses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_warehouses);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;

  @override
  String toString() {
    return 'SellerHomeState(warehouses: $warehouses, isLoading: $isLoading, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SellerHomeStateImpl &&
            const DeepCollectionEquality()
                .equals(other._warehouses, _warehouses) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_warehouses), isLoading, error);

  /// Create a copy of SellerHomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SellerHomeStateImplCopyWith<_$SellerHomeStateImpl> get copyWith =>
      __$$SellerHomeStateImplCopyWithImpl<_$SellerHomeStateImpl>(
          this, _$identity);
}

abstract class _SellerHomeState implements SellerHomeState {
  const factory _SellerHomeState(
      {final List<WarehouseModel> warehouses,
      final bool isLoading,
      final String? error}) = _$SellerHomeStateImpl;

  @override
  List<WarehouseModel> get warehouses;
  @override
  bool get isLoading;
  @override
  String? get error;

  /// Create a copy of SellerHomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SellerHomeStateImplCopyWith<_$SellerHomeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
