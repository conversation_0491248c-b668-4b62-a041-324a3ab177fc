import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/store/store_repository.dart';
import 'package:kitemite_app/features/home/<USER>/home_seller/seller_home_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'seller_home_provider.g.dart';

@riverpod
class SellerHomeNotifier extends _$SellerHomeNotifier {
  @override
  SellerHomeState build() {
    return const SellerHomeState();
  }

  Future<void> loadWarehouses() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response =
          await ref.read(getStoreRepositoryProvider).getListStoreSeller();
      state = state.copyWith(
        warehouses: response.data ?? [],
        isLoading: false,
      );
    } on AppFailure catch (e) {
      if (e.code != 401) {
        state = state.copyWith(
          isLoading: false,
          error: e.message,
        );
      }
    }
  }
}
