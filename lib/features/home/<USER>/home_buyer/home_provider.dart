import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/product/product_repository.dart';
import 'package:kitemite_app/features/home/<USER>/home_buyer/home_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'home_provider.g.dart';

@riverpod
class HomeNotifier extends _$HomeNotifier {
  late final ProductRepository _repo;

  @override
  HomeState build() {
    _repo = ref.read(getProductRepositoryProvider);
    return const HomeState();
  }

  Future<void> loadProducts(
      {required bool isRefresh, double? latitude, double? longitude}) async {
    try {
      state = state.copyWith(
        isLoading: isRefresh ? false : true,
        error: null,
      );
      if (latitude != null && longitude != null) {
        state = state.copyWith(
          latitude: latitude,
          longitude: longitude,
        );
      }
      final response = await _repo.getProducts(
          1, 10, state.searchKeyword, state.latitude, state.longitude);
      state = state.copyWith(
        products: response.data ?? [],
        currentPage: response.currentPage ?? 1,
        lastPage: response.lastPage ?? 1,
        perPage: response.perPage ?? 10,
        isLoading: false,
        searchKeyword: state.searchKeyword,
      );
    } on AppFailure catch (e) {
      if (e.code != 401) {
        state = state.copyWith(
          error: e.message,
          isLoading: false,
        );
      }
    }
  }

  Future<void> loadMore() async {
    if (state.isLoadingMore || state.currentPage >= state.lastPage) return;

    state = state.copyWith(isLoadingMore: true, error: null);
    try {
      final response = await _repo.getProducts(
        state.currentPage + 1,
        10,
        state.searchKeyword,
        state.latitude,
        state.longitude,
      );
      state = state.copyWith(
        products: [...state.products, ...response.data ?? []],
        currentPage: response.currentPage ?? state.currentPage + 1,
        lastPage: response.lastPage ?? state.lastPage,
        perPage: response.perPage ?? state.perPage,
        isLoadingMore: false,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isLoadingMore: false,
      );
    }
  }

  Future<void> searchProducts(String keyword) async {
    state = state.copyWith(isLoading: true);
    try {
      String? keyword0 = keyword.trim();
      if (keyword0.isEmpty) {
        keyword0 = null;
      }
      final response = await _repo.getProducts(
          1, 10, keyword0, state.latitude, state.longitude);
      state = state.copyWith(
        products: response.data ?? [],
        currentPage: response.currentPage ?? 1,
        lastPage: response.lastPage ?? 1,
        perPage: response.perPage ?? 10,
        isLoading: false,
        searchKeyword: keyword0,
      );
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isLoading: false,
      );
    }
  }

  void setLoading() {
    state = state.copyWith(isLoading: true);
  }
}
