import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';

part 'home_state.freezed.dart';

@freezed
class HomeState with _$HomeState {
  const factory HomeState({
    @Default([]) List<ProductModel> products,
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingMore,
    @Default(1) int currentPage,
    @Default(1) int lastPage,
    @Default(10) int perPage,
    String? error,
    String? searchKeyword,
    double? latitude,
    double? longitude,
  }) = _HomeState;
}
