 
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/data_base/hive_config.dart';
import 'package:kitemite_app/core/service/background_worker_service.dart';
import 'package:kitemite_app/core/service/push_notification_service.dart';
import 'package:kitemite_app/core/utils/app_preferences.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/flavors.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/routing/router.dart';
import 'package:talker_riverpod_logger/talker_riverpod_logger_observer.dart';

import 'core/utils/app_version_service.dart';
 
void mainRoot() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  try {
    // Initialize Hive
    await HiveConfig.init();
    await AppPreferences.init();
    await AppVersionService().init();
    await Firebase.initializeApp(
      options: F.firebaseOptions,
    );
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    await PushNotificationService.initializeNotification();
    await PushNotificationService.firebaseMessagingHandler();

    // Initialize background worker service
    await BackgroundWorkerService.initialize();
  
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  } catch (e) {
    print('Error in mainRoot: $e');
  }

  runApp(
    ProviderScope(
      observers: [if (kDebugMode) TalkerRiverpodObserver()],
      child: const MyApp(),
    ),
  );
}

// Add this to handle app termination
@pragma('vm:entry-point')
void onAppTerminate() async {
  await HiveConfig.close();
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    // Register the observer for app lifecycle changes
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    // Remove the observer when the widget is disposed
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Update app lifecycle state in PushNotificationService
    PushNotificationService.updateAppLifecycleState(state);

    // When app resumes from background, sync badge
    if (state == AppLifecycleState.resumed) {
      // After a short delay to ensure context is available
      Future.delayed(const Duration(milliseconds: 500), () async {
        // Check if user is logged in before syncing badge
        final token = AppPreferences.getString(AppConstants.tokenKey);
        if (token != null && token.isNotEmpty) {
          PushNotificationService.syncBadgeOnAppStart();
        }
      });
    }

    // When app goes to background, register background task for iOS
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      final token = AppPreferences.getString(AppConstants.tokenKey);
      if (token != null && token.isNotEmpty) {
        BackgroundWorkerService.registerBadgeSyncTask();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check for initial notification on app start

    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return MaterialApp.router(
              title: 'Kitemite App',
              theme: context.lightTheme,
              darkTheme: context.darkTheme,
              themeMode: ThemeMode.light,
              routerConfig: appRouter,
              debugShowCheckedModeBanner: false,
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('ja'),
                Locale('en'),
              ],
              locale: const Locale('ja'));
        });
  }
}
