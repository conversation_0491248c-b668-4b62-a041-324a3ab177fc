// Generated by Hive CE
// Do not modify
// Check in to version control

import 'package:hive_ce/hive.dart';
import 'package:kitemite_app/model/response/product/product_hive_model.dart';

extension HiveRegistrar on HiveInterface {
  void registerAdapters() {
    registerAdapter(CabinetHiveModelAdapter());
    registerAdapter(ProductHiveModelAdapter());
    registerAdapter(ProductImageHiveModelAdapter());
    registerAdapter(ShelfHiveModelAdapter());
  }
}

extension IsolatedHiveRegistrar on IsolatedHiveInterface {
  void registerAdapters() {
    registerAdapter(CabinetHiveModelAdapter());
    registerAdapter(ProductHiveModelAdapter());
    registerAdapter(ProductImageHiveModelAdapter());
    registerAdapter(ShelfHiveModelAdapter());
  }
}
