{"@@locale": "en", "hello": "Hello", "welcome": "Welcome", "login": "<PERSON><PERSON>", "notificationTitle": "Notification", "updateMessage": "A new version of the app has been released", "updateLater": "Later", "updateStore": "Go to Store", "emailAddress": "Email Address", "enterEmailAddress": "Enter email address", "termsAndPrivacyPolicy": "Please check the \"Terms of Service\" and \"Privacy Policy\" of the KITEMITE app. After checking, please check the checkboxes.", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "iUnderstandTerms": "I understand the Terms of Service.", "iUnderstandPrivacy": "I understand the Privacy Policy.", "iAmOver20": "I am over 20 years old.", "sendAuthCode": "Send Authentication Code", "viewAsGuest": "View as Guest", "authCode": "Authentication Code", "enterAuthCode": "Enter authentication code", "authCodeSent": "We have sent an authentication code to your registered email address.\nPlease enter the code to log in.", "authCodeNotReceived": "Didn't receive the code?", "resend": "Resend {time}", "userRegistration": "User Registration", "nickname": "Nickname", "birthYear": "Year of Birth", "prefecture": "Prefecture", "gender": "Gender", "occupation": "Occupation", "uploadInstructions": "Maximum 50MB,\n*.jpeg, *.jpg, *.png, *.gif\nfiles only.", "updatePhoto": "Update Photo", "next": "Next", "takePhoto": "Take Photo", "chooseFromLibrary": "Choose from Library", "paymentMethodRegistration": "Payment Method Registration", "paymentMethodDescription": "You can register your payment method for product purchases.", "canRegisterLater": "You can register later.", "creditCard": "Credit Card", "addCard": "Add Card", "skip": "<PERSON><PERSON>", "productManagementYourProducts": "Your products", "productManagementProducts": "Products", "productManagementTemplate": "Template", "productManagementHistory": "History", "productManagementNoProducts": "No products", "productManagementNoTemplates": "No templates", "productManagementDelete": "Delete", "selectCabinetTitle": "Select cabinets and storage compartments", "selectCabinetContinue": "Continue", "selectCabinetAvailable": "Available", "selectCabinetRegistered": "Registered", "selectCabinetBooked": "Already booked", "registerProductTitle": "Register Product", "registerProductUpdateTitle": "Update Product", "registerProductSalesPrice": "Sales price", "registerProductListPrice": "List price", "registerProductIncludingTax": "Including consumption tax and handling fee", "registerProductSize": "Size", "registerProductSizeDescription": "Total length x width x height", "registerProductInventory": "The amount of inventory to bring", "registerProductExpirationDate": "Expiration date", "registerProductNote": "Note", "registerProductDetails": "Product details", "registerProductWarning": "Lưu ý: <PERSON><PERSON><PERSON> tôi sẽ chiếu khấu 35% trên mỗi sản phẩm", "registerProductUpdate": "Update", "registerProductContinue": "Continue", "registerProductCancel": "Cancel", "registerProductAddNew": "Add new \"{text}\"", "registerProductValidationSalesPriceRequired": "Sales price is required", "registerProductValidationListPriceRequired": "List price is required", "takePhotoProductUpdateTitle": "Update product photos", "takePhotoProductTitle": "Take photos of the product", "takePhotoProductGallery": "Gallery", "takePhotoProductCamera": "Camera", "takePhotoProductNoImages": "No images taken yet", "takePhotoProductUpdate": "Update", "takePhotoProductConfirm": "Confirm", "takePhotoProductCancel": "Cancel", "takePhotoProductValidationAtLeastOneImage": "Please select at least one image", "takePhotoProductValidationAtLeastSixImages": "Please select at least 6 images", "takePhotoProductErrorCaptureImage": "Failed to capture image. Please try again.", "takePhotoProductErrorSelectImages": "Failed to select images. Please try again.", "takePhotoProductUpdateSuccess": "Update product success", "takePhotoProductRegisterSuccess": "Register product success", "productDetailPreviewUpdateProduct": "Update product", "productDetailPreviewPublishProduct": "Publish product", "productDetailPreviewBackToEdit": "Back to edit", "productDetailPreviewRetakePhotos": "Retake photos", "productDetailReport": "Report", "productDetailEditProduct": "Edit product", "productDetailRetakePhotos": "Retake photos", "productDetailCartUpdate": "Cart update", "productDetailCartUpdateMessage": "You have added products in another store, do you want to delete all products and update again?", "productDetailTotal": "Total", "productDetailBack": "Back", "productDetailDeleteAndUpdate": "Delete & Update new product"}