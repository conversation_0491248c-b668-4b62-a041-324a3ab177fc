// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ja locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ja';

  static String m0(text) => "\"${text}\"のテンプレート追加";

  static String m1(time) => "再送${time}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "addCard": MessageLookupByLibrary.simpleMessage("カードを追加する"),
    "authCode": MessageLookupByLibrary.simpleMessage("認証コード"),
    "authCodeNotReceived": MessageLookupByLibrary.simpleMessage("認証コードが届かない?"),
    "authCodeSent": MessageLookupByLibrary.simpleMessage(
      "登録のメールアドレスに認証コードを送信しました。\n認証コードを入力してログインしてください。",
    ),
    "birthYear": MessageLookupByLibrary.simpleMessage("生まれた年"),
    "canRegisterLater": MessageLookupByLibrary.simpleMessage("後から登録も可能です。"),
    "chooseFromLibrary": MessageLookupByLibrary.simpleMessage("ライブラリから選択"),
    "creditCard": MessageLookupByLibrary.simpleMessage("クレジットカード"),
    "emailAddress": MessageLookupByLibrary.simpleMessage("メールアドレス"),
    "enterAuthCode": MessageLookupByLibrary.simpleMessage("認証コードを入力"),
    "enterEmailAddress": MessageLookupByLibrary.simpleMessage("メールアドレス"),
    "gender": MessageLookupByLibrary.simpleMessage("性別"),
    "hello": MessageLookupByLibrary.simpleMessage("こんにちは"),
    "iAmOver20": MessageLookupByLibrary.simpleMessage("20 歳以上でご利用いただけます。"),
    "iUnderstandPrivacy": MessageLookupByLibrary.simpleMessage(
      "プライバシーポリシーを理解しました。",
    ),
    "iUnderstandTerms": MessageLookupByLibrary.simpleMessage("利用規約を理解しました。"),
    "login": MessageLookupByLibrary.simpleMessage("ログイン"),
    "next": MessageLookupByLibrary.simpleMessage("次へ"),
    "nickname": MessageLookupByLibrary.simpleMessage("ニックネーム"),
    "notificationTitle": MessageLookupByLibrary.simpleMessage("お知らせ"),
    "occupation": MessageLookupByLibrary.simpleMessage("ご職業"),
    "paymentMethodDescription": MessageLookupByLibrary.simpleMessage(
      "商品購入時の決済方法を登録できます。",
    ),
    "paymentMethodRegistration": MessageLookupByLibrary.simpleMessage(
      "決済方法の登録",
    ),
    "prefecture": MessageLookupByLibrary.simpleMessage("都道府県"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("プライバシポリシー"),
    "productDetailBack": MessageLookupByLibrary.simpleMessage("キャンセル"),
    "productDetailCartUpdate": MessageLookupByLibrary.simpleMessage("カートの編集"),
    "productDetailCartUpdateMessage": MessageLookupByLibrary.simpleMessage(
      "他の販売所の商品が追加されています。すべての商品を削除して再更新しますか？",
    ),
    "productDetailDeleteAndUpdate": MessageLookupByLibrary.simpleMessage("更新"),
    "productDetailEditProduct": MessageLookupByLibrary.simpleMessage("商品の編集"),
    "productDetailPreviewBackToEdit": MessageLookupByLibrary.simpleMessage(
      "商品情報の編集",
    ),
    "productDetailPreviewPublishProduct": MessageLookupByLibrary.simpleMessage(
      "商品の販売開始",
    ),
    "productDetailPreviewRetakePhotos": MessageLookupByLibrary.simpleMessage(
      "画像の編集",
    ),
    "productDetailPreviewUpdateProduct": MessageLookupByLibrary.simpleMessage(
      "商品情報の編集",
    ),
    "productDetailReport": MessageLookupByLibrary.simpleMessage("通報"),
    "productDetailRetakePhotos": MessageLookupByLibrary.simpleMessage("画像の編集"),
    "productDetailTotal": MessageLookupByLibrary.simpleMessage("合計"),
    "productManagementDelete": MessageLookupByLibrary.simpleMessage("削除"),
    "productManagementHistory": MessageLookupByLibrary.simpleMessage("履歴"),
    "productManagementNoProducts": MessageLookupByLibrary.simpleMessage(
      "商品がありません。",
    ),
    "productManagementNoTemplates": MessageLookupByLibrary.simpleMessage(
      "テンプレートがありません。",
    ),
    "productManagementProducts": MessageLookupByLibrary.simpleMessage("商品"),
    "productManagementTemplate": MessageLookupByLibrary.simpleMessage("テンプレート"),
    "productManagementYourProducts": MessageLookupByLibrary.simpleMessage(
      "あなたの商品",
    ),
    "registerProductAddNew": m0,
    "registerProductCancel": MessageLookupByLibrary.simpleMessage("キャンセル"),
    "registerProductContinue": MessageLookupByLibrary.simpleMessage("次へ"),
    "registerProductDetails": MessageLookupByLibrary.simpleMessage("商品の詳細"),
    "registerProductExpirationDate": MessageLookupByLibrary.simpleMessage(
      "賞味期限",
    ),
    "registerProductIncludingTax": MessageLookupByLibrary.simpleMessage(
      "上記価格は税込みです。",
    ),
    "registerProductInventory": MessageLookupByLibrary.simpleMessage("在庫数"),
    "registerProductListPrice": MessageLookupByLibrary.simpleMessage("定価"),
    "registerProductNote": MessageLookupByLibrary.simpleMessage("備考"),
    "registerProductSalesPrice": MessageLookupByLibrary.simpleMessage("販売価格"),
    "registerProductSize": MessageLookupByLibrary.simpleMessage("商品の大きさ"),
    "registerProductSizeDescription": MessageLookupByLibrary.simpleMessage(
      "幅 * 高さ * 奥行",
    ),
    "registerProductTitle": MessageLookupByLibrary.simpleMessage("商品の登録"),
    "registerProductUpdate": MessageLookupByLibrary.simpleMessage("更新"),
    "registerProductUpdateTitle": MessageLookupByLibrary.simpleMessage("商品の更新"),
    "registerProductValidationListPriceRequired":
        MessageLookupByLibrary.simpleMessage("定価は必須です"),
    "registerProductValidationSalesPriceRequired":
        MessageLookupByLibrary.simpleMessage("販売価格は必須です。"),
    "registerProductWarning": MessageLookupByLibrary.simpleMessage(
      "注意: 販売者に対する手数料は35％です。",
    ),
    "resend": m1,
    "selectCabinetAvailable": MessageLookupByLibrary.simpleMessage("空き"),
    "selectCabinetBooked": MessageLookupByLibrary.simpleMessage("格納済"),
    "selectCabinetContinue": MessageLookupByLibrary.simpleMessage("次へ"),
    "selectCabinetRegistered": MessageLookupByLibrary.simpleMessage("登録済"),
    "selectCabinetTitle": MessageLookupByLibrary.simpleMessage("商品セルの選択"),
    "sendAuthCode": MessageLookupByLibrary.simpleMessage("認証コードを送信する"),
    "skip": MessageLookupByLibrary.simpleMessage("スキップ"),
    "takePhoto": MessageLookupByLibrary.simpleMessage("写真を撮る"),
    "takePhotoProductCamera": MessageLookupByLibrary.simpleMessage("カメラ"),
    "takePhotoProductCancel": MessageLookupByLibrary.simpleMessage("キャンセル"),
    "takePhotoProductConfirm": MessageLookupByLibrary.simpleMessage("確定"),
    "takePhotoProductErrorCaptureImage": MessageLookupByLibrary.simpleMessage(
      "撮影出来ませんでした。もう一度お試しください。",
    ),
    "takePhotoProductErrorSelectImages": MessageLookupByLibrary.simpleMessage(
      "アルバムからアップロード出来ませんでした。もう一度お試しください。",
    ),
    "takePhotoProductGallery": MessageLookupByLibrary.simpleMessage("アルバム"),
    "takePhotoProductNoImages": MessageLookupByLibrary.simpleMessage(
      "画像はまだありません。",
    ),
    "takePhotoProductRegisterSuccess": MessageLookupByLibrary.simpleMessage(
      "商品が登録されました。",
    ),
    "takePhotoProductTitle": MessageLookupByLibrary.simpleMessage(
      "商品画像のアップロード",
    ),
    "takePhotoProductUpdate": MessageLookupByLibrary.simpleMessage("更新"),
    "takePhotoProductUpdateSuccess": MessageLookupByLibrary.simpleMessage(
      "アップロードしました。",
    ),
    "takePhotoProductUpdateTitle": MessageLookupByLibrary.simpleMessage(
      "商品画像の更新",
    ),
    "takePhotoProductValidationAtLeastOneImage":
        MessageLookupByLibrary.simpleMessage("画像を一枚以上選択してください。"),
    "takePhotoProductValidationAtLeastSixImages":
        MessageLookupByLibrary.simpleMessage("画像を6枚選択してください。"),
    "termsAndPrivacyPolicy": MessageLookupByLibrary.simpleMessage(
      "KITEMITEアプリの「利用規約」と「プライバシポリシー」をご確認下さい。ご確認後チェックボックスにチェクを入れて下さい。",
    ),
    "termsOfService": MessageLookupByLibrary.simpleMessage("利用規約"),
    "updateLater": MessageLookupByLibrary.simpleMessage("後で"),
    "updateMessage": MessageLookupByLibrary.simpleMessage(
      "新しいバージョンのアプリがリリースされました",
    ),
    "updatePhoto": MessageLookupByLibrary.simpleMessage("写真を更新"),
    "updateStore": MessageLookupByLibrary.simpleMessage("ストアへ"),
    "uploadInstructions": MessageLookupByLibrary.simpleMessage(
      "最大50MBで、\n*.jpeg, *.jpg, *.png, *.gif\nファイルだけアップロードしてください。",
    ),
    "userRegistration": MessageLookupByLibrary.simpleMessage("ユーザー登録"),
    "viewAsGuest": MessageLookupByLibrary.simpleMessage("ゲストで見る"),
    "welcome": MessageLookupByLibrary.simpleMessage("ようこそ"),
  };
}
