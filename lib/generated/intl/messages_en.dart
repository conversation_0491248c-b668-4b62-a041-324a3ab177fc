// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(text) => "Add new \"${text}\"";

  static String m1(time) => "Resend ${time}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "addCard": MessageLookupByLibrary.simpleMessage("Add Card"),
    "authCode": MessageLookupByLibrary.simpleMessage("Authentication Code"),
    "authCodeNotReceived": MessageLookupByLibrary.simpleMessage(
      "Didn\'t receive the code?",
    ),
    "authCodeSent": MessageLookupByLibrary.simpleMessage(
      "We have sent an authentication code to your registered email address.\nPlease enter the code to log in.",
    ),
    "birthYear": MessageLookupByLibrary.simpleMessage("Year of Birth"),
    "canRegisterLater": MessageLookupByLibrary.simpleMessage(
      "You can register later.",
    ),
    "chooseFromLibrary": MessageLookupByLibrary.simpleMessage(
      "Choose from Library",
    ),
    "creditCard": MessageLookupByLibrary.simpleMessage("Credit Card"),
    "emailAddress": MessageLookupByLibrary.simpleMessage("Email Address"),
    "enterAuthCode": MessageLookupByLibrary.simpleMessage(
      "Enter authentication code",
    ),
    "enterEmailAddress": MessageLookupByLibrary.simpleMessage(
      "Enter email address",
    ),
    "gender": MessageLookupByLibrary.simpleMessage("Gender"),
    "hello": MessageLookupByLibrary.simpleMessage("Hello"),
    "iAmOver20": MessageLookupByLibrary.simpleMessage(
      "I am over 20 years old.",
    ),
    "iUnderstandPrivacy": MessageLookupByLibrary.simpleMessage(
      "I understand the Privacy Policy.",
    ),
    "iUnderstandTerms": MessageLookupByLibrary.simpleMessage(
      "I understand the Terms of Service.",
    ),
    "login": MessageLookupByLibrary.simpleMessage("Login"),
    "next": MessageLookupByLibrary.simpleMessage("Next"),
    "nickname": MessageLookupByLibrary.simpleMessage("Nickname"),
    "notificationTitle": MessageLookupByLibrary.simpleMessage("Notification"),
    "occupation": MessageLookupByLibrary.simpleMessage("Occupation"),
    "paymentMethodDescription": MessageLookupByLibrary.simpleMessage(
      "You can register your payment method for product purchases.",
    ),
    "paymentMethodRegistration": MessageLookupByLibrary.simpleMessage(
      "Payment Method Registration",
    ),
    "prefecture": MessageLookupByLibrary.simpleMessage("Prefecture"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "productDetailBack": MessageLookupByLibrary.simpleMessage("Back"),
    "productDetailCartUpdate": MessageLookupByLibrary.simpleMessage(
      "Cart update",
    ),
    "productDetailCartUpdateMessage": MessageLookupByLibrary.simpleMessage(
      "You have added products in another store, do you want to delete all products and update again?",
    ),
    "productDetailDeleteAndUpdate": MessageLookupByLibrary.simpleMessage(
      "Delete & Update new product",
    ),
    "productDetailEditProduct": MessageLookupByLibrary.simpleMessage(
      "Edit product",
    ),
    "productDetailPreviewBackToEdit": MessageLookupByLibrary.simpleMessage(
      "Back to edit",
    ),
    "productDetailPreviewPublishProduct": MessageLookupByLibrary.simpleMessage(
      "Publish product",
    ),
    "productDetailPreviewRetakePhotos": MessageLookupByLibrary.simpleMessage(
      "Retake photos",
    ),
    "productDetailPreviewUpdateProduct": MessageLookupByLibrary.simpleMessage(
      "Update product",
    ),
    "productDetailReport": MessageLookupByLibrary.simpleMessage("Report"),
    "productDetailRetakePhotos": MessageLookupByLibrary.simpleMessage(
      "Retake photos",
    ),
    "productDetailTotal": MessageLookupByLibrary.simpleMessage("Total"),
    "productManagementDelete": MessageLookupByLibrary.simpleMessage("Delete"),
    "productManagementHistory": MessageLookupByLibrary.simpleMessage("History"),
    "productManagementNoProducts": MessageLookupByLibrary.simpleMessage(
      "No products",
    ),
    "productManagementNoTemplates": MessageLookupByLibrary.simpleMessage(
      "No templates",
    ),
    "productManagementProducts": MessageLookupByLibrary.simpleMessage(
      "Products",
    ),
    "productManagementTemplate": MessageLookupByLibrary.simpleMessage(
      "Template",
    ),
    "productManagementYourProducts": MessageLookupByLibrary.simpleMessage(
      "Your products",
    ),
    "registerProductAddNew": m0,
    "registerProductCancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "registerProductContinue": MessageLookupByLibrary.simpleMessage("Continue"),
    "registerProductDetails": MessageLookupByLibrary.simpleMessage(
      "Product details",
    ),
    "registerProductExpirationDate": MessageLookupByLibrary.simpleMessage(
      "Expiration date",
    ),
    "registerProductIncludingTax": MessageLookupByLibrary.simpleMessage(
      "Including consumption tax and handling fee",
    ),
    "registerProductInventory": MessageLookupByLibrary.simpleMessage(
      "The amount of inventory to bring",
    ),
    "registerProductListPrice": MessageLookupByLibrary.simpleMessage(
      "List price",
    ),
    "registerProductNote": MessageLookupByLibrary.simpleMessage("Note"),
    "registerProductSalesPrice": MessageLookupByLibrary.simpleMessage(
      "Sales price",
    ),
    "registerProductSize": MessageLookupByLibrary.simpleMessage("Size"),
    "registerProductSizeDescription": MessageLookupByLibrary.simpleMessage(
      "Total length x width x height",
    ),
    "registerProductTitle": MessageLookupByLibrary.simpleMessage(
      "Register Product",
    ),
    "registerProductUpdate": MessageLookupByLibrary.simpleMessage("Update"),
    "registerProductUpdateTitle": MessageLookupByLibrary.simpleMessage(
      "Update Product",
    ),
    "registerProductValidationListPriceRequired":
        MessageLookupByLibrary.simpleMessage("List price is required"),
    "registerProductValidationSalesPriceRequired":
        MessageLookupByLibrary.simpleMessage("Sales price is required"),
    "registerProductWarning": MessageLookupByLibrary.simpleMessage(
      "Lưu ý: Chúng tôi sẽ chiếu khấu 35% trên mỗi sản phẩm",
    ),
    "resend": m1,
    "selectCabinetAvailable": MessageLookupByLibrary.simpleMessage("Available"),
    "selectCabinetBooked": MessageLookupByLibrary.simpleMessage(
      "Already booked",
    ),
    "selectCabinetContinue": MessageLookupByLibrary.simpleMessage("Continue"),
    "selectCabinetRegistered": MessageLookupByLibrary.simpleMessage(
      "Registered",
    ),
    "selectCabinetTitle": MessageLookupByLibrary.simpleMessage(
      "Select cabinets and storage compartments",
    ),
    "sendAuthCode": MessageLookupByLibrary.simpleMessage(
      "Send Authentication Code",
    ),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "takePhoto": MessageLookupByLibrary.simpleMessage("Take Photo"),
    "takePhotoProductCamera": MessageLookupByLibrary.simpleMessage("Camera"),
    "takePhotoProductCancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "takePhotoProductConfirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "takePhotoProductErrorCaptureImage": MessageLookupByLibrary.simpleMessage(
      "Failed to capture image. Please try again.",
    ),
    "takePhotoProductErrorSelectImages": MessageLookupByLibrary.simpleMessage(
      "Failed to select images. Please try again.",
    ),
    "takePhotoProductGallery": MessageLookupByLibrary.simpleMessage("Gallery"),
    "takePhotoProductNoImages": MessageLookupByLibrary.simpleMessage(
      "No images taken yet",
    ),
    "takePhotoProductRegisterSuccess": MessageLookupByLibrary.simpleMessage(
      "Register product success",
    ),
    "takePhotoProductTitle": MessageLookupByLibrary.simpleMessage(
      "Take photos of the product",
    ),
    "takePhotoProductUpdate": MessageLookupByLibrary.simpleMessage("Update"),
    "takePhotoProductUpdateSuccess": MessageLookupByLibrary.simpleMessage(
      "Update product success",
    ),
    "takePhotoProductUpdateTitle": MessageLookupByLibrary.simpleMessage(
      "Update product photos",
    ),
    "takePhotoProductValidationAtLeastOneImage":
        MessageLookupByLibrary.simpleMessage(
          "Please select at least one image",
        ),
    "takePhotoProductValidationAtLeastSixImages":
        MessageLookupByLibrary.simpleMessage("Please select at least 6 images"),
    "termsAndPrivacyPolicy": MessageLookupByLibrary.simpleMessage(
      "Please check the \"Terms of Service\" and \"Privacy Policy\" of the KITEMITE app. After checking, please check the checkboxes.",
    ),
    "termsOfService": MessageLookupByLibrary.simpleMessage("Terms of Service"),
    "updateLater": MessageLookupByLibrary.simpleMessage("Later"),
    "updateMessage": MessageLookupByLibrary.simpleMessage(
      "A new version of the app has been released",
    ),
    "updatePhoto": MessageLookupByLibrary.simpleMessage("Update Photo"),
    "updateStore": MessageLookupByLibrary.simpleMessage("Go to Store"),
    "uploadInstructions": MessageLookupByLibrary.simpleMessage(
      "Maximum 50MB,\n*.jpeg, *.jpg, *.png, *.gif\nfiles only.",
    ),
    "userRegistration": MessageLookupByLibrary.simpleMessage(
      "User Registration",
    ),
    "viewAsGuest": MessageLookupByLibrary.simpleMessage("View as Guest"),
    "welcome": MessageLookupByLibrary.simpleMessage("Welcome"),
  };
}
