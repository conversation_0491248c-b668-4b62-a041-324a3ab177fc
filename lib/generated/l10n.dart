// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name =
        (locale.countryCode?.isEmpty ?? false)
            ? locale.languageCode
            : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Hello`
  String get hello {
    return Intl.message('Hello', name: 'hello', desc: '', args: []);
  }

  /// `Welcome`
  String get welcome {
    return Intl.message('Welcome', name: 'welcome', desc: '', args: []);
  }

  /// `Login`
  String get login {
    return Intl.message('Login', name: 'login', desc: '', args: []);
  }

  /// `Notification`
  String get notificationTitle {
    return Intl.message(
      'Notification',
      name: 'notificationTitle',
      desc: '',
      args: [],
    );
  }

  /// `A new version of the app has been released`
  String get updateMessage {
    return Intl.message(
      'A new version of the app has been released',
      name: 'updateMessage',
      desc: '',
      args: [],
    );
  }

  /// `Later`
  String get updateLater {
    return Intl.message('Later', name: 'updateLater', desc: '', args: []);
  }

  /// `Go to Store`
  String get updateStore {
    return Intl.message('Go to Store', name: 'updateStore', desc: '', args: []);
  }

  /// `Email Address`
  String get emailAddress {
    return Intl.message(
      'Email Address',
      name: 'emailAddress',
      desc: '',
      args: [],
    );
  }

  /// `Enter email address`
  String get enterEmailAddress {
    return Intl.message(
      'Enter email address',
      name: 'enterEmailAddress',
      desc: '',
      args: [],
    );
  }

  /// `Please check the "Terms of Service" and "Privacy Policy" of the KITEMITE app. After checking, please check the checkboxes.`
  String get termsAndPrivacyPolicy {
    return Intl.message(
      'Please check the "Terms of Service" and "Privacy Policy" of the KITEMITE app. After checking, please check the checkboxes.',
      name: 'termsAndPrivacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Terms of Service`
  String get termsOfService {
    return Intl.message(
      'Terms of Service',
      name: 'termsOfService',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `I understand the Terms of Service.`
  String get iUnderstandTerms {
    return Intl.message(
      'I understand the Terms of Service.',
      name: 'iUnderstandTerms',
      desc: '',
      args: [],
    );
  }

  /// `I understand the Privacy Policy.`
  String get iUnderstandPrivacy {
    return Intl.message(
      'I understand the Privacy Policy.',
      name: 'iUnderstandPrivacy',
      desc: '',
      args: [],
    );
  }

  /// `I am over 20 years old.`
  String get iAmOver20 {
    return Intl.message(
      'I am over 20 years old.',
      name: 'iAmOver20',
      desc: '',
      args: [],
    );
  }

  /// `Send Authentication Code`
  String get sendAuthCode {
    return Intl.message(
      'Send Authentication Code',
      name: 'sendAuthCode',
      desc: '',
      args: [],
    );
  }

  /// `View as Guest`
  String get viewAsGuest {
    return Intl.message(
      'View as Guest',
      name: 'viewAsGuest',
      desc: '',
      args: [],
    );
  }

  /// `Authentication Code`
  String get authCode {
    return Intl.message(
      'Authentication Code',
      name: 'authCode',
      desc: '',
      args: [],
    );
  }

  /// `Enter authentication code`
  String get enterAuthCode {
    return Intl.message(
      'Enter authentication code',
      name: 'enterAuthCode',
      desc: '',
      args: [],
    );
  }

  /// `We have sent an authentication code to your registered email address.\nPlease enter the code to log in.`
  String get authCodeSent {
    return Intl.message(
      'We have sent an authentication code to your registered email address.\nPlease enter the code to log in.',
      name: 'authCodeSent',
      desc: '',
      args: [],
    );
  }

  /// `Didn't receive the code?`
  String get authCodeNotReceived {
    return Intl.message(
      'Didn\'t receive the code?',
      name: 'authCodeNotReceived',
      desc: '',
      args: [],
    );
  }

  /// `Resend {time}`
  String resend(Object time) {
    return Intl.message('Resend $time', name: 'resend', desc: '', args: [time]);
  }

  /// `User Registration`
  String get userRegistration {
    return Intl.message(
      'User Registration',
      name: 'userRegistration',
      desc: '',
      args: [],
    );
  }

  /// `Nickname`
  String get nickname {
    return Intl.message('Nickname', name: 'nickname', desc: '', args: []);
  }

  /// `Year of Birth`
  String get birthYear {
    return Intl.message('Year of Birth', name: 'birthYear', desc: '', args: []);
  }

  /// `Prefecture`
  String get prefecture {
    return Intl.message('Prefecture', name: 'prefecture', desc: '', args: []);
  }

  /// `Gender`
  String get gender {
    return Intl.message('Gender', name: 'gender', desc: '', args: []);
  }

  /// `Occupation`
  String get occupation {
    return Intl.message('Occupation', name: 'occupation', desc: '', args: []);
  }

  /// `Maximum 50MB,\n*.jpeg, *.jpg, *.png, *.gif\nfiles only.`
  String get uploadInstructions {
    return Intl.message(
      'Maximum 50MB,\n*.jpeg, *.jpg, *.png, *.gif\nfiles only.',
      name: 'uploadInstructions',
      desc: '',
      args: [],
    );
  }

  /// `Update Photo`
  String get updatePhoto {
    return Intl.message(
      'Update Photo',
      name: 'updatePhoto',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get next {
    return Intl.message('Next', name: 'next', desc: '', args: []);
  }

  /// `Take Photo`
  String get takePhoto {
    return Intl.message('Take Photo', name: 'takePhoto', desc: '', args: []);
  }

  /// `Choose from Library`
  String get chooseFromLibrary {
    return Intl.message(
      'Choose from Library',
      name: 'chooseFromLibrary',
      desc: '',
      args: [],
    );
  }

  /// `Payment Method Registration`
  String get paymentMethodRegistration {
    return Intl.message(
      'Payment Method Registration',
      name: 'paymentMethodRegistration',
      desc: '',
      args: [],
    );
  }

  /// `You can register your payment method for product purchases.`
  String get paymentMethodDescription {
    return Intl.message(
      'You can register your payment method for product purchases.',
      name: 'paymentMethodDescription',
      desc: '',
      args: [],
    );
  }

  /// `You can register later.`
  String get canRegisterLater {
    return Intl.message(
      'You can register later.',
      name: 'canRegisterLater',
      desc: '',
      args: [],
    );
  }

  /// `Credit Card`
  String get creditCard {
    return Intl.message('Credit Card', name: 'creditCard', desc: '', args: []);
  }

  /// `Add Card`
  String get addCard {
    return Intl.message('Add Card', name: 'addCard', desc: '', args: []);
  }

  /// `Skip`
  String get skip {
    return Intl.message('Skip', name: 'skip', desc: '', args: []);
  }

  /// `Your products`
  String get productManagementYourProducts {
    return Intl.message(
      'Your products',
      name: 'productManagementYourProducts',
      desc: '',
      args: [],
    );
  }

  /// `Products`
  String get productManagementProducts {
    return Intl.message(
      'Products',
      name: 'productManagementProducts',
      desc: '',
      args: [],
    );
  }

  /// `Template`
  String get productManagementTemplate {
    return Intl.message(
      'Template',
      name: 'productManagementTemplate',
      desc: '',
      args: [],
    );
  }

  /// `History`
  String get productManagementHistory {
    return Intl.message(
      'History',
      name: 'productManagementHistory',
      desc: '',
      args: [],
    );
  }

  /// `No products`
  String get productManagementNoProducts {
    return Intl.message(
      'No products',
      name: 'productManagementNoProducts',
      desc: '',
      args: [],
    );
  }

  /// `No templates`
  String get productManagementNoTemplates {
    return Intl.message(
      'No templates',
      name: 'productManagementNoTemplates',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get productManagementDelete {
    return Intl.message(
      'Delete',
      name: 'productManagementDelete',
      desc: '',
      args: [],
    );
  }

  /// `Select cabinets and storage compartments`
  String get selectCabinetTitle {
    return Intl.message(
      'Select cabinets and storage compartments',
      name: 'selectCabinetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get selectCabinetContinue {
    return Intl.message(
      'Continue',
      name: 'selectCabinetContinue',
      desc: '',
      args: [],
    );
  }

  /// `Available`
  String get selectCabinetAvailable {
    return Intl.message(
      'Available',
      name: 'selectCabinetAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Registered`
  String get selectCabinetRegistered {
    return Intl.message(
      'Registered',
      name: 'selectCabinetRegistered',
      desc: '',
      args: [],
    );
  }

  /// `Already booked`
  String get selectCabinetBooked {
    return Intl.message(
      'Already booked',
      name: 'selectCabinetBooked',
      desc: '',
      args: [],
    );
  }

  /// `Register Product`
  String get registerProductTitle {
    return Intl.message(
      'Register Product',
      name: 'registerProductTitle',
      desc: '',
      args: [],
    );
  }

  /// `Update Product`
  String get registerProductUpdateTitle {
    return Intl.message(
      'Update Product',
      name: 'registerProductUpdateTitle',
      desc: '',
      args: [],
    );
  }

  /// `Sales price`
  String get registerProductSalesPrice {
    return Intl.message(
      'Sales price',
      name: 'registerProductSalesPrice',
      desc: '',
      args: [],
    );
  }

  /// `List price`
  String get registerProductListPrice {
    return Intl.message(
      'List price',
      name: 'registerProductListPrice',
      desc: '',
      args: [],
    );
  }

  /// `Including consumption tax and handling fee`
  String get registerProductIncludingTax {
    return Intl.message(
      'Including consumption tax and handling fee',
      name: 'registerProductIncludingTax',
      desc: '',
      args: [],
    );
  }

  /// `Size`
  String get registerProductSize {
    return Intl.message(
      'Size',
      name: 'registerProductSize',
      desc: '',
      args: [],
    );
  }

  /// `Total length x width x height`
  String get registerProductSizeDescription {
    return Intl.message(
      'Total length x width x height',
      name: 'registerProductSizeDescription',
      desc: '',
      args: [],
    );
  }

  /// `The amount of inventory to bring`
  String get registerProductInventory {
    return Intl.message(
      'The amount of inventory to bring',
      name: 'registerProductInventory',
      desc: '',
      args: [],
    );
  }

  /// `Expiration date`
  String get registerProductExpirationDate {
    return Intl.message(
      'Expiration date',
      name: 'registerProductExpirationDate',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get registerProductNote {
    return Intl.message(
      'Note',
      name: 'registerProductNote',
      desc: '',
      args: [],
    );
  }

  /// `Product details`
  String get registerProductDetails {
    return Intl.message(
      'Product details',
      name: 'registerProductDetails',
      desc: '',
      args: [],
    );
  }

  /// `Lưu ý: Chúng tôi sẽ chiếu khấu 35% trên mỗi sản phẩm`
  String get registerProductWarning {
    return Intl.message(
      'Lưu ý: Chúng tôi sẽ chiếu khấu 35% trên mỗi sản phẩm',
      name: 'registerProductWarning',
      desc: '',
      args: [],
    );
  }

  /// `Update`
  String get registerProductUpdate {
    return Intl.message(
      'Update',
      name: 'registerProductUpdate',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get registerProductContinue {
    return Intl.message(
      'Continue',
      name: 'registerProductContinue',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get registerProductCancel {
    return Intl.message(
      'Cancel',
      name: 'registerProductCancel',
      desc: '',
      args: [],
    );
  }

  /// `Add new "{text}"`
  String registerProductAddNew(Object text) {
    return Intl.message(
      'Add new "$text"',
      name: 'registerProductAddNew',
      desc: '',
      args: [text],
    );
  }

  /// `Sales price is required`
  String get registerProductValidationSalesPriceRequired {
    return Intl.message(
      'Sales price is required',
      name: 'registerProductValidationSalesPriceRequired',
      desc: '',
      args: [],
    );
  }

  /// `List price is required`
  String get registerProductValidationListPriceRequired {
    return Intl.message(
      'List price is required',
      name: 'registerProductValidationListPriceRequired',
      desc: '',
      args: [],
    );
  }

  /// `Update product photos`
  String get takePhotoProductUpdateTitle {
    return Intl.message(
      'Update product photos',
      name: 'takePhotoProductUpdateTitle',
      desc: '',
      args: [],
    );
  }

  /// `Take photos of the product`
  String get takePhotoProductTitle {
    return Intl.message(
      'Take photos of the product',
      name: 'takePhotoProductTitle',
      desc: '',
      args: [],
    );
  }

  /// `Gallery`
  String get takePhotoProductGallery {
    return Intl.message(
      'Gallery',
      name: 'takePhotoProductGallery',
      desc: '',
      args: [],
    );
  }

  /// `Camera`
  String get takePhotoProductCamera {
    return Intl.message(
      'Camera',
      name: 'takePhotoProductCamera',
      desc: '',
      args: [],
    );
  }

  /// `No images taken yet`
  String get takePhotoProductNoImages {
    return Intl.message(
      'No images taken yet',
      name: 'takePhotoProductNoImages',
      desc: '',
      args: [],
    );
  }

  /// `Update`
  String get takePhotoProductUpdate {
    return Intl.message(
      'Update',
      name: 'takePhotoProductUpdate',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get takePhotoProductConfirm {
    return Intl.message(
      'Confirm',
      name: 'takePhotoProductConfirm',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get takePhotoProductCancel {
    return Intl.message(
      'Cancel',
      name: 'takePhotoProductCancel',
      desc: '',
      args: [],
    );
  }

  /// `Please select at least one image`
  String get takePhotoProductValidationAtLeastOneImage {
    return Intl.message(
      'Please select at least one image',
      name: 'takePhotoProductValidationAtLeastOneImage',
      desc: '',
      args: [],
    );
  }

  /// `Please select at least 6 images`
  String get takePhotoProductValidationAtLeastSixImages {
    return Intl.message(
      'Please select at least 6 images',
      name: 'takePhotoProductValidationAtLeastSixImages',
      desc: '',
      args: [],
    );
  }

  /// `Failed to capture image. Please try again.`
  String get takePhotoProductErrorCaptureImage {
    return Intl.message(
      'Failed to capture image. Please try again.',
      name: 'takePhotoProductErrorCaptureImage',
      desc: '',
      args: [],
    );
  }

  /// `Failed to select images. Please try again.`
  String get takePhotoProductErrorSelectImages {
    return Intl.message(
      'Failed to select images. Please try again.',
      name: 'takePhotoProductErrorSelectImages',
      desc: '',
      args: [],
    );
  }

  /// `Update product success`
  String get takePhotoProductUpdateSuccess {
    return Intl.message(
      'Update product success',
      name: 'takePhotoProductUpdateSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Register product success`
  String get takePhotoProductRegisterSuccess {
    return Intl.message(
      'Register product success',
      name: 'takePhotoProductRegisterSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Update product`
  String get productDetailPreviewUpdateProduct {
    return Intl.message(
      'Update product',
      name: 'productDetailPreviewUpdateProduct',
      desc: '',
      args: [],
    );
  }

  /// `Publish product`
  String get productDetailPreviewPublishProduct {
    return Intl.message(
      'Publish product',
      name: 'productDetailPreviewPublishProduct',
      desc: '',
      args: [],
    );
  }

  /// `Back to edit`
  String get productDetailPreviewBackToEdit {
    return Intl.message(
      'Back to edit',
      name: 'productDetailPreviewBackToEdit',
      desc: '',
      args: [],
    );
  }

  /// `Retake photos`
  String get productDetailPreviewRetakePhotos {
    return Intl.message(
      'Retake photos',
      name: 'productDetailPreviewRetakePhotos',
      desc: '',
      args: [],
    );
  }

  /// `Report`
  String get productDetailReport {
    return Intl.message(
      'Report',
      name: 'productDetailReport',
      desc: '',
      args: [],
    );
  }

  /// `Edit product`
  String get productDetailEditProduct {
    return Intl.message(
      'Edit product',
      name: 'productDetailEditProduct',
      desc: '',
      args: [],
    );
  }

  /// `Retake photos`
  String get productDetailRetakePhotos {
    return Intl.message(
      'Retake photos',
      name: 'productDetailRetakePhotos',
      desc: '',
      args: [],
    );
  }

  /// `Cart update`
  String get productDetailCartUpdate {
    return Intl.message(
      'Cart update',
      name: 'productDetailCartUpdate',
      desc: '',
      args: [],
    );
  }

  /// `You have added products in another store, do you want to delete all products and update again?`
  String get productDetailCartUpdateMessage {
    return Intl.message(
      'You have added products in another store, do you want to delete all products and update again?',
      name: 'productDetailCartUpdateMessage',
      desc: '',
      args: [],
    );
  }

  /// `Total`
  String get productDetailTotal {
    return Intl.message(
      'Total',
      name: 'productDetailTotal',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get productDetailBack {
    return Intl.message('Back', name: 'productDetailBack', desc: '', args: []);
  }

  /// `Delete & Update new product`
  String get productDetailDeleteAndUpdate {
    return Intl.message(
      'Delete & Update new product',
      name: 'productDetailDeleteAndUpdate',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ja'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
