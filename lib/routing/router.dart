import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kitemite_app/core/common/ui/bottom_nav_screen.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/service/push_notification_service.dart';
import 'package:kitemite_app/features/events/ui/create_event_screen.dart';
import 'package:kitemite_app/features/events/ui/event_detail_screen.dart';
import 'package:kitemite_app/features/events/ui/event_screen.dart';
import 'package:kitemite_app/features/events/ui/select_location_screen.dart';
import 'package:kitemite_app/features/home/<USER>/detail_store_seller.dart';
import 'package:kitemite_app/features/home/<USER>/home_screen.dart';
import 'package:kitemite_app/features/login/ui/confirm_with_otp.dart';
import 'package:kitemite_app/features/login/ui/login_screen.dart';
import 'package:kitemite_app/features/login/ui/register_payment.dart';
import 'package:kitemite_app/features/login/ui/sign_up_screen.dart';
import 'package:kitemite_app/features/map_store/ui/detail_shop_seller/detail_shop_seller.dart';
import 'package:kitemite_app/features/map_store/ui/detail_store/detail_store_screen.dart';
import 'package:kitemite_app/features/map_store/ui/map_store_screen.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/product_detail_preview.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/register_product_screen.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/select_cabinet.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/take_photo_product.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/take_photo_product_preview.dart';
import 'package:kitemite_app/features/map_store/ui/search_location/search_location_screen.dart';
import 'package:kitemite_app/features/notification/ui/notification_detail_screen.dart';
import 'package:kitemite_app/features/notification/ui/notification_list_screen.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/product_management/ui/product_management.dart';
import 'package:kitemite_app/features/product_management/ui/update_template/detail_tempalte.dart';
import 'package:kitemite_app/features/product_management/ui/update_template/update_tempalte.dart';
import 'package:kitemite_app/features/profile/ui/profile_screen.dart';
import 'package:kitemite_app/features/profile/ui/profile_store/choose_product_seller.dart';
import 'package:kitemite_app/features/profile/ui/profile_store/choose_product_seller_result.dart';
import 'package:kitemite_app/features/profile/ui/profile_store/profile_store_screen.dart';
import 'package:kitemite_app/features/profile/ui/profile_store/register_seller_result.dart';
import 'package:kitemite_app/features/report/ui/report_screen.dart';
import 'package:kitemite_app/features/shopping_cart/ui/result_payment.dart';
import 'package:kitemite_app/features/shopping_cart/ui/result_payment_fail.dart';
import 'package:kitemite_app/features/shopping_cart/ui/shopping_cart_screen.dart';
import 'package:kitemite_app/features/shopping_cart/ui/total_payment.dart';
import 'package:kitemite_app/features/splash/ui/splash_screen.dart';
import 'package:kitemite_app/model/response/auth/user_info_model.dart';
import 'package:kitemite_app/model/response/event/event_detail_model.dart';
import 'package:kitemite_app/model/response/product/product_model.dart';
import 'package:kitemite_app/model/response/store/store_model.dart';
import 'package:kitemite_app/model/response/store/template_model.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart'
    as warehouse;
import 'package:kitemite_app/routing/router_paths.dart';

GlobalKey<NavigatorState> _key(String debugLabel) {
  return GlobalKey<NavigatorState>(debugLabel: debugLabel);
}

final GlobalKey<NavigatorState> homeNavigatorKey = _key('HomeNavigator');
final GlobalKey<NavigatorState> mapStoreNavigatorKey =
    _key('MapStoreNavigator');
final GlobalKey<NavigatorState> eventNavigatorKey = _key('EventNavigator');
final GlobalKey<NavigatorState> shoppingCartNavigatorKey =
    _key('ShoppingCartNavigator');
final GlobalKey<NavigatorState> businessNavigatorKey =
    _key('businessNavigator');

final GoRouter appRouter = GoRouter(
  navigatorKey: navigatorKey,
  debugLogDiagnostics: true,
  initialLocation: RouterPaths.splash,
  routes: [
    GoRoute(
      path: RouterPaths.splash,
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: RouterPaths.login,
      builder: (context, state) => LoginScreen(),
    ),
    GoRoute(
      path: RouterPaths.signup,
      builder: (context, state) {
        final user = state.extra as UserInfoModel;
        return SignUpScreen(user: user);
      },
    ),
    GoRoute(
      path: RouterPaths.confirmWithOtp,
      builder: (context, state) {
        final email = state.extra as String?;
        return ConfirmWithOTP(email: email ?? '');
      },
    ),
    GoRoute(
      path: RouterPaths.registerPayment,
      builder: (context, state) {
        final isOpenByPayment = state.extra as bool?;
        return RegisterPayment(isOpenByPayment: isOpenByPayment);
      },
    ),
    GoRoute(
      path: RouterPaths.profileScreen,
      builder: (context, state) => const ProfileScreen(),
    ),
    GoRoute(
      path: RouterPaths.productDetail,
      builder: (context, state) {
        final productDetailScreenArg = state.extra as ProductDetailScreenArg;
        return ProductDetailScreen(
            productDetailScreenArg: productDetailScreenArg);
      },
    ),
    GoRoute(
      path: RouterPaths.productDetailPreview,
      builder: (context, state) {
        return ProductDetailPreview(
            arg: state.extra as ProductDetailPreviewArg);
      },
    ),
    GoRoute(
      path: RouterPaths.registerSellerResult,
      builder: (context, state) => const RegisterSellerResult(),
    ),
    GoRoute(
      path: RouterPaths.chooseProductSellerResult,
      builder: (context, state) => const ChooseProductSellerResult(),
    ),
    GoRoute(
      path: RouterPaths.chooseProductSeller,
      builder: (context, state) => ChooseProductSeller(
        cabinet: state.extra as warehouse.CabinetModel,
      ),
    ),
    GoRoute(
        path: RouterPaths.detailEventScreen,
        builder: (context, state) {
          final eventId = state.extra as int?;
          return EventDetailScreen(eventId: eventId ?? 0);
        }),
    GoRoute(
        path: RouterPaths.registerSeller,
        builder: (context, state) {
          return ProfileStoreScreen(storeInfo: state.extra as StoreModel?);
        }),
    GoRoute(
      path: RouterPaths.listNotification,
      builder: (context, state) => const NotificationListScreen(),
    ),
    GoRoute(
      path: RouterPaths.createEventScreen,
      builder: (context, state) {
        final event = state.extra as EventDetailModel?;
        return CreateEventScreen(event: event);
      },
    ),
    GoRoute(
      path: RouterPaths.selectLocation,
      builder: (context, state) {
        final position = state.extra as LatLng?;
        return SelectLocationScreen(
          initialLocation: position,
        );
      },
    ),
    GoRoute(
      path: RouterPaths.notificationDetail,
      builder: (context, state) {
        final notificationID = state.extra as int?;
        return NotificationDetailScreen(notificationID: notificationID ?? 0);
      },
    ),
    GoRoute(
      path: RouterPaths.detailStore,
      builder: (context, state) {
        final args = state.extra as DetailStoreScreenArg;
        return DetailStoreScreen(args: args);
      },
    ),
    GoRoute(
      path: RouterPaths.detailStoreSeller,
      builder: (context, state) {
        final storeId = state.extra as int?;
        return DetailStoreSeller(storeId: storeId ?? 0);
      },
    ),
    GoRoute(
      path: RouterPaths.report,
      builder: (context, state) {
        final product = state.extra as ProductModel;
        return ReportScreen(product: product);
      },
    ),
    GoRoute(
      path: RouterPaths.searchLocationSearch,
      builder: (context, state) {
        final args = state.extra as SearchLocationArg;
        return SearchLocationScreen(args: args);
      },
    ),
    GoRoute(
      path: RouterPaths.selectCabinet,
      builder: (context, state) {
        final args = state.extra as StorageSelectionScreenArg;
        return StorageSelectionScreen(arg: args);
      },
    ),
    GoRoute(
      path: RouterPaths.detailShopSeller,
      builder: (context, state) {
        final args = state.extra as DetailShopSellerArg;
        return DetailShopSeller(args: args);
      },
    ),
    GoRoute(
      path: RouterPaths.registerProductScreen,
      builder: (context, state) {
        final args = state.extra as RegisterProductArg;
        return RegisterProductScreen(args: args);
      },
    ),
    GoRoute(
      path: RouterPaths.takePhotoProduct,
      builder: (context, state) {
        final args = state.extra as TakePhotoProductArg;
        return TakePhotoProduct(arg: args);
      },
    ),
    GoRoute(
      path: RouterPaths.updateTemplate,
      builder: (context, state) {
        final template = state.extra as TemplateModel?;
        return UpdateTemplate(template: template);
      },
    ),
    GoRoute(
        path: RouterPaths.homeGest,
        builder: (context, state) {
          final isLoginWithAccount = state.extra as bool?;
          return BaseScaffold(
            body: HomeScreen(
              isLoginWithAccount: isLoginWithAccount ?? false,
            ),
          );
        }),
    GoRoute(
      path: RouterPaths.detailTemplate,
      builder: (context, state) {
        final templateId = state.extra as int?;
        return DetailTemplate(templateId: templateId ?? 0);
      },
    ),
    GoRoute(
      path: RouterPaths.totalPayment,
      builder: (context, state) {
        final shopName = state.extra as String;
        return TotalPaymentScreen(
          shopName: shopName,
        );
      },
    ),
    GoRoute(
      path: RouterPaths.takePhotoProductPreview,
      builder: (context, state) {
        final args = state.extra as TakePhotoProductPreviewArg;
        return TakePhotoProductPreview(arg: args);
      },
    ),
    GoRoute(
      path: RouterPaths.resultPayment,
      builder: (context, state) => const ResultPayment(),
    ),
    GoRoute(
      path: RouterPaths.resultPaymentFail,
      builder: (context, state) => const ResultPaymentFail(),
    ),
    StatefulShellRoute.indexedStack(
      builder: (context, state, navigationShell) =>
          BottomNavScreen(navigationShell: navigationShell),
      branches: [
        StatefulShellBranch(
          navigatorKey: homeNavigatorKey,
          routes: [
            GoRoute(
                path: RouterPaths.home,
                builder: (context, state) {
                  final isLoginWithAccount = state.extra as bool?;
                  return HomeModeScreen(
                    isLoginWithAccount: isLoginWithAccount ?? false,
                  );
                }),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: mapStoreNavigatorKey,
          routes: [
            GoRoute(
              path: RouterPaths.mapStore,
              builder: (context, state) => const MapStoreScreen(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: shoppingCartNavigatorKey,
          routes: [
            GoRoute(
              path: RouterPaths.shoppingCart,
              builder: (context, state) => const ShoppingCartScreen(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: eventNavigatorKey,
          routes: [
            GoRoute(
              path: RouterPaths.eventScreen,
              builder: (context, state) => const EventScreen(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: businessNavigatorKey,
          routes: [
            GoRoute(
              path: RouterPaths.business,
              builder: (context, state) => ProductManagement(
                arg: state.extra as ProductManagementArg?,
              ),
            ),
          ],
        ),
      ],
    ),
  ],
);
