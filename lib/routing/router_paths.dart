class RouterPaths {
  static const String splash = '/splash';

  // Public
  static const String login = '/login';
  static const String signup = '/signup';
  static const String registerPayment = '/RegisterPayment';
  static const String confirmWithOtp = '/confirmWithOtp';
  // Private
  static const String home = '/home';
  static const String productDetail = '/productDetail';
  static const String mapStore = '/mapStore';
  static const String eventScreen = '/eventScreen';
  static const String profile = '/profile';
  static const String shoppingCart = '/shoppingCart';
  static const String business = '/business';
  static const String profileScreen = '/profileScreen';
  static const String registerSellerResult = '/registerSellerResult';
  static const String chooseProductSellerResult = '/chooseProductSellerResult';
  static const String chooseProductSeller = '/chooseProductSeller';
  static const String registerSeller = '/registerSeller';
  static const String listNotification = '/listNotification';
  static const String notificationDetail = '/notificationDetail';
  static const String talkerLog = '/talkerLog';
  static const String detailStore = '/detailStore';
  static const String homeGest = '/homeGest';
  static const String report = '/report';
  static const String searchLocationSearch = '/searchLocationSearch';
  static const String selectCabinet = '/selectCabinet';
  static const String detailShopSeller = '/detailShopSeller';
  static const String registerProductScreen = '/registerProductScreen';
  static const String takePhotoProduct = '/takePhotoProduct';
  static const String detailStoreSeller = '/detailStoreSeller';
  static const String createEventScreen = '/createEventScreen';
  static const String detailEventScreen = '/detailEventScreen';
  static const String selectLocation = '/selectLocation';
  static const String updateTemplate = '/updateTemplate';
  static const String detailTemplate = '/detailTemplate';
  static const String totalPayment = '/totalPayment';
  static const String productDetailPreview = '/productDetailPreview';
  static const String takePhotoProductPreview = '/takePhotoProductPreview';
  static const String resultPayment = '/resultPayment';
  static const String resultPaymentFail = '/resultPaymentFail';
}
