PODS:
  - awesome_notifications (0.10.0):
    - Flutter
    - IosAwnCore (~> 0.10.0)
  - device_info_plus (0.0.1):
    - Flutter
  - emoji_picker_flutter (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Messaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.15.0)
  - firebase_core (3.15.2):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - firebase_messaging (15.2.10):
    - Firebase/Messaging (= 11.15.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_blue_plus_darwin (0.0.2):
    - Flutter
    - FlutterMacOS
  - flutter_custom_tabs_ios (2.4.0):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - IosAwnCore (0.10.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_settings_plus (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (3.0.0):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - workmanager (0.0.1):
    - Flutter

DEPENDENCIES:
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - emoji_picker_flutter (from `.symlinks/plugins/emoji_picker_flutter/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue_plus_darwin (from `.symlinks/plugins/flutter_blue_plus_darwin/darwin`)
  - flutter_custom_tabs_ios (from `.symlinks/plugins/flutter_custom_tabs_ios/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - open_settings_plus (from `.symlinks/plugins/open_settings_plus/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - workmanager (from `.symlinks/plugins/workmanager/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Google-Maps-iOS-Utils
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - IosAwnCore
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  emoji_picker_flutter:
    :path: ".symlinks/plugins/emoji_picker_flutter/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_blue_plus_darwin:
    :path: ".symlinks/plugins/flutter_blue_plus_darwin/darwin"
  flutter_custom_tabs_ios:
    :path: ".symlinks/plugins/flutter_custom_tabs_ios/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  open_settings_plus:
    :path: ".symlinks/plugins/open_settings_plus/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"
  workmanager:
    :path: ".symlinks/plugins/workmanager/ios"

SPEC CHECKSUMS:
  awesome_notifications: 0f432b28098d193920b11a44cfa9d2d9313a3888
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  emoji_picker_flutter: ece213fc274bdddefb77d502d33080dc54e616cc
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_core: 995454a784ff288be5689b796deb9e9fa3601818
  firebase_messaging: f4a41dd102ac18b840eba3f39d67e77922d3f707
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseMessaging: 3b26e2cee503815e01c3701236b020aa9b576f09
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_blue_plus_darwin: 20a08bfeaa0f7804d524858d3d8744bcc1b6dbc3
  flutter_custom_tabs_ios: 87333f36c33a5971502766aca2f6ff4823b09bda
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  IosAwnCore: 653786a911089012092ce831f2945cd339855a89
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_settings_plus: d19f91e8a04649358a51c19b484ce2e637149d70
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  vibration: 69774ad57825b11c951ee4c46155f455d7a592ce
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  workmanager: b89e4e4445d8b57ee2fdbf1c3925696ebe5b8990

PODFILE CHECKSUM: 93923e33a70efab25bc0af48e63cbdb17beb95ff

COCOAPODS: 1.16.2
