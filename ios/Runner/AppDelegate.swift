import Flutter
import UIKit
import GoogleMaps
import workmanager

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("AIzaSyDwidKr74u2-e-pZc3yZWv277P12KREuGk")
    GeneratedPluginRegistrant.register(with: self)

    // Register for background app refresh
    if #available(iOS 13.0, *) {
      application.setMinimumBackgroundFetchInterval(UIApplication.backgroundFetchIntervalMinimum)
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Handle background app refresh
  override func application(
    _ application: UIApplication,
    performFetchWithCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
  ) {
    // This will be handled by the workmanager plugin
    completionHandler(.newData)
  }
}
